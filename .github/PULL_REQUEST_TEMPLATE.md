## Summary of the PR
<!-- Brief description of what this PR does -->

## Type of Change
- [ ] 🐛 Bug fix
- [ ] ✨ New feature
- [ ] 💥 Breaking change
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring
- [ ] ⚡ Performance improvement

## Changes Made
<!-- What was changed and why -->

## Related Issues
<!-- Link to Asana tasks or GitHub issues -->
- Closes #[issue-number]
- <PERSON>ana: [task-link]

## Testing
<!-- How was this tested? -->
- [ ] Unit tests added/updated
- [ ] Manual testing completed
- [ ] Regression testing performed

**Test Steps:**
1. 
2. 

## Screenshots (if applicable)
<!-- Before/After screenshots for UI changes -->

## Checklist
- [ ] Code follows project standards
- [ ] Self-review completed
- [ ] Documentation updated (if needed)
- [ ] No breaking changes (or documented)
- [ ] Ready for review
- [ ] Changes are backward compatible for GraphQL clients
