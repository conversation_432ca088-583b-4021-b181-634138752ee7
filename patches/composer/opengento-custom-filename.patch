--- a/Model/Export/ToCsv.php
+++ b/Model/Export/ToCsv.php
@@ -39,7 +39,8 @@
     {
         $directoryWrite = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_IMPORT_EXPORT);
         $directoryWrite->create('export');
-        $fileName = 'export/' . time() . '-categories.csv';
+        $hashed = hash('sha256', serialize($attributes));
+        $fileName = 'export/' . $hashed . '-' . time() . '-categories.csv';

         $batch = [];
         foreach ($storeIds as $storeId) {
