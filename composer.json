{"name": "comave-app/comave_magento", "description": "eCommerce Platform for Growth (Enterprise Edition)", "type": "project", "version": "2.4.7-p4", "license": "OSL-3.0", "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "php-http/discovery": true, "magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "cweagans/composer-patches": true}, "preferred-install": "dist", "sort-packages": true}, "repositories": {"artifact": {"type": "artifact", "url": "artifacts"}, "webkul_repo": {"type": "composer", "url": "https://magerepo.webkul.com/"}, "repo": {"type": "composer", "url": "https://repo.magento.com"}}, "require": {"ext-curl": "*", "bss/store-view-flags": "1.0.0", "cweagans/composer-patches": "^1.7", "diablomedia/zendframework1-filter-input": "1.0.7", "elgentos/regenerate-catalog-urls": "^0.4.5", "ethanyehuda/magento2-cronjobmanager": "^2.2", "ghoster/changecustomerpassword": "^1.0", "google/auth": "^1.42", "graycore/magento2-cors": "^2.1", "integer-net/magento2-enable-swagger": "^1.0", "jomashop/module-new-relic-monitoring-for-gql": "^1.1", "laminas/laminas-inputfilter": "^2.30", "laminas/laminas-serializer": "^2.10", "landofcoder/module-all": "1.0.*", "landofcoder/module-product-reviews": "1.0.*", "landofcoder/module-product-reviews-graphql": "1.0.*", "magento/composer-dependency-version-audit-plugin": "~0.1", "magento/composer-root-update-plugin": "^2.0.4", "magento/module-company": "^102.0", "magento/module-page-builder-product-recommendations": "^2.0", "magento/module-visual-product-recommendations": "^2.0", "magento/product-enterprise-edition": "2.4.7-p4", "magento/product-recommendations": "^6.0", "navigate/magento-2-allow-svg-webp-avif-image": "^1.0", "opengento/module-category-import-export": "^0.5.5", "printu/customerio": "~3.0", "stripe/stripe-payments": "^4.1", "symfony/translation-contracts": "^3.6", "twilio/sdk": "^8.6.2", "umc/module-crud": "^2.1", "webkul/outofstocknotification": "^5.0.4", "webkul/price-drop-alert": "^5.0"}, "replace": {"amzn/amazon-pay-and-login-magento-2-module": "*", "amzn/amazon-pay-and-login-with-amazon-core-module": "*", "amzn/amazon-pay-module": "*", "amzn/amazon-pay-sdk-php": "*", "amzn/login-with-amazon-module": "*", "magento/module-negotiable-quote": "*", "magento/module-inventory-in-store-pickup": "*", "magento/module-inventory-in-store-pickup-api": "*", "magento/module-inventory-in-store-pickup-frontend": "*", "magento/module-inventory-in-store-pickup-multishipping": "*", "magento/module-inventory-in-store-pickup-quote": "*", "magento/module-inventory-in-store-pickup-sales": "*", "magento/module-inventory-in-store-pickup-shipping": "*", "magento/module-inventory-in-store-pickup-admin-ui": "*", "magento/module-inventory-in-store-pickup-sales-api": "*", "magento/module-inventory-in-store-pickup-shipping-api": "*", "magento/module-inventory-in-store-pickup-webapi-extension": "*", "magento/module-inventory-in-store-pickup-quote-graph-ql": "*", "magento/module-inventory-in-store-pickup-sales-admin-ui": "*", "magento/module-inventory-in-store-pickup-shipping-admin-ui": "*", "magento/module-inventory-in-store-pickup-graph-ql": "*", "magento/module-inventory-requisition-list": "*", "magento/module-marketplace": "*", "klarna/m2-payments": "*", "klarna/module-core": "*", "klarna/module-kp": "*", "klarna/module-kp-graph-ql": "*", "klarna/module-ordermanagement": "*", "klarna/module-onsitemessaging": "*"}, "suggest": {"ext-pcntl": "Need for run processes in parallel mode"}, "conflict": {"gene/bluefoot": "*"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "extra": {"component_paths": {"trentrichardson/jquery-timepicker-addon": "lib/web/jquery/jquery-ui-timepicker-addon.js", "components/jquery": ["lib/web/jquery.js", "lib/web/jquery/jquery.min.js", "lib/web/jquery/jquery-migrate.js"], "blueimp/jquery-file-upload": "lib/web/jquery/fileUploader", "components/jqueryui": ["lib/web/jquery/jquery-ui.js"], "twbs/bootstrap": ["lib/web/jquery/jquery.tabs.js"], "tinymce/tinymce": "lib/web/tiny_mce_5"}, "magento-force": "override", "composer-exit-on-patch-failure": true, "patches": {"opengento/module-category-import-export": {"custom filename export": "patches/composer/opengento-custom-filename.patch"}, "magento/theme-adminhtml-backend": {"Magento 2 issue #39371": "patches/composer/grid-actions-overlapping.patch"}, "magento/theme-adminhtml-spectrum": {"Magento 2 issue #39371": "patches/composer/grid-actions-overlapping-spectrum.patch"}, "magento/module-catalog": {"category tree builder public": "patches/composer/category-tree-public.patch", "Admin product grid allow empty name attribute": "patches/composer/fix_product_empty_name_grid.patch"}, "magento/module-config": {"ignore file validation if media file is missing and reset config": "patches/composer/file-validation.patch"}, "landofcoder/module-product-reviews": {"Fix function signature to allow guest review add if config enabled in admin": "patches/composer/fix_lof_guest_review_graphql_func_signature.patch", "Fix dynamix property GetListReply": "patches/composer/lof_product_review_bugfix_get_list_reply_retry.patch", "Fix dynamic property": "patches/composer/landofcoder--module-product-reviews-1.0.10.patch"}, "magento/module-url-rewrite": {"debug error in deploy rewrite": "patches/composer/debug-runtime-storage.patch"}, "magento/module-quote-graph-ql": {"debug error in deploy rewrite": "patches/composer/allow_null_return_applied_to_discount_query.patch"}, "magento/module-deploy": {"[PATCH] #38682 - Github Fix DeployPackage type error": "patches/composer/github-issue-38682.patch"}, "webkul/outofstocknotification": {"Changed join condition on row_id from deprecated entity_id": "patches/composer/collection_join_on_row_id_fix_16102024.patch", "Fixed BUG in notifyMe mutation throws error": "patches/composer/fix-notifyme-mutation-error-20241209.patch", "Fixed BUG in unsubscribe mutation": "patches/composer/fix-oos-unsubscribe-mutation-20241211.patch", "Updated unsubscribe mutation schema": "patches/composer/updated-oos-unsubscribe-mutation-schema.patch", "Updated subscribe and unsubscribe mutation resolvers to include email field check based on the context": "patches/composer/updated-oos-subscribe-and-unsubscribe-mutations-v2.patch"}, "magento/module-sales-graph-ql": {"[PATCH] #38900 - Github Fix GraphQL customer orders query error when order item with deleted product v2": "patches/composer/github-issue-38900-v2.patch", "Order totals tax title returning null when graphql requires it": "patches/composer/order-total-resolver-fix.patch"}, "magento/module-customer": {"fix type hinting": "patches/composer/fix-auth-token-param.patch"}, "magento/module-customer-graph-ql": {"fix attribute hinting": "patches/composer/customer-graphql-attribute.patch"}, "magento/module-sales": {"fix pdf output mark payment block": "patches/composer/pdf-block-mark.patch", "fix admin re order functionality": "patches/composer/admin-reorder-fix.patch"}, "stripe/module-payments": {"fix pdf output mark template block": "patches/composer/pdf-template-mark.patch", "fix pdf output mark template block flow 2": "patches/composer/pdf-template2-mark.patch"}, "magento/module-inventory-catalog": {"[PATCH] #37450 - Reindex cataloginventory_stock does not update cataloginventory_stock_status": "patches/composer/stock-37450-issue.patch"}, "magento/framework": {"Fix imports on empty / not found files": "patches/composer/import-deploy.patch", "Set SameSite to Strict for session cookies": "patches/composer/set-same-site-strict.patch"}, "stripe/module-tax": {"[PATCH] Fix Stripe Tax Caching false response": "patches/composer/fix-stripe-tax-caching-false-response.patch"}, "magento/module-catalog-graph-ql": {"Fix undefined array keys attribute-type-and-attribute-is-filterable": "patches/composer/fix-undefined-array-keys-attribute-type-and-attribute-is-filterable-in-attribute-options25032025.patch"}, "magento/module-user": {"[HOTFIX] Magento admin user is not able to reset password": "patches/composer/fix-magento-module-user-resetforgottenpassword-blank-form.patch"}, "magento/module-graph-ql": {"Fix GraphQL controller to prevent errors when POST data is not an array": "patches/composer/handle_misformated_graphql_request.patch"}, "magento/module-page-cache": {"Fix form key provider to use domain in cookie": "patches/composer/front-generated-formkey-domain.patch"}, "magento/module-rma-graph-ql": {"verbose logging graphql": "patches/composer/rma-verbose.patch", "rma item graphql fix": "patches/composer/rma-custom-attribute-option-fix.patch"}, "magento/module-login-as-customer-frontend-ui": {"Fix Login as Customer frontend UI to work with Magento 2.4.7-p4": "patches/composer/admin-prefix-login-as-customer.patch"}}}, "scripts": {}, "require-dev": {"allure-framework/allure-phpunit": "^3.0", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "dg/bypass-finals": "^1.9", "friendsofphp/php-cs-fixer": "^3.68", "lusitanian/oauth": "^0.8.11", "mage2tv/magento-cache-clean": "^1.0", "magento/magento-coding-standard": "^36.0", "pdepend/pdepend": "^2.16", "phpmd/phpmd": "^2.15", "phpmetrics/phpmetrics": "^0.0.1", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^10.5", "sebastian/phpcpd": "^2.0", "symfony/finder": "^7.2"}}