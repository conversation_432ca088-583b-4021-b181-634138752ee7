<?php
declare(strict_types=1);
/**
 * @category    WeltPixel
 * @package     WeltPixel_EnhancedEmail
 * @copyright   Copyright (c) 2018 Weltpixel
 * <AUTHOR> Attila @ Weltpixel TEAM
 */

namespace WeltPixel\EnhancedEmail\Setup;

use Magento\Cms\Model\BlockFactory;
use Magento\Email\Model\ResourceModel\Template\CollectionFactory;
use Magento\Email\Model\Template;
use Magento\Email\Model\Template\Config;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\App\ProductMetadataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;

/**
 * Class UpgradeData
 * @package WeltPixel\EnhancedEmail\Setup
 *
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var BlockFactory
     */
    private $blockFactory;

    /**
     * @var WriterInterface
     */
    private $configWriter;


    /**
     * @var Config
     */
    protected $_emailConfig;

    /**
     * @var CollectionFactory
     */
    protected $_collectionFactory;

    /**
     * @var ProductMetadataInterface
     */
    protected $productMetadata;

    /**
     * @var array|array[]
     */
    protected $_templates = [];

    protected $_template;

    protected $magentoVersion;

    public function __construct(
        BlockFactory $blockFactory,
        WriterInterface $configWriter,
        Config $emailConfig,
        CollectionFactory $collectionFactory,
        Template $template,
        ProductMetadataInterface $productMetadata
    ) {
        $this->blockFactory = $blockFactory;
        $this->configWriter = $configWriter;
        $this->_emailConfig = $emailConfig;
        $this->_collectionFactory = $collectionFactory;
        $this->_template = $template;
        $this->productMetadata = $productMetadata;
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface $context
     * @throws \Exception
     */
    public function upgrade(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $setup->startSetup();

        $this->magentoVersion = $this->productMetadata->getVersion();

        if (version_compare($context->getVersion(), "1.0.1", "<")) {
            $content = <<<EOT
                <a style="padding-left: 30px" href="http://facebook.com/#"><img src="{{view url='WeltPixel_EnhancedEmail/images/fb.png'}}" alt="fb" width="15" height="15" /></a>
                <a style="padding-left: 30px" href="http://twitter.com/#"><img src="{{view url='WeltPixel_EnhancedEmail/images/twitter.png'}}" alt="twitter" width="15" height="15" /></a>
                <a style="padding-left: 30px" href="http://instagram.com/#"><img src="{{view url='WeltPixel_EnhancedEmail/images/instagram.png'}}" alt="instagram" width="15" height="15" /></a>
                <a style="padding-left: 30px" href="http://youtube.com/#"><img src="{{view url='WeltPixel_EnhancedEmail/images/youtube.png'}}" alt="youtube" width="15" height="15" /></a>
EOT;

            // social media block
            $cmsBlockData = [
                'title' => 'EnhancedEmail Social Media Block',
                'identifier' => 'weltpixel_social_media_email_block',
                'content' => $content,
                'is_active' => 1,
                'stores' => [0],
                'sort_order' => 0
            ];

            try {
                $this->blockFactory->create()->setData($cmsBlockData)->save();
            } catch (\Exception $ex) {
            }

            // custom block
            $cmsCustomBlockData = [
                'title' => 'EnhancedEmail Custom Block',
                'identifier' => 'weltpixel_custom_block_1',
                'content' => "<h3>Enhanced Email custom block content.</h3>",
                'is_active' => 1,
                'stores' => [0],
                'sort_order' => 0
            ];

            try {
                $this->blockFactory->create()->setData($cmsCustomBlockData)->save();
            } catch (\Exception $ex) {
            }
        }

        if (version_compare($context->getVersion(), "1.0.3", "<")) {
            $content = <<<EOT
<p><strong>RETURNS</strong>: Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
EOT;

            $cmsCustomBlockData = [
                'title' => 'EnhancedEmail Custom Block - Returns',
                'identifier' => 'weltpixel_custom_block_returns',
                'content' => $content,
                'is_active' => 1,
                'stores' => [0],
                'sort_order' => 0
            ];

            try {
                $this->blockFactory->create()->setData($cmsCustomBlockData)->save();
            } catch (\Exception $ex) {
            }
        }

        $setup->endSetup();
    }
}
