<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="marketplace_styles"/>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Manage Shipping Methods</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
            <block class="Magento\Framework\View\Element\Template" name="sellership_rate_manage" template="Coditron_CustomShippingRate::shiprate/list.phtml" cacheable="false"></block>
        </referenceContainer>
        <referenceContainer name="sellership_rate_manage">
            <uiComponent name="sellership_rates_list_front"/>
        </referenceContainer>
    </body>
</page>
