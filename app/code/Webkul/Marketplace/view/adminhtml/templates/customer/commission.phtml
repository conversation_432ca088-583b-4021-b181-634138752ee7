<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<?php
    $commissionValue = $block->getCommission();
    // $currencySymbol = $block->getCurrencySymbol();
?>
<div class="admin__fieldset-wrapper opened">
    <div class="admin__fieldset-wrapper-content">
        <fieldset class="admin__fieldset">
            <table class="admin__table-secondary">
                <tbody>
                    <tr>
                        <th><?= $block->escapeHtml(__('Total Sale'));?></th>
                        <td><?= $block->escapeHtml($block->getCurrencyPrice($commissionValue['total_sale'])); ?></td>
                    </tr>
                    <tr>
                        <th><?= $block->escapeHtml(__('Total Seller Sale')); ?></th>
                        <td><?= $block->escapeHtml($block
                        ->getCurrencyPrice($commissionValue['actual_seller_amt'])); ?></td>
                    </tr>
                    <tr>
                        <th><?= $block->escapeHtml(__('Total Admin Sale')) ?></th>
                        <td><?= $block->escapeHtml($block->getCurrencyPrice($commissionValue['total_comm'])); ?></td>
                    </tr>
                    <tr>
                        <th><?= $block->escapeHtml(__('Current Commission Type')) ?></th>
                        <td><?= $block->escapeHtml(ucfirst($commissionValue['commission_type'])); ?></td>
                    </tr>
                    <?php if ($commissionValue['commission_type'] === 'percentage'): ?>
                    <tr>
                        <th><?= $block->escapeHtml(__('Current Commission %')) ?></th>
                        <td><?= $block->escapeHtml($commissionValue['current_val']); ?>%</td>
                    </tr>
                    <?php else: ?>
                    <tr>
                        <th><?= $block->escapeHtml(__('Current Fixed Commission Amount')) ?></th>
                        <td>
                            <?= $block->escapeHtml($block->getCurrencyPrice($commissionValue['commission_fixed_amount'])); ?> 
                            <small style="color: #666;">(Base: <?= $block->escapeHtml(number_format($commissionValue['commission_fixed_amount'], 2)); ?> <?= $block->escapeHtml($block->getBaseCurrencyCode()); ?>)</small>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </fieldset>
    </div>
</div>
