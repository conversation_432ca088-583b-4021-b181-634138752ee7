<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Webkul\Marketplace\Block\Adminhtml\Customer\Edit;

use Magento\Customer\Controller\RegistryConstants;
use Magento\Ui\Component\Layout\Tabs\TabInterface;
use Magento\Backend\Block\Widget\Form;
use Magento\Backend\Block\Widget\Form\Generic;
use Magento\Directory\Model\ResourceModel\Country\Collection as CountryModel;

/**
 * Customer account form block.
 */
class CommissionTab extends Generic implements TabInterface
{
    /**
     * @var \Magento\Store\Model\System\Store
     */
    protected $_systemStore;

    /**
     *
     * @var string|null
     */
    protected $_dob = null;

    /**
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry;

    /**
     * @var CountryModel
     */
    protected $_country;

    /**
     * Construct
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Data\FormFactory $formFactory
     * @param \Magento\Store\Model\System\Store $systemStore
     * @param CountryModel $country
     * @param \Webkul\Marketplace\Block\Adminhtml\Customer\Edit $customerEdit
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Magento\Store\Model\System\Store $systemStore,
        CountryModel $country,
        \Webkul\Marketplace\Block\Adminhtml\Customer\Edit $customerEdit,
        array $data = []
    ) {
        $this->_coreRegistry = $registry;
        $this->_systemStore = $systemStore;
        $this->customerEdit = $customerEdit;
        $this->_country = $country;
        parent::__construct($context, $registry, $formFactory, $data);
    }

    /**
     * Get customer id
     *
     * @return string|null
     */
    public function getCustomerId()
    {
        return $this->_coreRegistry->registry(
            RegistryConstants::CURRENT_CUSTOMER_ID
        );
    }

    /**
     * Get tab label
     *
     * @return \Magento\Framework\Phrase
     */
    public function getTabLabel()
    {
        return __('Commission');
    }

    /**
     * Get tab title
     *
     * @return \Magento\Framework\Phrase
     */
    public function getTabTitle()
    {
        return __('Commission');
    }

    /**
     * Get seller status
     *
     * @return bool
     */
    protected function getSellerStatus()
    {
        $coll = $this->customerEdit->getMarketplaceUserCollection();
        $isSeller = false;
        foreach ($coll as $row) {
            $isSeller = $row->getIsSeller();
        }
        if ($this->getCustomerId() && $isSeller) {
            return true;
        }

        return false;
    }

    /**
     * Can show tab
     *
     * @return bool
     */
    public function canShowTab()
    {
        return $this->getSellerStatus();
    }

    /**
     * Get is isHidde
     *
     * @return bool
     */
    public function isHidden()
    {
        return $this->getSellerStatus();
    }

    /**
     * Tab class getter.
     *
     * @return string
     */
    public function getTabClass()
    {
        return '';
    }

    /**
     * Return URL link to Tab content.
     *
     * @return string
     */
    public function getTabUrl()
    {
        return '';
    }

    /**
     * Tab should be loaded trough Ajax call.
     *
     * @return bool
     */
    public function isAjaxLoaded()
    {
        return false;
    }

    /**
     * Init form
     *
     * @return void
     */
    public function initForm()
    {
        if (!$this->canShowTab()) {
            return $this;
        }
        /**@var \Magento\Framework\Data\Form $form */
        $form = $this->_formFactory->create();
        $form->setHtmlIdPrefix('marketplace_');
        $customerId = $this->_coreRegistry->registry(
            RegistryConstants::CURRENT_CUSTOMER_ID
        );
        $storeid = $this->_storeManager->getStore()->getId();

        $fieldset = $form->addFieldset(
            'base_fieldset',
            ['legend' => __('Commission Details')]
        );

        $rowcom = $this->customerEdit
                        ->getSalesPartnerCollection()
                        ->addFieldToFilter(
                            'commission_status',
                            1
                        )
                        ->addFieldToSelect(
                            'commission_rate'
                        )
                        ->addFieldToSelect(
                            'commission_type'
                        )
                        ->addFieldToSelect(
                            'commission_fixed_amount'
                        )
                        ->getFirstItem();
        
        $commissionRate = $rowcom->getCommissionRate();
        $commissionType = $rowcom->getCommissionType() ?: 'percentage';
        $commissionFixedAmount = $rowcom->getCommissionFixedAmount() ?: 0;
        
        if ($commissionRate === null) {
            $commissionRate = $this->customerEdit->getConfigCommissionRate();
        }
        $fieldset->addField(
            'commission_enable',
            'checkbox',
            [
                'name' => 'commission_enable',
                'data-form-part' => $this->getData('target_form'),
                'label' => __('Change Commission'),
                'title' => __('Change Commission'),
                'onchange' => 'this.value = this.checked;',
                'after_element_html' => "<script>
                require([
                    'jquery'
                ], function($){
                    $('#marketplace_commission_enable').on('change', function () {
                        if (this.checked === true) {
                            $('#marketplace_commission_type').removeAttr('disabled');
                            var commissionType = $('#marketplace_commission_type').val();
                            if (commissionType === 'percentage') {
                                $('#marketplace_commission').removeAttr('disabled');
                                $('#marketplace_commission_fixed_amount').attr('disabled', 'disabled');
                            } else {
                                $('#marketplace_commission').attr('disabled', 'disabled');
                                $('#marketplace_commission_fixed_amount').removeAttr('disabled');
                            }
                        } else {
                            $('#marketplace_commission_type').attr('disabled', 'disabled');
                            $('#marketplace_commission').attr('disabled', 'disabled');
                            $('#marketplace_commission_fixed_amount').attr('disabled', 'disabled');
                        }
                    });
                    
                    $('#marketplace_commission_type').on('change', function () {
                        var isEnabled = $('#marketplace_commission_enable').is(':checked');
                        if (!isEnabled) return;
                        
                        if (this.value === 'percentage') {
                            $('#marketplace_commission').removeAttr('disabled');
                            $('#marketplace_commission_fixed_amount').attr('disabled', 'disabled');
                        } else {
                            $('#marketplace_commission').attr('disabled', 'disabled');
                            $('#marketplace_commission_fixed_amount').removeAttr('disabled');
                        }
                    });
                });
                </script>"
            ]
        );
        
        $fieldset->addField(
            'commission_type',
            'select',
            [
                'name' => 'commission_type',
                'data-form-part' => $this->getData('target_form'),
                'label' => __('Commission Type'),
                'title' => __('Commission Type'),
                'values' => [
                    ['value' => 'percentage', 'label' => __('Percentage')],
                    ['value' => 'fixed', 'label' => __('Fixed Amount')]
                ],
                'value' => $commissionType,
                'disabled' => 'disabled'
            ]
        );
        $fieldset->addField(
            'commission',
            'text',
            [
                'name' => 'commission',
                'data-form-part' => $this->getData('target_form'),
                'label' => __('Set Commission In Percentage For This Seller'),
                'title' => __('Set Commission In Percentage For This Seller'),
                'class' => 'validate-number validate-digits-range digits-range-0-100',
                'disabled' => 'disabled',
                'value' => $commissionRate,
                'note' => __('Enter percentage value (0-100)')
            ]
        );
        
        $baseCurrency = $this->customerEdit->getBaseCurrencyCode();
        $fieldset->addField(
            'commission_fixed_amount',
            'text',
            [
                'name' => 'commission_fixed_amount',
                'data-form-part' => $this->getData('target_form'),
                'label' => __('Set Fixed Commission Amount Per Product (%1)', $baseCurrency),
                'title' => __('Set Fixed Commission Amount Per Product (%1)', $baseCurrency),
                'class' => 'validate-number validate-zero-or-greater',
                'disabled' => 'disabled',
                'value' => $commissionFixedAmount,
                'note' => __('Enter fixed amount per product in %1 (base currency)', $baseCurrency)
            ]
        );
        $this->setForm($form);

        return $this;
    }

    /**
     * To html
     *
     * @return string
     */
    protected function _toHtml()
    {
        if ($this->canShowTab()) {
            $this->initForm();

            return parent::_toHtml();
        } else {
            return '';
        }
    }

    /**
     * Prepare the layout.
     *
     * @return $this
     */
    public function getFormHtml()
    {
        $html = parent::getFormHtml();
        $html .= $this->getLayout()->createBlock(
            \Webkul\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Commission::class
        )->toHtml();

        return $html;
    }
}
