<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\SellerOnboarding\Controller\Adminhtml\Seller;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller\Collection;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;

/**
 * Controller to search seller name for the ui-select component
 */
class Search extends Action implements HttpGetActionInterface
{
    const string ADMIN_RESOURCE = 'Comave_SellerOnboarding::category_source_list';

    /**
     * @param \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory
     * @param \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory $sellerCollectionFactory
     * @param \Magento\Backend\App\Action\Context $context
     */
    public function __construct(
        private readonly JsonFactory $resultJsonFactory,
        private readonly CollectionFactory $sellerCollectionFactory,
        Context $context
    ) {
        parent::__construct($context);
    }

    /**
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute(): ResultInterface
    {
        $searchKey = $this->getRequest()->getParam('searchKey');
        if (empty($searchKey)) {
            $resultJson = $this->resultJsonFactory->create();

            return $resultJson->setData([
                'options' => [],
                'total' => 0,
            ]);
        }

        $sellerById = [];
        $pageNum = (int)$this->getRequest()->getParam('page');
        $limit = (int)$this->getRequest()->getParam('limit');

        $sellerCollection = $this->sellerCollectionFactory->create();
        $this->prepareCollection($sellerCollection);
        $sellerCollection->addFieldToFilter('cgf.name', [
            ['like' => "%{$searchKey}%"],
            ['like' => "%{$searchKey}"],
            ['like' => "{$searchKey}%"],
        ]);

        $sellerCollection->setCurPage($pageNum);
        $sellerCollection->setPageSize($limit);
        $totalValues = $sellerCollection->getSize();

        foreach ($sellerCollection as $seller) {
            $sellerId = $seller->getSellerId();
            $sellerById[$sellerId] = [
                'value' => $sellerId,
                'label' => $seller->getName(),
                'is_active' => $seller->getIsSeller(),
                'path' => $seller->getShopUrl(),
                'optgroup' => false,
            ];
        }
        $resultJson = $this->resultJsonFactory->create();

        return $resultJson->setData([
            'options' => $sellerById,
            'total' => empty($sellerById) ? 0 : $totalValues,
        ]);
    }

    /**
     * @param \Webkul\Marketplace\Model\ResourceModel\Seller\Collection $sellerCollection
     * @return void
     */
    private function prepareCollection(Collection $sellerCollection): void
    {
        $joinTable = $sellerCollection->getTable('customer_grid_flat');
        $sellerCollection->getSelect()->join(
            $joinTable.' as cgf',
            'main_table.seller_id = cgf.entity_id',
            [
                'name' => 'name',
                'email' => 'email',
            ]
        );
        $flagsTable = $sellerCollection->getTable('marketplace_sellerflags');
        $sellerCollection->getSelect()->joinLeft(
            $flagsTable.' as flagTable',
            'main_table.seller_id = flagTable.seller_id',
            [
                'flagcount' => 'count(flagTable.entity_id)',
            ]
        )->group("main_table.seller_id");
    }
}
