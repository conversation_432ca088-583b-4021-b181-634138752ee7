<?php
declare(strict_types=1);

namespace Comave\CustomerIoEmail\Console;

use Magento\Framework\App\State;
use Symfony\Component\Console\Command\Command;
use Magento\Framework\Console\Cli;
use Magento\Framework\App\Area;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Comave\CustomerIoEmail\Service\MapCustomerIoTemplateCodesService;
use Psr\Log\LoggerInterface;

class MapCustomerIoTemplateCodes extends Command
{
    public function __construct(
        private readonly State $appState,
        private readonly MapCustomerIoTemplateCodesService $mapCustomerIoTemplateCodesService,
        private readonly LoggerInterface $logger,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('comave:email-template:map-customer-io-template-codes')
            ->setDescription('Map customer io template codes');
        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->appState->setAreaCode(Area::AREA_ADMINHTML);
        try {
            $this->mapCustomerIoTemplateCodesService->mapCustomerIoTemplateCodes();
            return Cli::RETURN_SUCCESS;
        } catch (\Exception $exception) {
            $output->write('Failed to map Customer.io template codes.');
            $this->logger->error('Error while mapping Customer.io template codes: ' . $exception->getMessage());
            return Cli::RETURN_FAILURE;
        }
    }
}
