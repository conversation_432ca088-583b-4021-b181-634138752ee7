<?php
declare(strict_types=1);

namespace Comave\CustomerIoEmail\Plugin;

use Magento\Framework\Convert\DataObject;
use Comave\CustomerIoEmail\Service\PayloadService;

class EmailSender
{

    /**
     * Construct function
     *
     * @param \Comave\CustomerIoEmail\Config\CustomerIoProvider $customerIoConfig
     * @param \Magento\Email\Model\TemplateFactory $templateFactory
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\CustomerIoEmail\Mail\Template\CustomTransportBuilder $customTransportBuilder
     */
    public function __construct(
        private readonly \Comave\CustomerIoEmail\Config\CustomerIoProvider $customerIoConfig,
        private readonly \Magento\Email\Model\TemplateFactory $templateFactory,
        private readonly \Psr\Log\LoggerInterface $logger,
        private readonly \Comave\CustomerIoEmail\Mail\Template\CustomTransportBuilder $customTransportBuilder,
        private readonly DataObject $dataObject,
        private readonly PayloadService $payloadService
    ) {
    }

    /**
     * Around plugin for getTransport method
     *
     * @param \Magento\Framework\Mail\Template\TransportBuilder $subject
     * @param \Closure $proceed
     * @return \Magento\Framework\Mail\TransportInterface | \Comave\CustomerIoEmail\Mail\Template\CustomTransportBuilder
     */
    public function aroundGetTransport(
        \Magento\Framework\Mail\Template\TransportBuilder $subject,
        \Closure $proceed
    ): \Magento\Framework\Mail\TransportInterface | \Comave\CustomerIoEmail\Mail\Template\CustomTransportBuilder {
        if (!$this->customerIoConfig->isEnabled()) {
            return $proceed();
        }

        try {
            $template = $this->getTemplateById($subject->getTemplateId());

            if (!$this->shouldUseCustomerIoForTemplate($template, $subject)) {
                return $proceed();
            }

            return $this->createCustomTransport($subject, $template);
        } catch (\Exception $e) {
            $this->logger->error($e);

            return $proceed();
        }
    }

    /**
     * Check if the template should use customer.io as defined in the admin settings function
     *
     * @param \Magento\Email\Model\Template $template
     * @param \Magento\Framework\Mail\Template\TransportBuilder $subject
     * @return bool
     */
    protected function shouldUseCustomerIoForTemplate(
        \Magento\Email\Model\Template $template,
        \Magento\Framework\Mail\Template\TransportBuilder $subject
    ): bool {
        $templateId = $subject->getTemplateId();
        $templateCode = is_numeric($templateId) ? $template->getOrigTemplateCode() : $templateId;

        return in_array($templateCode, $this->customerIoConfig->getTemplateIds());
    }

    /**
     * Use custom transport builder for Customer.io function
     *
     * @param \Magento\Framework\Mail\Template\TransportBuilder $subject
     * @param \Magento\Email\Model\Template $template
     * @return \Comave\CustomerIoEmail\Mail\Template\CustomTransportBuilder
     */
    private function createCustomTransport(
        \Magento\Framework\Mail\Template\TransportBuilder $subject,
        \Magento\Email\Model\Template $template
    ): \Comave\CustomerIoEmail\Mail\Template\CustomTransportBuilder {
        $customTransportBuilder = $this->customTransportBuilder;
        $customTransportBuilder->setCustomerIoData(
            $this->getCustomerIoDataByTemplate(
                $subject,
                $template,
                $this->customerIoConfig->getDebugEmail()
            )
        );

        return $customTransportBuilder;
    }

    /**
     * Get formatted email data function
     *
     * @param \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder
     * @param \Magento\Email\Model\Template $template
     * @param string $debugEmail
     * @return mixed[]
     * @throws \InvalidArgumentException
     */
    private function getCustomerIoDataByTemplate(
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Magento\Email\Model\Template $template,
        string $debugEmail = ''
    ): array {
        $fromEmails = $transportBuilder->getFromEmails();
        $toEmails = $transportBuilder->getToEmails();
        $templateVars = $transportBuilder->getTemplateVars();

        if (!empty($debugEmail)) {
            $toEmails = [$debugEmail];
            $identifiersEmail = $debugEmail;
        } else {
            $identifiersEmail = implode(', ', $toEmails);
        }

        $data = $this->dataObject->convertDataToArray($templateVars);
        $data = $this->payloadService->cleanPayload($data);
        $data = $this->payloadService->addBackendUrls($data);
        $data = $this->payloadService->addFrontendUrls($data);

        return [
            'transactional_message_id' => $template->getTemplateCode(),
            'message_data' => ['data' => $data],
            'from' => $fromEmails,
            'identifiers' => ['email' => $identifiersEmail],
            'to' => implode(', ', $toEmails),
        ];
    }

    /**
     * Get template code by template id function
     *
     * @param string $templateId
     * @return \Magento\Email\Model\Template
     */
    private function getTemplateById(string $templateId): \Magento\Email\Model\Template
    {
        return $this->templateFactory->create()->load($templateId);
    }
}
