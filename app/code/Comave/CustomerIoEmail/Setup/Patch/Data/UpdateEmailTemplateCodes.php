<?php

declare(strict_types=1);

namespace Comave\CustomerIoEmail\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Psr\Log\LoggerInterface;

class UpdateEmailTemplateCodes implements DataPatchInterface
{

    const array EMAIL_TEMPLATE_CODE_MAP = [
        'customer_new_account_confirmation_weltpixel' => 'account_created',
        'new_order_weltpixel' => 'order_confirmed',
        'customer_password_forgot_email_template' => 'password_reset',
        'new_shipment_weltpixel' => 'order_shipped',
        'shipment_update_weltpixel' => 'order_delivered'
    ];

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param DirReader $dirReader
     * @param IoFile $ioFile
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly DirReader $dirReader,
        private readonly IoFile $ioFile,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        foreach (self::EMAIL_TEMPLATE_CODE_MAP as $orgTemplateCode => $templateCode) {
            try {
                $this->moduleDataSetup->getConnection()->update(
                    $this->moduleDataSetup->getTable('email_template'),
                    ['template_code' => $templateCode],
                    ['orig_template_code = ?' => $orgTemplateCode]
                );
            } catch (CouldNotSaveException $e) {
                $this->logger->error('Failed to update the email template ' . $e->getMessage());
            }
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
