<?php

declare(strict_types=1);

namespace Comave\CustomerIoEmail\Mail\Template;

use Comave\CustomerIoEmail\Service\Client;
use Magento\Framework\Exception\MailException;
use Magento\Framework\Phrase;
use Psr\Log\LoggerInterface;

class CustomTransportBuilder
{
    /**
     * @var mixed[]
     */
    private array $customerIoData;

    /**
     * @param LoggerInterface $logger
     * @param Client $clientService
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly Client $clientService
    ) {}

    /**
     * Set the customer.io data for sending the email function
     *
     * @param mixed[] $data
     * @return void
     */
    public function setCustomerIoData(array $data): void
    {
        $this->customerIoData = $data;
    }

    /**
     * Send the email function
     *
     * @return void
     * @throws \Magento\Framework\Exception\MailException
     */
    public function sendMessage(): void
    {
        try {
            $client = $this->clientService->getClient();
            $client->send->email($this->customerIoData);
        } catch (\Exception $e) {
            $this->logger->error('Unable to send mail using customer.io. Please try again later. ' . $e->getMessage());
            throw new MailException(new Phrase('Unable to send mail using customer.io. Please try again later.'));
        }
    }
}
