<?php
declare(strict_types=1);

namespace Comave\CustomerIoEmail\Service;

use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class MapCustomerIoTemplateCodesService
{
    private const array EMAIL_TEMPLATE_CODE_MAPPINGS = [
        'customer_new_account_confirmed_weltpixel' => 'email_confirmation',
        'customer_new_account_confirmation_weltpixel' => 'account_created',
        'new_shipment_weltpixel' => 'order_shipped',
        'new_order_weltpixel' => 'order_confirmed',
        'customer_password_forgot_email_template' => 'password_reset',
        'shipment_update_weltpixel' => 'order_delivered'
    ];

    /**
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return void
     */
    public function mapCustomerIoTemplateCodes(): void
    {
        $connection = $this->resourceConnection->getConnection('write');

        foreach (self::EMAIL_TEMPLATE_CODE_MAPPINGS as $magentoTemplateCode => $customerIotemplateCode) {
            try {
                $connection->update(
                    $connection->getTableName('email_template'),
                    ['template_code' => $customerIotemplateCode],
                    ['orig_template_code = ?' => $magentoTemplateCode]
                );
            } catch (\Throwable $exception) {
                $this->logger->error(
                    sprintf(
                        'Failed to update template code from "%s" to "%s": %s',
                        $magentoTemplateCode,
                        $customerIotemplateCode,
                        $exception->getMessage()
                    )
                );
            }
        }
    }
}
