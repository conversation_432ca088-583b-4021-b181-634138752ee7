<?php

declare(strict_types=1);

namespace Comave\CustomerIoEmail\Service;

use Comave\EmailConfig\Model\Config\Config;
use Magento\Framework\UrlInterface;
use \Magento\Store\Model\StoreManagerInterface;

class PayloadService
{

    //paths to try and remove from magento 2 data objects so that we do not send sensitive data to customer IO
    // ex. customer/address/billing will navigate throw the customer io data array first in the customer key, then address key and will remove the billing key
    const array BLOCKED_KEYS = [
        'customer/rp_token',
        'customer/password_hash',
        'customer/addresses',
        'customer/custom_attributes'
    ];

    /**
     * @param Config $config
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private readonly Config $config,
        private readonly StoreManagerInterface $storeManager
    ) {
    }

    /**
     * @param array $data
     * @return array
     */
    public function addFrontendUrls(array $data): array
    {
        $data['frontend_base_secure_url'] = $this->config->getFrontendSecureUrl();
        $data['frontend_base_unsecured_url'] = $this->config->getFrontendUnsecureUrl();
        return $data;
    }

    /**
     * @param array $data
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function addBackendUrls(array $data): array
    {
        $data['backend_base_secure_url'] = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_LINK, true);
        $data['backend_base_unsecured_url'] = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_LINK, false);
        return $data;
    }

    /** fallows the class paths ( constant ) and tries to remove the last key from the data array
     * @param array $data
     * @return array
     */
    public function cleanPayload(array &$data): array
    {
        foreach (self::BLOCKED_KEYS as $key){
            $searchKey = explode('/', $key);
            $this->unsetKeys($data, $searchKey);
        }
        return $data;
    }

    /** Tries to unset a key from an array by fallowing the given path to it as a second array .
     * @param array $data
     * @param $arrayPath
     * @return bool
     */
    public function unsetKeys(array &$data, $arrayPath): bool
    {
        if (sizeof($arrayPath) == 1 && isset($data[$arrayPath[0]])) {
            unset($data[$arrayPath[0]]);
            return true;
        }
        if (isset($data[$arrayPath[0]])) {
            $valuePath = $arrayPath[0];
            array_shift($arrayPath);
            $this->unsetKeys($data[$valuePath], $arrayPath);
        }
        return false;
    }

}
