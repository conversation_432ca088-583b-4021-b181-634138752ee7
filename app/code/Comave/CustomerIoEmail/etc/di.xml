<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Mail\Template\TransportBuilder">
        <plugin name="customer_io_email_sender" type="Comave\CustomerIoEmail\Plugin\EmailSender" sortOrder="0" />
    </type>
    <preference for="\Magento\Framework\Mail\Template\TransportBuilder" type="Comave\CustomerIoEmail\Model\Mail\Template\TransportBuilder" />
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave:email-template:map-customer-io-template-codes"
                      xsi:type="object">Comave\CustomerIoEmail\Console\MapCustomerIoTemplateCodes</item>
            </argument>
        </arguments>
    </type>
</config>
