<?php
declare(strict_types=1);

namespace Comave\DbClean\Service;

use Magento\Framework\App\Config\ConfigResource\ConfigInterface;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

class EmailTemplateConfigProvider
{
    public const STORE_CODE_EN = 'en_store';

    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly StoreRepositoryInterface $storeRepository,
        private readonly WriterInterface $configWriter,
        private readonly ConfigInterface $resourceConfig,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @param int $storeId
     * @param string $scope
     * @param string $configPath
     * @return array|null
     */
    public function getEmailTemplateConfigByStore(int $storeId, string $scope, string $configPath): ?array
    {
        try {
            $result = $this->resourceConnection->getConnection()->fetchRow(
                $this->resourceConnection->getConnection()
                    ->select()
                    ->from('core_config_data', ['config_id', 'value'])
                    ->where('scope = ?', $scope)
                    ->where('scope_id = ?', $storeId)
                    ->where('path = ?', $configPath)
            );

            return $result ?: null;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return null;
        }
    }

    /**
     * @param int $configId
     * @return void
     */
    public function deleteConfigById(int $configId): void
    {
        try {
            $deletedConfig = $this->resourceConnection->getConnection()->delete(
                'core_config_data',
                [
                    'config_id = ?' => $configId,
                ]
            );

            if ($deletedConfig) {
                $this->logger->info("Deleted config entry with config_id = $configId.");
            } else {
                $this->logger->warning("No config entry found to delete for config_id = $configId.");
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param string $templateCode
     * @param string $origTemplateCode
     * @return int|null
     */
    public function getEmailTemplateId(string $templateCode, string $origTemplateCode): ?int
    {
        try {
            $templateId = $this->resourceConnection->getConnection()->fetchOne(
                $this->resourceConnection->getConnection()->select()
                    ->from('email_template', 'template_id')
                    ->where('template_code = ?', $templateCode)
                    ->where('orig_template_code = ?', $origTemplateCode)
                    ->limit(1)
            );

            return $templateId != false ? (int) $templateId : null;
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf(
                    'Error fetching email template ID for template_code="%s", orig_template_code="%s": %s',
                    $templateCode,
                    $origTemplateCode,
                    $e->getMessage()
                )
            );
            return null;
        }
    }

    /**
     * @param int $storeId
     * @param string $path
     * @param string $value
     * @return void
     */
    public function setEmailTemplate(int $storeId, string $path, string $value, string $scope = ScopeInterface::SCOPE_STORES): void
    {
        try {
            $this->configWriter->save(
                $path,
                $value,
                $scope,
                $storeId
            );

            $this->logger->info(
                sprintf(
                    'HeaderEmailTemplate config has been set: %s, %d',
                    $path,
                    $value
                )
            );
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf(
                    'HeaderEmailTemplate config has not been set %s, %d, %s',
                    $path,
                    $value,
                    $e->getMessage()
                )
            );
        }
    }

    /**
     * @param string $storeCode
     * @return int|null
     */
    public function getStoreIdByCode(string $storeCode): ?int
    {
        try {
            $store = $this->storeRepository->get($storeCode);
            return (int) $store->getId();
        } catch (NoSuchEntityException $e) {
            return null;
        }
    }

    /**
     * @param int $storeId
     * @param string $scope
     * @param string $configPath
     * @param string $value
     * @return void
     */
    public function updateTemplateConfig(int $storeId, string $scope, string $configPath, string $value): void
    {
        try {
            $this->resourceConfig->saveConfig(
                $configPath,
                $value,
                $scope,
                $storeId
            );
        } catch (NoSuchEntityException $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
