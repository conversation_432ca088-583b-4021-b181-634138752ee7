<?php
declare(strict_types=1);

namespace Comave\DbClean\Service;

class DisabledModuleConfigProvider
{
    /**
     * @return array
     */
    private function getDisabledModuleConfigPaths(): array
    {
        return [
            'Magento_CloudComponents' => '',
            'Magento_PaypalOnBoarding' => '',
            'Coditron_Cartiteminstructions' => [
                'Coditron_Cartiteminstructions/'
            ],
            'Coditron_CategoryTree' => '',
            'Coditron_Club' => [
                'coditronclub/',
                'weltpixel_owl_carousel_config/'
            ],
            'Coditron_Core' => [
                'coditron_core/'
            ],
            'Coditron_CustomerAddressType' => '',
            'Coditron_CustomerCustomAddressType' => '',
            'Coditron_Customheader' => '',
            'Coditron_Dynamicslider' => '',
            'Coditron_GamificationResult' => '',
            'Coditron_Lixtask' => '',
            'Comave_LixApi' => '',
            'Comave_LixApiConnector' => '',
            'Coditron_NotificationLogs' => '',
            'Coditron_RestaurantSearch' => [
                'newrestaurant/',
                'weltpixel_owl_carousel_config/',
                'google_map_api_key/'
            ],
            'Coditron_RestaurantShifts' => '',
            'Coditron_UserHistory' => '',
            'Comave_ApiRestaurant' => '',
            'WeSupply_Toolbox' => [
                'wesupply_api/',
                'help_center_order_tracking_notification/',
                'help_center_returns_rma/',
                'help_center_estimated_delivery_dates/',
                'help_center_store_locator/',
                'help_center_store_pickup_curbside/',
                'help_center_reviews/',
            ],
            'Comave_CheckoutPhoneValidator' => [
                'internationaltelephoneinput/'
            ],
            'Coditron_Languagepack' => '',
            'Comave_CustomerXp' => '',
            'Comave_LixOffer' => '',
            'Comave_OurClubSlider' => [
                'displayourclubslider'
            ],
            'WeltPixel_Quickview' => [
                'weltpixel_quickview/',
                'wp/info/WeltPixel_Quickview'
            ],
            'Comave_Salesorder' => '',
            'Comave_ShiftInfo' => '',
            'Fastly_Cdn' => '',
            'Magebees_Layerednavigation' => [
                'layerednavigation'
            ],
            'Magelearn_Customform' => '',
            'MiniOrange_OAuth' => [
                'miniorange/'
            ],
            'Comave_CancelOrder' => '',
            'Webkul_GeoIp' => '',
            'Webkul_MpRmaSystem' => [
                'mprmasystem/'
            ],
            'Webkul_MpSellerMapLocator' => [
                'mpsellermaplocator/'
            ],
            'Webkul_MpSellerProductSearch' => '',
            'Webkul_Requestforquote' => [
                'requestforquote/'
            ],
            'WeltPixel_AjaxInfiniteScroll' => [
                'weltpixel_infinite_scroll/'
            ],
            'WeltPixel_GoogleTagManager' => [
                'wp/info/WeltPixel_GoogleTagManager'
            ],
            'WeltPixel_InstagramWidget' => [
                'wp/info/WeltPixel_InstagramWidget'
            ],
            'WeltPixel_RecentlyViewedBar' => [
                'wp/info/WeltPixel_RecentlyViewedBar'
            ],
            'WeltPixel_Newsletter' => [
                'weltpixel_newsletter/'
            ],
            'WeltPixel_OwlCarouselSlider' => [
                'weltpixel_owl_carousel_config/',
                'weltpixel_owl_slider_config/'
            ],
            'WeltPixel_NavigationLinks' => [
                'weltpixel_megamenu/'
            ],
            'WeltPixel_CategoryPage' => [
                'weltpixel_category_page/'
            ],
            'Comave_POSTamias' => [
                'api_configuration/'
            ],
            'WeltPixel_SocialLogin' => [
                'weltpixel_sociallogin/'
            ],
            'Webkul_Mpsplitorder' => [
                'marketplace/mpsplitorder_enable' //???
            ],
        ];
    }

    /**
     * @return array
     */
    public function getDisabledModuleConfigPathsValues(): array
    {
        $config = $this->getDisabledModuleConfigPaths();

        $configPaths = [];

        foreach ($config as $value) {
            if (is_array($value) && !empty($value)) {
                $configPaths = array_merge($configPaths, $value);
            }
        }

        return $configPaths;
    }
}
