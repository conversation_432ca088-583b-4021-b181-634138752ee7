<?php
declare(strict_types=1);

namespace Comave\DbClean\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Comave\MigrateEmailTemplates\Service\EnglishStoreHeaderEmailTemplateService;

class UpdateEnglishStoreHeaderEmailTemplateV2 implements DataPatchInterface
{
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EnglishStoreHeaderEmailTemplateService $englishStoreHeaderEmailTemplate,
    ) {}

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $this->englishStoreHeaderEmailTemplate->updateEnglishStoreHeaderEmailTemplate();

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
