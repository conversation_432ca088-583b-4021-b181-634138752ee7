<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_catalog_product_blacklisting.comave_catalog_product_blacklisting_data_source
            </item>
        </item>
    </argument>
    <settings>
        <buttons>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">Add Blacklisted Products</label>
            </button>
        </buttons>
        <spinner>comave_catalog_product_blacklist_columns</spinner>
        <deps>
            <dep>comave_catalog_product_blacklisting.comave_catalog_product_blacklisting_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_catalog_product_blacklisting_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">blacklist_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="comave_catalog_product_blacklisting_data_source">
            <settings>
                <requestFieldName>blacklist_id</requestFieldName>
                <primaryFieldName>blacklist_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
        </filters>
        <massaction name="listing_massaction">
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete the selected blacklisted product?</message>
                        <title translate="true">Delete</title>
                    </confirm>
                    <url path="comave_catalog/product_blacklist/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="comave_catalog_product_blacklist_columns">
        <selectionsColumn name="ids">
            <settings>
                <indexField>blacklist_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>20</resizeDefaultWidth>
            </settings>
        </selectionsColumn>
        <column name="blacklist_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
                <visible>false</visible>
            </settings>
        </column>
        <column name="product_sku">
            <settings>
                <filter>text</filter>
                <label translate="true">Product SKU</label>
                <draggable>false</draggable>
            </settings>
        </column>
        <column name="product_name">
            <settings>
                <filter>text</filter>
                <label translate="true">Product Name</label>
                <draggable>false</draggable>
                <sortable>true</sortable>
            </settings>
        </column>
        <column name="product_status" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <options class="Comave\Catalog\Ui\Component\Listing\Column\Product\Blacklist\Status"/>
                <label translate="true">Product Status</label>
                <draggable>false</draggable>
            </settings>
        </column>
        <actionsColumn name="actions" class="Comave\Catalog\Ui\Component\Listing\Column\Product\Blacklist\Actions">
            <settings>
                <indexField>blacklist_id</indexField>
                <resizeEnabled>false</resizeEnabled>
            </settings>
        </actionsColumn>
    </columns>
</listing>
