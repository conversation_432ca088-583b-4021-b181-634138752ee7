<?xml version="1.0"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_catalog_product_blacklist_form.comave_catalog_product_blacklist_form_data_source
            </item>
        </item>
        <item name="label" xsi:type="string" translate="true">Blacklist Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="back" class="ComaveCatalogBlacklistButtonBack"/>
            <button name="save" class="ComaveCatalogBlacklistButtonSave"/>
        </buttons>
        <namespace>comave_catalog_product_blacklist_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>comave_catalog_product_blacklist_form.comave_catalog_product_blacklist_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_catalog_product_blacklist_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="comave_catalog/product_blacklist/save"/>
        </settings>
        <dataProvider class="ComaveCatalogBlacklistUiFormDataProvider"
                      name="comave_catalog_product_blacklist_form_data_source"/>
    </dataSource>
    <fieldset name="blacklisted_product" sortOrder="10">
        <settings>
            <collapsible>false</collapsible>
            <opened>true</opened>
            <label translate="true"></label>
        </settings>
        <field name="product_sku" component="Magento_Ui/js/form/element/ui-select" sortOrder="10" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filterOptions" xsi:type="boolean">true</item>
                    <item name="multiple" xsi:type="boolean">true</item>
                    <item name="showCheckbox" xsi:type="boolean">true</item>
                    <item name="disableLabel" xsi:type="boolean">true</item>
                    <item name="levelsVisibility" xsi:type="number">1</item>
                    <item name="searchOptions" xsi:type="boolean">true</item>
                    <item name="searchUrl" xsi:type="url" path="comave_catalog/product/search"/>
                    <item name="filterPlaceholder" xsi:type="string" translate="true">Search by Name or SKU or EAN</item>
                    <item name="emptyOptionsHtml" xsi:type="string" translate="true">Start typing to find product</item>
                    <item name="missingValuePlaceholder" xsi:type="string" translate="true">%s</item>
                    <item name="dataScope" xsi:type="string">product_sku</item>
                </item>
            </argument>
            <settings>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <elementTmpl>ui/grid/filters/elements/ui-select</elementTmpl>
                <label translate="true">Products</label>
                <dataScope>product_sku</dataScope>
                <componentType>field</componentType>
                <listens>
                    <link name="${ $.namespace }.${ $.namespace }:responseData">setParsed</link>
                </listens>
            </settings>
        </field>
    </fieldset>
</form>
