<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Plugin\Model\ProductLink;

use Comave\Catalog\Model\Product\BlacklistUiManager;
use Magento\Catalog\Model\ProductLink\Search;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\Exception\LocalizedException;

readonly class SearchPlugin
{
    /**
     * @param \Comave\Catalog\Model\Product\BlacklistUiManager $blacklistUiManager
     */
    public function __construct(private BlacklistUiManager $blacklistUiManager)
    {
    }

    /**
     * @param \Magento\Catalog\Model\ProductLink\Search $search
     * @param \Magento\Catalog\Model\ResourceModel\Product\Collection $productCollection
     * @return \Magento\Catalog\Model\ResourceModel\Product\Collection
     */
    public function afterPrepareCollection(Search $search, Collection $productCollection): Collection
    {
        try {
            return $productCollection->addAttributeToFilter(
                'sku',
                ['nin' => $this->getBlacklistedProductSkus()]
            );
        } catch (LocalizedException $e) {
            return $productCollection;
        }
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function getBlacklistedProductSkus(): array
    {
        return array_map(function ($blacklistedProduct) {
            return $blacklistedProduct->getProductSku();
        }, $this->blacklistUiManager->getList());
    }
}
