<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Ui\Component\Listing\Column\Product\Blacklist;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class Actions extends Column
{
    /**
     * Url path to edit
     * @var string
     */
    private const string URL_PATH_EDIT = 'comave_catalog/product_blacklist/edit';

    /**
     * Url path  to delete
     * @var string
     */
    private const string URL_PATH_DELETE = 'comave_catalog/product_blacklist/delete';

    /**
     * Url builder
     * @var UrlInterface
     */
    protected UrlInterface $urlBuilder;

    /**
     * @param \Magento\Framework\View\Element\UiComponent\ContextInterface $context
     * @param \Magento\Framework\View\Element\UiComponentFactory $uiComponentFactory
     * @param \Magento\Framework\UrlInterface $urlBuilder
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        UrlInterface $urlBuilder,
        array $components = [],
        array $data = []
    ) {
        $this->urlBuilder = $urlBuilder;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (!isset($dataSource['data']['items'])) {
            return $dataSource;
        }

        foreach ($dataSource['data']['items'] as & $item) {
            if (!isset($item['blacklist_id'])) {
                continue;
            }
            $item[$this->getData('name')]['delete'] = [
                'href' => $this->urlBuilder->getUrl(
                    static::URL_PATH_DELETE,
                    [
                        'blacklist_id' => $item['blacklist_id'],
                    ]
                ),
                'label' => __('Delete'),
                'confirm' => [
                    'title' => __('Delete'),
                    'message' => __(
                        'Are you sure you want to delete this blacklisted product?'
                    ),
                ],
            ];
        }

        return $dataSource;
    }
}
