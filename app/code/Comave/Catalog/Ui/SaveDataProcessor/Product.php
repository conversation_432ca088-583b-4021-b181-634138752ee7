<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Ui\SaveDataProcessor;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Comave\Catalog\Model\Product\BlacklistUiManager;
use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Umc\Crud\Ui\SaveDataProcessorInterface;

class Product implements SaveDataProcessorInterface
{
    /**
     * @param \Comave\Catalog\Model\Product\BlacklistUiManager $blacklistUiManager
     * @param \Magento\Catalog\Model\ProductRepository $productRepository
     * @param array $fields
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly BlacklistUiManager $blacklistUiManager,
        private readonly ProductRepository $productRepository,
        private readonly array $fields
    ) {
    }

    /**
     * @param array $data
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function modifyData(array $data): array
    {
        foreach ($this->fields as $field) {
            if (array_key_exists($field, $data)) {
                $searchCriteria = $this->searchCriteriaBuilder->addFilter('entity_id', $data[$field], 'in')->create();
                $productList = $this->productRepository->getList($searchCriteria);
                $data[$field] = [];
                foreach ($productList->getItems() as $product) {
                    $item = [
                        BlacklistInterface::PRODUCT_SKU => $product->getSku(),
                        BlacklistInterface::PRODUCT_NAME => $product->getName(),
                        BlacklistInterface::PRODUCT_STATUS => BlacklistInterface::PRODUCT_STATUS_DISCONTINUED,
                    ];
                    $data[$field][$product->getId()] = $item;
                }
            }
        }

        return $data;
    }
}
