<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!--[+] define the ui config for entity -->
    <virtualType name="ComaveCatalogBlacklistUiConfig" type="Umc\Crud\Ui\EntityUiConfig">
        <arguments>
            <argument name="interface" xsi:type="string">Comave\Catalog\Api\Data\Product\BlacklistInterface</argument>
            <argument name="data" xsi:type="array">
                <item name="name_attribute" xsi:type="string">
                    product_name
                </item><!-- this is the attribute / field name that represents your entity -->
                <item name="labels" xsi:type="array">
                    <item name="new" xsi:type="string" translatable="true">Add New Products to Blacklist</item>
                    <item name="back" xsi:type="string" translatable="true">Back to list</item>
                    <item name="save" xsi:type="string" translatable="true">Save Blacklisted Products</item>
                    <item name="delete" xsi:type="string" translatable="true">Delete Product</item>
                    <item name="delete_message" xsi:type="string" translatable="true">Are you sure you want to delete
                        this blacklisted product?
                    </item>
                </item>
                <item name="save" xsi:type="array">
                    <item name="allow_close" xsi:type="boolean">false</item>
                    <item name="allow_duplicate" xsi:type="boolean">false</item>
                </item>
                <item name="list" xsi:type="array">
                    <item name="page_title" xsi:type="string" translatable="true">Catalog Blacklisted Products</item>
                </item>
                <item name="messages" xsi:type="array">
                    <item name="delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">Blacklisted Product successfully
                            deleted
                        </item>
                        <item name="missing_entity" xsi:type="string" translatable="true">Couldn't find blacklisted
                            product to delete
                        </item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem deleting the
                            blacklisted product
                        </item>
                    </item>
                    <item name="save" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">Blacklist Product successfully
                            saved
                        </item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem saving the
                            blacklisted product
                        </item>
                        <item name="duplicate" xsi:type="string" translatable="true">Blacklist Product duplicated
                            successfully
                        </item>
                    </item>
                    <item name="mass_delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">%1 Blacklisted Products were
                            successfully deleted
                        </item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem deleting the
                            blacklisted products
                        </item>
                    </item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    <!--[-] define the ui config for "media" entity -->
    <!--[+] configure admin controllers to use the ui entity config for "media" -->
    <virtualType name="ComaveCatalogBlacklistDataModifier" type="Umc\Crud\Ui\Form\DataModifier\CompositeDataModifier">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="product" xsi:type="object">ComaveCatalogBlacklistProductDataModifier</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveCatalogBlacklistSaveDataProcessor" type="Umc\Crud\Ui\SaveDataProcessor\CompositeProcessor">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="product" xsi:type="object">ComaveCatalogBlacklistProductProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveCatalogBlacklistProductProcessor" type="Comave\Catalog\Ui\SaveDataProcessor\Product">
        <arguments>
            <argument name="fields" xsi:type="array">
                <item name="product_sku" xsi:type="string">product_sku</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveCatalogBlacklistProductDataModifier" type="Comave\Catalog\Ui\Form\DataModifier\Product">
        <arguments>
            <argument name="fields" xsi:type="array">
                <item name="product_sku" xsi:type="string">product_sku</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\Catalog\Controller\Adminhtml\Product\Blacklist\Index">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\Catalog\Controller\Adminhtml\Product\Blacklist\Edit">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\Catalog\Model\Product\BlacklistUiManager
            </argument>
        </arguments>
    </type>
    <type name="Comave\Catalog\Controller\Adminhtml\Product\Blacklist\Save">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\Catalog\Model\Product\BlacklistUiManager</argument>
            <argument name="dataProcessor" xsi:type="object">ComaveCatalogBlacklistSaveDataProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\Catalog\Controller\Adminhtml\Product\Blacklist\InlineEdit">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\Catalog\Model\Product\BlacklistUiManager</argument>
            <argument name="dataProcessor" xsi:type="object">Umc\Crud\Ui\SaveDataProcessor\NullProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\Catalog\Controller\Adminhtml\Product\Blacklist\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\Catalog\Model\Product\BlacklistUiManager</argument>
        </arguments>
    </type>
    <type name="Comave\Catalog\Controller\Adminhtml\Product\Blacklist\MassDelete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\Catalog\Model\Product\BlacklistUiManager</argument>
            <argument name="collectionProvider" xsi:type="object">
                Comave\Catalog\Model\Product\BlacklistUiCollectionProvider
            </argument>
        </arguments>
    </type>
    <!--[+] configure admin controllers to use the ui entity config for "process" -->
    <virtualType name="ComaveCatalogBlacklistUiFormDataProvider" type="Umc\Crud\Ui\Form\DataProvider">
        <arguments>
            <argument name="primaryFieldName" xsi:type="object">blacklist_id</argument>
            <argument name="requestFieldName" xsi:type="object">blacklist_id</argument>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
            <argument name="dataModifier" xsi:type="object">ComaveCatalogBlacklistDataModifier</argument>
            <argument name="collectionProvider" xsi:type="object">
                Comave\Catalog\Model\Product\BlacklistUiCollectionProvider
            </argument>
        </arguments>
    </virtualType>
    <!--[+] form button configuration for 'process' -->
    <virtualType name="ComaveCatalogBlacklistButtonBack" type="Umc\Crud\Block\Adminhtml\Button\Back">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveCatalogBlacklistButtonSave" type="Umc\Crud\Block\Adminhtml\Button\Save">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveCatalogBlacklistButtonDelete" type="Umc\Crud\Block\Adminhtml\Button\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\Catalog\Model\Product\BlacklistUiManager
            </argument>
        </arguments>
    </virtualType>
    <!--[-] form button configuration for 'process' -->
    <!--[+] configure the grid actions column  for "process" entity-->
    <virtualType name="ComaveCatalogBlacklistGridActions" type="Umc\Crud\Ui\Component\Listing\ActionsColumn">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveCatalogBlacklistUiConfig</argument>
        </arguments>
    </virtualType>
    <!--[-] configure the grid actions column for "process" entity-->
    <type name="Magento\Catalog\Model\ProductLink\Search">
        <plugin name="comave_catalog_product_link_search" type="Comave\Catalog\Plugin\Model\ProductLink\SearchPlugin"/>
    </type>
</config>
