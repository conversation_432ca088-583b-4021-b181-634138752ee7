<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Controller\Adminhtml\Product\Blacklist;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Umc\Crud\Ui\EntityUiConfig;
use Umc\Crud\Ui\EntityUiManagerInterface;
use Umc\Crud\Ui\SaveDataProcessorInterface;

class Save extends \Umc\Crud\Controller\Adminhtml\Save
{
    public const string ADMIN_RESOURCE = 'Comave_Catalog::comave_catalog_blacklist';

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Api\DataObjectHelper $dataObjectHelper
     * @param \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor
     * @param \Umc\Crud\Ui\SaveDataProcessorInterface $dataProcessor
     * @param \Umc\Crud\Ui\EntityUiManagerInterface $entityUiManager
     * @param \Umc\Crud\Ui\EntityUiConfig $uiConfig
     */
    public function __construct(
        private readonly Context $context,
        private readonly DataObjectHelper $dataObjectHelper,
        private readonly DataPersistorInterface $dataPersistor,
        private readonly SaveDataProcessorInterface $dataProcessor,
        private readonly EntityUiManagerInterface $entityUiManager,
        private readonly EntityUiConfig $uiConfig
    ) {
        parent::__construct(
            $context,
            $dataObjectHelper,
            $dataPersistor,
            $dataProcessor,
            $entityUiManager,
            $uiConfig
        );
    }

    /**
     * run the action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $entity = null;
        $postData = $this->getRequest()->getPostValue();
        $data = $this->dataProcessor->modifyData($postData);
        $requestParam = $this->uiConfig->getRequestParamName();
        $persistorKey = $this->uiConfig->getPersistoryKey();
        $id = !empty($data[$requestParam]) ? (int)$data[$requestParam] : null;
        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        try {
            $this->saveMultiple($data[BlacklistInterface::PRODUCT_SKU]);
            $this->messageManager->addSuccessMessage($this->uiConfig->getSaveSuccessMessage());
            $this->dataPersistor->clear($this->uiConfig->getPersistoryKey());
            $back = $this->getRequest()->getParam('back', 'continue');
            if ($back === 'close' && $this->uiConfig->getAllowSaveAndClose()) {
                $resultRedirect->setPath('*/*/index');
            } elseif ($back === 'duplicate' && $this->uiConfig->getAllowSaveAndDuplicate()) {
                $newEntity = $this->entityUiManager->get(null);
                $this->dataObjectHelper->populateWithArray(
                    $newEntity,
                    $data,
                    $this->uiConfig->getInterface()
                );
                $newEntity->setId(null);
                $this->entityUiManager->save($newEntity);
                $mewId = $newEntity->getId();
                $this->messageManager->addSuccessMessage($this->uiConfig->getDuplicateSuccessMessage());
                $this->dataPersistor->set($persistorKey, $data);
                $resultRedirect->setPath('*/*/edit', [$requestParam => $mewId]);
            } else {
                $resultRedirect->setPath('*/*/index');
            }
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $this->dataPersistor->set($persistorKey, $postData);
            $resultRedirect->setPath('*/*/edit', [$requestParam => $id]);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($this->uiConfig->getSaveErrorMessage());
            $this->dataPersistor->set($persistorKey, $postData);
            $resultRedirect->setPath('*/*/edit', [$requestParam => $id]);
        }

        return $resultRedirect;
    }

    /**
     * @param array $items
     * @return void
     */
    private function saveMultiple(array $items): void
    {
        foreach ($items as $item) {
            try {
                $entity = $this->entityUiManager->get(null);
                $this->dataObjectHelper->populateWithArray(
                    $entity,
                    $item,
                    $this->uiConfig->getInterface()
                );
                $this->entityUiManager->save($entity);
            } catch (NoSuchEntityException|LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            }
        }
    }
}
