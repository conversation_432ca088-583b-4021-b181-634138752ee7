<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Controller\Adminhtml\Product;

use Comave\Catalog\Model\Product\BlacklistUiManager;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultInterface;

class Search extends Action implements HttpGetActionInterface
{
    const ADMIN_RESOURCE = 'Magento_Catalog::products';

    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    private $resultJsonFactory;

    /**
     * @var \Magento\Catalog\Model\ProductLink\Search
     */
    private $productSearch;

    /**
     * @param \Magento\Framework\Controller\Result\JsonFactory $resultFactory
     * @param \Magento\Catalog\Model\ProductLink\Search $productSearch
     * @param \Magento\Backend\App\Action\Context $context
     */
    public function __construct(
        private readonly BlacklistUiManager $blacklistUiManager,
        JsonFactory $resultFactory,
        \Magento\Catalog\Model\ProductLink\Search $productSearch,
        Context $context
    ) {
        $this->resultJsonFactory = $resultFactory;
        $this->productSearch = $productSearch;
        parent::__construct($context);
    }

    /**
     * Execute product search.
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute(): ResultInterface
    {
        $searchKey = $this->getRequest()->getParam('searchKey');
        $pageNum = (int)$this->getRequest()->getParam('page');
        $limit = (int)$this->getRequest()->getParam('limit');

        /** @var \Magento\Catalog\Model\ResourceModel\Product\Collection $productCollection */
        $productCollection = $this->productSearch->prepareCollection($searchKey, $pageNum, $limit);
        $productCollection = $productCollection->addAttributeToFilter(
            'sku',
            ['nin' => $this->getBlacklistedProductSkus()]
        );
        $totalValues = $productCollection->getSize();
        $productById = [];
        /** @var  ProductInterface $product */
        foreach ($productCollection as $product) {
            $productId = $product->getId();
            $productById[$productId] = [
                'value' => $productId,
                'label' => $product->getName(),
                'is_active' => $product->getStatus(),
                'path' => $product->getSku(),
                'optgroup' => false,
            ];
        }
        /** @var \Magento\Framework\Controller\Result\Json $resultJson */
        $resultJson = $this->resultJsonFactory->create();

        return $resultJson->setData([
            'options' => $productById,
            'total' => empty($productById) ? 0 : $totalValues,
        ]);
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function getBlacklistedProductSkus(): array
    {
        return array_map(function ($blacklistedProduct) {
            return $blacklistedProduct->getProductSku();
        }, $this->blacklistUiManager->getList());
    }
}
