<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Cron;

use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Comave\SellerStatus\Model\RoleValidator\Active;
use Comave\SellerStatus\Model\RoleValidator\OutOfStock;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\State;
use Psr\Log\LoggerInterface;

class OutOfStockSellers
{
    /**
     * @param OutOfStock $outOfStockStatusAssign
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     */
    public function __construct(
        private readonly OutOfStock $outOfStockStatusAssign,
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
        private readonly SellerCompanyRolesProvider $sellerCompanyRolesProvider,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(): void
    {
        $sellerRoles = $this->sellerCompanyRolesProvider->get(OutOfStock::ROLE_NAME);
        /** @var RoleInterface $outOfStock */
        $outOfStock = current($sellerRoles);
        $sellerRoles = $this->sellerCompanyRolesProvider->get(Active::ROLE_NAME);
        /** @var RoleInterface $outOfStock */
        $activeRole = current($sellerRoles);
        $connection = $this->resourceConnection->getConnection();
        $sellerIdsSelect = $connection->select()
            ->distinct()
            ->from(
                [
                    'main' => $connection->getTableName('marketplace_userdata'),
                ],
                [
                    'seller_id'
                ]
            )->join(
                ['c' => $connection->getTableName('customer_entity')],
                'c.entity_id = main.seller_id',
                []
            )->join(
                ['cr' => $connection->getTableName('company_user_roles')],
                'cr.user_id = main.seller_id',
                [
                    'role_id'
                ]
            )->where(
                'role_id IN (?)',
                [
                    $outOfStock->getId(),
                    $activeRole->getId(),
                ]
            )->where(
                'force_assigned = ?',
                0
            );

        $toOutOfStock = [];
        $toActivate = [];
        $sellerIds = $connection->fetchAll($sellerIdsSelect);

        if (empty($sellerIds)) {
            $this->logger->info(
                'No sellers found to update',
            );

            return;
        }

        foreach ($sellerIds as $rowData) {
            $sellerId = (int) $rowData['seller_id'];
            $isOutOfStock = $this->outOfStockStatusAssign->getForSeller((int) $sellerId);

            if (!$isOutOfStock && (int) $rowData['role_id'] === (int) $outOfStock->getId()) {
                $toActivate[] = $sellerId;
            } elseif ($isOutOfStock && (int) $rowData['role_id'] === (int) $activeRole->getId()) {
                $toOutOfStock[] = $sellerId;
            }
        }

        if (empty($toOutOfStock) && empty($toActivate)) {
            $this->logger->info(
                'No sellers found to update',
            );

            return;
        }

        $this->logger->info(
            'Assigning statuses for sellers',
            [
                'outOfStock' => $toOutOfStock,
                'activates' => $toActivate
            ]
        );
        $connection->beginTransaction();

        try {
            foreach ($toOutOfStock as $sellerId) {
                $this->companyUserRoleManagement->setRolesForCompanyUser(
                    (int) $sellerId,
                    (int) $outOfStock->getCompanyId(),
                    [$outOfStock->getId()]
                );
            }

            foreach ($toActivate as $sellerId) {
                $this->companyUserRoleManagement->setRolesForCompanyUser(
                    (int) $sellerId,
                    (int) $activeRole->getCompanyId(),
                    [$activeRole->getId()]
                );
            }

            $connection->commit();
        } catch (\Throwable $e) {
            $this->logger->warning(
                'Unable to assign sellers statuses',
                [
                    'message' => $e->getMessage(),
                ]
            );

            $connection->rollBack();
        }
    }
}
