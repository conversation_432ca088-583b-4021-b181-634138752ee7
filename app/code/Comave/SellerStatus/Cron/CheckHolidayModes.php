<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Cron;

use Comave\SellerStatus\Api\RoleProviderInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Model\Command\EmailNotifier;
use Comave\SellerStatus\Model\Command\GetHolidayDates;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Comave\SellerStatus\Model\RoleValidator\Active;
use Comave\SellerStatus\Model\RoleValidator\HolidayMode;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;
use Webkul\Marketplace\Model\Seller;

class CheckHolidayModes
{
    /**
     * @param CollectionFactory $sellerFactory
     * @param EmailNotifier $emailNotifier
     * @param GetHolidayDates $getHolidayDates
     * @param LoggerInterface $logger
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param RoleProviderManagementInterface $roleProviderManagement
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     */
    public function __construct(
        private readonly CollectionFactory $sellerFactory,
        private readonly EmailNotifier $emailNotifier,
        private readonly GetHolidayDates $getHolidayDates,
        private readonly LoggerInterface $logger,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly RoleProviderManagementInterface $roleProviderManagement,
        private readonly SellerCompanyRolesProvider $sellerCompanyRolesProvider
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(): void
    {
        $roles = $this->sellerCompanyRolesProvider->get(
            HolidayMode::ROLE_NAME
        );
        $holidayMode = current($roles);
        $collection = $this->sellerFactory->create();
        $collection->getSelect()
            ->join(
                ['cu' => $collection->getTable('company_user_roles')],
                'main_table.seller_id = cu.user_id',
                [
                    'note'
                ]
            );
        $collection->addFieldToFilter(
            'cu.role_id',
            $holidayMode->getId()
        );

        if (!$collection->getSize()) {
            $this->logger->info(
                'No sellers with holiday mode active'
            );

            return;
        }

        /** @var Seller $sellerModel */
        foreach ($collection->getItems() as $sellerModel) {
            try {
                $dates = $this->getHolidayDates->get($sellerModel->getSellerId());
            } catch (\Exception $e) {
                $this->logger->warning(
                    'Unable to get dates for seller',
                    [
                        'message' => $e->getMessage(),
                        'seller_id' => $sellerModel->getSellerId()
                    ]
                );

                continue;
            }

            $endDate = \DateTime::createFromFormat(
                'Y-m-d',
                $dates['end_date']
            )->setTime(0, 0);
            $now = (new \DateTime())->setTime(0, 0);

            if ($now->getTimestamp() >= $endDate->getTimestamp()) {
                $this->reactivateAccount($sellerModel);
                continue;
            }

            $diffInHours = $endDate->diff($now)->days * 24;

            if ($diffInHours > 24) {
                $this->logger->info(
                    'Continuing, not within the 24 hours of reactivation',
                    [
                        'seller_id' => $sellerModel->getSellerId(),
                        'holiday_mode_remaining_hours' => $diffInHours
                    ]
                );

                continue;
            }

            if ($diffInHours === 0) {
                $this->reactivateAccount($sellerModel);
                continue;
            }

            $this->sendReminder($sellerModel, $dates);
        }
    }

    /**
     * @param Seller $seller
     * @return void
     * @throws LocalizedException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function reactivateAccount(Seller $seller): void
    {
        $roleManager = $this->roleProviderManagement->getForSeller(
            (int) $seller->getSellerId()
        );

        if (!$roleManager instanceof RoleProviderInterface) {
            throw new LocalizedException(__('Invalid role manager provided'));
        }

        $role = $roleManager->getRole();

        if ($role->getRoleName() !== Active::ROLE_NAME) {
            $this->logger->info(
                'Seller not activated, different status detected',
                [
                    'seller' => $seller->getSellerId(),
                    'new_role' => $role->getRoleName()
                ]
            );
        } else {
            $this->logger->info(
                'Seller re-activated',
                [
                    'seller' => $seller->getSellerId()
                ]
            );
        }

        $this->companyUserRoleManagement->setRolesForCompanyUser(
            (int) $seller->getSellerId(),
            (int) $role->getCompanyId(),
            [$role->getId()]
        );
    }

    /**
     * @param Seller $sellerModel
     * @param array{start_date: string, end_date: string} $dates
     * @return void
     */
    private function sendReminder(Seller $sellerModel, array $dates): void
    {
        try {
            $this->emailNotifier->sendEmail(
                $sellerModel,
                'comave_seller_status_email_templates_holiday_mode_reminder',
                [
                    'start_date' => \DateTime::createFromFormat(
                        'Y-m-d',
                        $dates['start_date']
                    )->format('l jS \o\f F Y'),
                    'end_date' => \DateTime::createFromFormat(
                        'Y-m-d',
                        $dates['end_date']
                    )->format('l jS \o\f F Y'),
                ]
            );
        } catch (\Exception $e) {
            $this->logger->warning(
                'Unable to send email for seller',
                [
                    'message' => $e->getMessage(),
                    'seller_id' => $sellerModel->getSellerId()
                ]
            );
        }
    }
}
