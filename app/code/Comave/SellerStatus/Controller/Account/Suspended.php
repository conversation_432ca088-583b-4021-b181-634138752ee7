<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Controller\Account;

use Comave\SellerStatus\Model\SellerRegistry;
use Magento\Customer\Controller\AccountInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\Page;

class Suspended implements AccountInterface, HttpGetActionInterface, HttpPostActionInterface
{
    /**
     * @param ResultFactory $resultFactory
     * @param SellerRegistry $sellerRegistry
     * @param CurrentCustomer $currentCustomer
     */
    public function __construct(
        private readonly ResultFactory $resultFactory,
        private readonly SellerRegistry $sellerRegistry,
        private readonly CurrentCustomer $currentCustomer,
    ) {
    }

    /**
     * @return Page
     */
    public function execute(): Page
    {
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $resultPage->getConfig()->getTitle()->set(
            __('Account has been rejected')
        );

        $this->sellerRegistry->setSellerId(
            (string) $this->currentCustomer->getCustomerId()
        );
        return $resultPage;
    }
}
