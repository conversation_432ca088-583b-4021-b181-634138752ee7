<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Model\MarketplaceOrder\Processor;

use Comave\SplitOrder\Api\SellerCartDetailsInterface;
use Magento\Bundle\Model\Product\Type;
use Magento\Framework\DataObject;
use Magento\Framework\Event\ManagerInterface;
use Magento\Sales\Model\Order;
use Webkul\Marketplace\Api\Data\OrdersInterfaceFactory;
use Webkul\Marketplace\Helper\Data;
use Webkul\Marketplace\Helper\Notification;
use Webkul\Marketplace\Model\ResourceModel\Orders;
use Webkul\Marketplace\Model\ResourceModel\Saleperpartner\CollectionFactory;
use Webkul\Marketplace\Model\SaleslistFactory;
use Webkul\Marketplace\Model\ResourceModel\Saleslist;

class MarketplaceOrderProcessor
{
    /**
     * @param SaleslistFactory $salesListFactory
     * @param Saleslist $saleslistResource
     * @param CollectionFactory $salesPartnerCollectionFactory
     * @param ManagerInterface $eventManager
     * @param OrdersInterfaceFactory $orderFactory
     * @param Orders $mpOrderResource
     * @param Data $marketplaceHelper
     * @param Notification $notificationHelper
     */
    public function __construct(
        private readonly SaleslistFactory $salesListFactory,
        private readonly Saleslist $saleslistResource,
        private readonly CollectionFactory $salesPartnerCollectionFactory,
        private readonly ManagerInterface $eventManager,
        private readonly OrdersInterfaceFactory $orderFactory,
        private readonly Orders $mpOrderResource,
        private readonly Data $marketplaceHelper,
        private readonly Notification $notificationHelper
    ) {
    }

    /**
     * @param Order $order
     * @return \Webkul\Marketplace\Model\Orders[]
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    public function process(Order $order): array
    {
        /*
       * Marketplace Order details save before Observer
       */
        $this->eventManager->dispatch(
            'mp_order_save_before',
            ['order' => $order]
        );

        /*
        * Get Current Store Currency Rate
        */
        $currentCurrencyCode = $order->getOrderCurrencyCode();
        $baseCurrencyCode = $order->getBaseCurrencyCode();
        $allowedCurrencies = $this->marketplaceHelper->getConfigAllowCurrencies();
        $rates = $this->marketplaceHelper->getCurrencyRates(
            $baseCurrencyCode,
            array_values($allowedCurrencies)
        );
        if (empty($rates[$currentCurrencyCode])) {
            $rates[$currentCurrencyCode] = 1;
        }

        $lastOrderId = $order->getId();

        /*
        * Marketplace Credit Management module Observer
        */
        $this->eventManager->dispatch(
            'mp_discount_manager',
            ['order' => $order]
        );

        $this->eventManager->dispatch(
            'mp_advance_commission_rule',
            ['order' => $order]
        );

        $sellerData = $this->getSellerProductData($order, $rates[$currentCurrencyCode]);
        $sellerProArr = $sellerData['seller_pro_arr'];
        $sellerTaxArr = $sellerData['seller_tax_arr'];
        $sellerCouponArr = $sellerData['seller_coupon_arr'];

        $taxToSeller = $this->marketplaceHelper->getConfigTaxManage();
        $shippingAll = $order->getData('shipping_data');
        $shippingAllCount = !($shippingAll == null) && count($shippingAll);
        $createdOrders = [];

        foreach ($sellerProArr as $sellerId => $value) {
            $productIds = implode(',', $value);
            $data = [
                'order_id' => $lastOrderId,
                'product_ids' => $productIds,
                'seller_id' => $sellerId,
                'total_tax' => $sellerTaxArr[$sellerId],
                'tax_to_seller' => $taxToSeller,
            ];

            if ($shippingAllCount) {
                $shippingCharges = current($shippingAll[$sellerId]);
                $data = [
                    'order_id' => $lastOrderId,
                    'product_ids' => $productIds,
                    'seller_id' => $sellerId,
                    'shipping_charges' => $shippingCharges['price'],
                    'total_tax' => $sellerTaxArr[$sellerId],
                    'tax_to_seller' => $taxToSeller,
                    'carrier_name' => $shippingCharges['service_name']
                ];
            } elseif (!$sellerId) {
                $shippingCharges = $order->getBaseShippingAmount();
                $data = [
                    'order_id' => $lastOrderId,
                    'product_ids' => $productIds,
                    'seller_id' => $sellerId,
                    'shipping_charges' => $shippingCharges,
                    'total_tax' => $sellerTaxArr[$sellerId],
                    'tax_to_seller' => $taxToSeller,
                ];
            }

            if (!empty($sellerCouponArr) && !empty($sellerCouponArr[$sellerId])) {
                $data['coupon_amount'] = $sellerCouponArr[$sellerId];
            }

            $data['seller_pending_notification'] = 1;
            $mpOrderModel = $this->orderFactory->create();
            $mpOrderModel->setData($data);
            $this->mpOrderResource->save($mpOrderModel);

            $sellerOrderId = $mpOrderModel->getId();
            $createdOrders[$sellerId] = $mpOrderModel;
            $this->notificationHelper->saveNotification(
                \Webkul\Marketplace\Model\Notification::TYPE_ORDER,
                $sellerOrderId,
                $lastOrderId
            );
        }

        $this->eventManager->dispatch(
            'mp_order_save_after',
            [
                'order' => $order
            ]
        );

        return $createdOrders;
    }

    /**
     * @param Order $order
     * @param int $ratesPerCurrency
     * @return array[]
     */
    private function getSellerProductData(Order $order, int $ratesPerCurrency): array
    {
        $lastOrderId = $order->getId();
        $percent = $this->marketplaceHelper->getConfigCommissionRate();
        $sellerProArr = [];
        $sellerTaxArr = [];
        $sellerCouponArr = [];
        $isShippingFlag = [];
        $advanceCommissionRule = [];

        foreach ($order->getAllItems() as $item) {
            $sellerId = $this->getSeller($item);

            if (empty($sellerId)) {
                continue;
            }

            $calculationStatus = true;

            if ($item->getProductType() == Type::TYPE_CODE) {
                $productOptions = $item->getProductOptions();
                $calculationStatus = isset($productOptions['product_calculations']);
            }

            if ($calculationStatus) {
                $isShippingFlag = $this->getShippingFlag($item, $sellerId, $isShippingFlag);
                $price = $item->getBasePrice();
                $taxAmount = $item->getBaseTaxAmount();
                $qty = $item->getQtyOrdered();
                $totalAmount = $qty * $price;
                $commission = $this->getCommission($sellerId, $totalAmount, $item, $advanceCommissionRule);
                $actPartnerProCost = $totalAmount - $commission;
            } else {
                if (empty($isShippingFlag[$sellerId])) {
                    $isShippingFlag[$sellerId] = 0;
                }

                $price = 0;
                $taxAmount = 0;
                $qty = $item->getQtyOrdered();
                $totalAmount = 0;
                $commission = 0;
                $actPartnerProCost = 0;
            }

            $salesListModel = $this->salesListFactory->create();
            $salesListModel->setMageproductId($item->getProductId());
            $salesListModel->setOrderItemId($item->getItemId());
            $salesListModel->setParentItemId($item->getParentItemId());
            $salesListModel->setOrderId($lastOrderId);
            $salesListModel->setMagerealorderId($order->getIncrementId());
            $salesListModel->setMagequantity($qty);
            $salesListModel->setSellerId($sellerId);
            $salesListModel->setCpprostatus(\Webkul\Marketplace\Model\Saleslist::PAID_STATUS_PENDING);
            $salesListModel->setMagebuyerId($order->getCustomerId());
            $salesListModel->setMageproPrice($price);
            $salesListModel->setMageproName($item->getName());
            $salesListModel->setTotalAmount($totalAmount > 0 ? $totalAmount : $price);
            $commissionRate = $totalAmount > 0 ? ($commission * 100) / $totalAmount : $percent;
            $salesListModel->setTotalTax($taxAmount);

            if (!$this->marketplaceHelper->isSellerCouponModuleInstalled()) {
                if ($item->getBaseDiscountAmount()) {
                    $salesListModel->setIsCoupon(1);
                    $salesListModel->setAppliedCouponAmount(
                        $item->getBaseDiscountAmount()
                    );

                    if (!isset($sellerCouponArr[$sellerId])) {
                        $sellerCouponArr[$sellerId] = 0;
                    }

                    $sellerCouponArr[$sellerId] = $sellerCouponArr[$sellerId] + $item->getBaseDiscountAmount();
                }
            }

            $salesListModel->setTotalCommission($commission);
            $salesListModel->setActualSellerAmount($actPartnerProCost);
            $salesListModel->setCommissionRate($commissionRate);
            $salesListModel->setCurrencyRate($ratesPerCurrency);

            if (isset($isShippingFlag[$sellerId])) {
                $salesListModel->setIsShipping($isShippingFlag[$sellerId]);
            }

            $this->saleslistResource->save($salesListModel);

            if (!isset($sellerTaxArr[$sellerId])) {
                $sellerTaxArr[$sellerId] = 0;
            }
            if (!isset($sellerProArr[$sellerId])) {
                $sellerProArr[$sellerId] = [];
            }

            $sellerTaxArr[$sellerId] = $sellerTaxArr[$sellerId] + $taxAmount;
            $sellerProArr[$sellerId][] = $item->getProductId();
        }

        return [
            'seller_pro_arr' => $sellerProArr,
            'seller_tax_arr' => $sellerTaxArr,
            'seller_coupon_arr' => $sellerCouponArr
        ];
    }

    /**
     * @param Order\Item $item
     * @return string
     */
    private function getSeller(Order\Item $item): string
    {
        $bySellerOption = $item->getProductOptionByCode(SellerCartDetailsInterface::SELLER_OPTION);

        if (empty($bySellerOption)) {
            return '';
        }

        return $bySellerOption['seller_id'];
    }

    /**
     * @param Order\Item $item
     * @param string $sellerId
     * @param array $isShippingFlag
     * @return array
     */
    private function getShippingFlag(Order\Item $item, string $sellerId, array $isShippingFlag = []): array
    {
        if (
            in_array(
                $item->getProductType(),
                [
                    \Magento\Catalog\Model\Product\Type::TYPE_VIRTUAL,
                    \Magento\Downloadable\Model\Product\Type::TYPE_DOWNLOADABLE
                ]
            )
        ) {
            return $isShippingFlag;
        }

        $isShippingFlag[$sellerId] = !isset($isShippingFlag[$sellerId]) ? 1 : 0;

        return $isShippingFlag;
    }

    /**
     * @param string $sellerId
     * @param float $totalAmount
     * @param Order\Item $item
     * @param mixed $advanceCommissionRule
     * @return float
     */
    private function getCommission(
        string $sellerId,
        float $totalAmount,
        Order\Item $item,
        mixed $advanceCommissionRule
    ): float {
        /*
        * Get Global Commission Rate for Admin
        */
        $percent = $this->marketplaceHelper->getConfigCommissionRate();
        
        $salesPartner = $this->salesPartnerCollectionFactory->create()
            ->getCollection()
            ->addFieldToFilter('seller_id', $sellerId)
            ->addFieldToFilter('commission_status', 1)
            ->addFieldToSelect('commission_rate')
            ->addFieldToSelect('commission_type')
            ->addFieldToSelect('commission_fixed_amount')
            ->getFirstItem();

        $commissionRate = $salesPartner->getCommissionRate();
        $commissionType = $salesPartner->getCommissionType() ?: 'percentage';
        $commissionFixedAmount = $salesPartner->getCommissionFixedAmount() ?: 0;

        $commission = 0;
        if ($commissionRate != null || $commissionFixedAmount > 0) {
            if ($commissionType === 'fixed') {
                // Fixed commission per product - multiply by quantity
                $quantity = $item->getQtyOrdered();
                $commission = $commissionFixedAmount * $quantity;
            } else {
                // Percentage commission
                $commission = ($totalAmount * $commissionRate) / 100;
            }
        } else {
            $commission = ($totalAmount * $percent) / 100;
        }

        $transport = new DataObject();
        $transport->setData([
            'item' => $item,
            'commission_rate' => $commissionRate,
            'commission_type' => $commissionType,
            'commission_fixed_amount' => $commissionFixedAmount,
            'commission' => $commission,
            'total_amount' => $totalAmount,
            'advanced_commission_rate' => $advanceCommissionRule
        ]);
        $this->eventManager->dispatch(
            'marketplace_commission_pre_dispatch',
            [
                'transport' => $transport
            ]
        );

        // Allow plugins to override the commission calculation
        $finalCommission = $transport->getCommission();
        return (float) $finalCommission;
    }
}
