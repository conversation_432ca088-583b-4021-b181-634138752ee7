<?php
declare(strict_types=1);

namespace Comave\Sales\Ui\Component\Listing\Column;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class Sku extends Column
{
    private const SALES_ORDER_ITEM_TABLE = 'sales_order_item';

    /**
     * @param ResourceConnection $resourceConnection
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param array $components
     * @param array $data
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            $orderIds = array_column($dataSource['data']['items'], 'entity_id');
            
            if (!empty($orderIds)) {
                $skuData = $this->getSkuData($orderIds);
                $skusByOrderId = $this->groupDataByOrderId($skuData);

                foreach ($dataSource['data']['items'] as &$item) {
                    $orderId = $item['entity_id'];
                    $skus = $skusByOrderId[$orderId] ?? [];
                    $item[$this->getData('name')] = $this->formatSkuData($skus);
                }
            }
        }

        return $dataSource;
    }

    /**
     * Get SKU data for the specified order IDs.
     *
     * @param array $orderIds
     * @return array
     */
    private function getSkuData(array $orderIds): array
    {
        $connection = $this->resourceConnection->getConnection();

        $select = $connection->select()
            ->from(
                $connection->getTableName(self::SALES_ORDER_ITEM_TABLE),
                [
                    'order_id',
                    'sku',
                    'qty_ordered',
                ]
            )
            ->where('order_id IN (?)', $orderIds)
            ->where('parent_item_id IS NULL');

        return $connection->fetchAll($select);
    }

    /**
     * Group data by order ID.
     *
     * @param array $data
     * @return array
     */
    private function groupDataByOrderId(array $data): array
    {
        $grouped = [];
        foreach ($data as $item) {
            $orderId = $item['order_id'];
            if (!isset($grouped[$orderId])) {
                $grouped[$orderId] = [];
            }
            $grouped[$orderId][] = $item;
        }
        return $grouped;
    }

    /**
     * Format SKU data for display.
     *
     * @param array $skus
     * @return string
     */
    private function formatSkuData(array $skus): string
    {
        if (empty($skus)) {
            return '';
        }

        $formatted = [];
        foreach ($skus as $item) {
            $qty = (int)$item['qty_ordered'];
            $formatted[] = '<div>' . $item['sku'] . ($qty > 1 ? ' (x' . $qty . ')' : '') . '</div>';
        }

        return implode('', $formatted);
    }
}
