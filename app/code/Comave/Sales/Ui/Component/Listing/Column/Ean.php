<?php
declare(strict_types=1);

namespace Comave\Sales\Ui\Component\Listing\Column;

use Magento\Catalog\Model\Product;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Psr\Log\LoggerInterface;

class Ean extends Column
{
    private const SALES_ORDER_ITEM_TABLE = 'sales_order_item';

    /**
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param array $components
     * @param array $data
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            $orderIds = array_column($dataSource['data']['items'], 'entity_id');

            if (!empty($orderIds)) {
                // Quick diagnostic
                $this->runDiagnostic();

                $eanData = $this->getEanData($orderIds);
                $eansByOrderId = $this->groupDataByOrderId($eanData);

                foreach ($dataSource['data']['items'] as &$item) {
                    $orderId = $item['entity_id'];
                    $eans = $eansByOrderId[$orderId] ?? [];
                    $formattedEan = $this->formatEanData($eans);

                    // Debug: Show debug info
                    if (empty($formattedEan)) {
                        $item[$this->getData('name')] = 'Debug: Order ' . $orderId . ' - ' . count($eans) . ' EAN records';
                    } else {
                        $item[$this->getData('name')] = $formattedEan;
                    }
                }
            }
        }

        return $dataSource;
    }

    /**
     * Get EAN data for the specified order IDs.
     *
     * @param array $orderIds
     * @return array
     */
    private function getEanData(array $orderIds): array
    {
        $eanAttrId = $this->getEanAttributeId();
        if (!$eanAttrId) {
            // Debug: Log that EAN attribute was not found
            $this->logger->debug('EAN attribute not found in system');
            return [];
        }

        $connection = $this->resourceConnection->getConnection();

        $linkField = $this->hasRowIdColumn() ? 'row_id' : 'entity_id';

        $joinCondition = $connection->quoteInto(
            sprintf('cpe.%s = cpev.%s AND cpev.attribute_id = ? AND cpev.store_id = 0', $linkField, $linkField),
            $eanAttrId
        );

        $select = $connection->select()
            ->from(['soi' => $connection->getTableName(self::SALES_ORDER_ITEM_TABLE)], ['order_id', 'qty_ordered', 'product_id'])
            ->joinLeft(
                ['cpe' => $connection->getTableName('catalog_product_entity')],
                'soi.product_id = cpe.entity_id',
                [$linkField]
            )
            ->joinLeft(
                ['cpev' => $connection->getTableName('catalog_product_entity_varchar')],
                $joinCondition,
                ['value as ean']
            )
            ->where('soi.order_id IN (?)', $orderIds)
            ->where('soi.parent_item_id IS NULL');

        $result = $connection->fetchAll($select);

        // Debug: Log the query and result count
        $this->logger->debug('EAN Query: ' . $select->__toString());
        $this->logger->debug('EAN Results count: ' . count($result));

        return $result;
    }

    /**
     * Group data by order ID.
     *
     * @param array $data
     * @return array
     */
    private function groupDataByOrderId(array $data): array
    {
        $grouped = [];
        foreach ($data as $item) {
            $orderId = $item['order_id'];
            if (!isset($grouped[$orderId])) {
                $grouped[$orderId] = [];
            }
            $grouped[$orderId][] = $item;
        }
        return $grouped;
    }

    /**
     * Format EAN data for display.
     *
     * @param array $eans
     * @return string
     */
    private function formatEanData(array $eans): string
    {
        if (empty($eans)) {
            return '';
        }

        $formatted = [];
        foreach ($eans as $item) {
            if (!empty($item['ean'])) {
                $qty = (int)$item['qty_ordered'];
                $formatted[] = $item['ean'] . ($qty > 1 ? ' (x' . $qty . ')' : '');
            }
        }

        return implode(', ', $formatted);
    }

    /**
     * Get EAN attribute ID.
     *
     * @return int|null
     */
    private function getEanAttributeId(): ?int
    {
        $connection = $this->resourceConnection->getConnection();

        try {
            // First, let's check what EAN-related attributes exist
            $debugSelect = $connection->select()
                ->from(
                    ['ea' => $connection->getTableName('eav_attribute')],
                    ['attribute_code', 'attribute_id']
                )
                ->where('ea.entity_type_id = ?', Product::ENTITY)
                ->where('ea.attribute_code LIKE ?', '%ean%');

            $eanAttributes = $connection->fetchAll($debugSelect);
            $this->logger->debug('Available EAN attributes: ' . json_encode($eanAttributes));

            $select = $connection->select()
                ->from(
                    ['ea' => $connection->getTableName('eav_attribute')],
                    ['attribute_id']
                )
                ->where('ea.entity_type_id = ?', Product::ENTITY)
                ->where('ea.attribute_code = ?', 'ean');

            $result = $connection->fetchOne($select) ?: null;
            $this->logger->debug('EAN attribute ID found: ' . ($result ?: 'NULL'));

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('Error getting EAN attribute: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if catalog_product_entity has row_id column.
     *
     * @return bool
     */
    private function hasRowIdColumn(): bool
    {
        $connection = $this->resourceConnection->getConnection();

        return $connection->tableColumnExists(
            $connection->getTableName('catalog_product_entity'),
            'row_id'
        );
    }

    /**
     * Run diagnostic to check EAN setup
     */
    private function runDiagnostic(): void
    {
        static $diagnosticRun = false;
        if ($diagnosticRun) {
            return;
        }
        $diagnosticRun = true;

        $connection = $this->resourceConnection->getConnection();

        // Check if any products have EAN values
        $eanAttrId = $this->getEanAttributeId();
        if ($eanAttrId) {
            $select = $connection->select()
                ->from($connection->getTableName('catalog_product_entity_varchar'), ['COUNT(*)'])
                ->where('attribute_id = ?', $eanAttrId)
                ->where('value IS NOT NULL')
                ->where('value != ""');

            $count = $connection->fetchOne($select);
            $this->logger->debug("EAN Diagnostic: Found {$count} products with EAN values");
        }
    }
}
