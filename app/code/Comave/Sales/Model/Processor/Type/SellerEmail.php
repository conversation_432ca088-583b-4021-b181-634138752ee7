<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Processor\Type;

use Comave\Sales\Model\Service\InvoiceManager;
use Comave\Sales\Api\PostProcessorInterface;
use Exception;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Directory\Model\CountryFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Event\Manager as EventManager;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Module\Manager;
use Magento\Framework\Session\SessionManager;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order\AddressFactory;
use Magento\Sales\Model\Order\Item;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Api\Data\SaleslistInterface;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Webkul\Marketplace\Helper\Email as MpEmailHelper;
use Webkul\Marketplace\Helper\Notification as NotificationHelper;
use Webkul\Marketplace\Helper\Orders as OrdersHelper;
use Webkul\Marketplace\Model\Notification;
use Webkul\Marketplace\Model\OrderPendingMailsFactory;
use Webkul\Marketplace\Model\OrdersFactory;
use Webkul\Marketplace\Model\ProductFactory;
use Webkul\Marketplace\Model\SaleperpartnerFactory;
use Webkul\Marketplace\Model\Saleslist;
use Webkul\Marketplace\Model\SaleslistFactory;
use Webkul\MpAdvancedCommission\Helper\Data as AdvancedCommissionHelper;
use Webkul\MpAdvancedCommission\Model\CommissionRulesFactory;

class SellerEmail implements PostProcessorInterface
{
    /**
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Framework\Event\Manager $eventManager
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Webkul\Marketplace\Helper\Data $marketplaceHelper
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $date
     * @param \Webkul\Marketplace\Model\SaleslistFactory $salesListFactory
     * @param \Magento\Sales\Model\Order\AddressFactory $orderAddressFactory
     * @param \Magento\Directory\Model\CountryFactory $countryFactory
     * @param \Webkul\Marketplace\Model\OrdersFactory $ordersFactory
     * @param \Webkul\Marketplace\Helper\Email $marketplaceEmailHelper
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
     * @param \Magento\Framework\Session\SessionManager $coreSession
     * @param \Webkul\Marketplace\Helper\Orders $ordersHelper
     * @param \Webkul\Marketplace\Model\OrderPendingMailsFactory $orderPendingMailsFactory
     * @param \Webkul\Marketplace\Helper\Notification $notificationHelper
     * @param \Webkul\Marketplace\Model\ProductFactory $productFactory
     * @param \Magento\Framework\Module\Manager $moduleManager
     * @param \Webkul\Marketplace\Model\SaleperpartnerFactory $partnerSalesFactory
     * @param \Webkul\MpAdvancedCommission\Helper\Data $commissionHelper
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly EventManager $eventManager,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly CartRepositoryInterface $quoteRepository,
        private readonly MarketplaceHelper $marketplaceHelper,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly DateTime $date,
        private readonly SaleslistFactory $salesListFactory,
        private readonly AddressFactory $orderAddressFactory,
        private readonly CountryFactory $countryFactory,
        private readonly OrdersFactory $ordersFactory,
        private readonly MpEmailHelper $marketplaceEmailHelper,
        private readonly Session $customerSession,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly SessionManager $coreSession,
        private readonly OrdersHelper $ordersHelper,
        private readonly OrderPendingMailsFactory $orderPendingMailsFactory,
        private readonly NotificationHelper $notificationHelper,
        private readonly ProductFactory $productFactory,
        private readonly Manager $moduleManager,
        private readonly SaleperpartnerFactory $partnerSalesFactory,
        private readonly AdvancedCommissionHelper $commissionHelper,
        private readonly CommissionRulesFactory $commissionRulesFactory,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly InvoiceManager $invoiceManager
    ) {
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return void
     */
    public function process(OrderInterface $order): void
    {
        try {
            if (!$this->isMultiShipping($order)) {
                $this->sendEmail($order, (int)$order->getId());
            } else {
                /**
                 * @todo Cook a solution for async processing; cannot use session data.
                 * @see \Webkul\Mpsplitorder\Model\QuoteManagement::placeOrder()
                 */
                $orderIds = $this->coreSession->getOrderIds() ?? [];
                foreach ($orderIds as $ids => $orderIncId) {
                    $lastOrderId = $ids;
                    $order = $this->orderRepository->get($lastOrderId);
                    $this->sendEmail($order, $lastOrderId);
                }
            }
        } catch (LocalizedException $exception) {
            $this->logger->critical(sprintf("Unable to send email to seller for order #%s", $order->getId()), [
                'exception' => $exception->getMessage(),
                'order_id' => $order->getIncrementId(),
            ]);
        }
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return bool
     */
    private function isMultiShipping(OrderInterface $order): bool
    {
        try {
            $quote = $this->quoteRepository->get($order->getQuoteId());
        } catch (NoSuchEntityException $exception) {
            $this->logger->critical($exception->getMessage(), [
                'order' => $order->getIncrementId(),
            ]);

            return false;
        }

        return (bool)$quote->getIsMultiShipping();
    }

    /**
     * Send order mail notification to Seller
     *
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param int $lastOrderId
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function sendEmail(OrderInterface $order, int $lastOrderId): void
    {
        $this->applyProductSalesCalculation($order);

        $paymentCode = $order->getPayment()->getMethod() ?? '';

        $shippingInfo = '';
        $shippingDes = '';

        $billingId = $order->getBillingAddress()->getId();
        $billaddress = $this->orderAddressFactory->create()->load($billingId);
        $billinginfo = $billaddress['firstname'].'<br/>'.
            $billaddress['street'].'<br/>'.
            $billaddress['city'].' '.
            $billaddress['region'].' '.
            $billaddress['postcode'].'<br/>'.
            $this->countryFactory->create()->load($billaddress['country_id'])->getName().'<br/>T:'.
            $billaddress['telephone'];

        $order->setOrderApprovalStatus(1)->save();

        $payment = $order->getPayment()->getMethodInstance()->getTitle();

        if ($order->getShippingAddress()) {
            $shippingId = $order->getShippingAddress()->getId();
            $address = $this->orderAddressFactory->create()->load($shippingId);
            $shippingInfo = $address['firstname'].'<br/>'.
                $address['street'].'<br/>'.
                $address['city'].' '.
                $address['region'].' '.
                $address['postcode'].'<br/>'.
                $this->countryFactory->create()->load($address['country_id'])->getName().'<br/>T:'.
                $address['telephone'];
            $shippingDes = $order->getShippingDescription();
        }

        $adminStoremail = $this->marketplaceHelper->getAdminEmailId();
        $defaultTransEmailId = $this->marketplaceHelper->getDefaultTransEmailId();
        $adminEmail = $adminStoremail ?: $defaultTransEmailId;
        $adminUsername = $this->marketplaceHelper->getAdminName();

        $sellerOrder = $this->ordersFactory->create()
            ->getCollection()
            ->addFieldToFilter('order_id', $lastOrderId)
            ->addFieldToFilter('seller_id', ['neq' => 0]);
        foreach ($sellerOrder as $info) {
            $userdata = $this->customerRepository->getById((int)$info['seller_id']);
            $username = $userdata->getFirstname();
            $useremail = $userdata->getEmail();

            $senderInfo = [];
            $receiverInfo = [];

            $receiverInfo = [
                'name' => $username,
                'email' => $useremail,
            ];
            $senderInfo = [
                'name' => $adminUsername,
                'email' => $adminEmail,
            ];
            $totalprice = 0;
            $totalTaxAmount = 0;
            $codCharges = 0;
            $shippingCharges = 0;
            $orderInfo = '';

            $saleslistIds = [];
            $collection1 = $this->salesListFactory->create()
                ->getCollection()
                ->addFieldToFilter('order_id', $lastOrderId)
                ->addFieldToFilter('seller_id', $info['seller_id'])
                ->addFieldToFilter('parent_item_id', ['null' => 'true'])
                ->addFieldToFilter('magerealorder_id', ['neq' => 0])
                ->addFieldToSelect('entity_id');

            $saleslistIds = $collection1->getData();

            $fetchsale = $this->salesListFactory->create()
                ->getCollection()
                ->addFieldToFilter(
                    'entity_id',
                    ['in' => $saleslistIds]
                );
            $fetchsale->getSellerOrderCollection();
            foreach ($fetchsale as $res) {
                $productName = $res->getMageproName();
                $result = [];
                $result = $this->getProductOptionData($res, $result);
                $productName = $this->getProductNameHtml($result, $productName);
                if ($res->getProductType() == 'configurable') {
                    $configurableSalesItem = $this->salesListFactory->create()
                        ->getCollection()
                        ->addFieldToFilter('order_id', $lastOrderId)
                        ->addFieldToFilter('seller_id', $info['seller_id'])
                        ->addFieldToFilter('parent_item_id', $res->getOrderItemId());
                    $configurableItemArr = $configurableSalesItem->getOrderedProductId();
                    $configurableItemId = $res['mageproduct_id'];
                    if (!empty($configurableItemArr)) {
                        $configurableItemId = $configurableItemArr[0];
                    }
                    $product = $this->productRepository->getById($configurableItemId);
                } else {
                    $product = $this->productRepository->getById($res['mageproduct_id']);
                }

                $sku = $product->getSku();
                $orderInfo = $orderInfo."<tbody><tr>
                                <td class='item-info'>".$productName."</td>
                                <td class='item-info'>".$sku."</td>
                                <td class='item-qty'>".($res['magequantity'] * 1)."</td>
                                <td class='item-price'>".
                    $order->formatPrice(
                        $res['magepro_price'] * $res['magequantity']
                    ).
                    '</td>
                             </tr></tbody>';
                $totalTaxAmount = $totalTaxAmount + $res['total_tax'];
                $totalprice = $totalprice + ($res['magepro_price'] * $res['magequantity']);

                /*
                * Low Stock Notification mail to seller
                */
                if ($this->marketplaceHelper->getlowStockNotification()) {
                    if (!empty($product['quantity_and_stock_status']['qty'])) {
                        $stockItemQty = $product['quantity_and_stock_status']['qty'];
                    } else {
                        $stockItemQty = $product->getQty();
                    }
                    if ($stockItemQty <= $this->marketplaceHelper->getlowStockQty()) {
                        $orderProductInfo = "<tbody><tr>
                                <td class='item-info'>".$productName."</td>
                                <td class='item-info'>".$sku."</td>
                                <td class='item-qty'>".($stockItemQty * 1).'</td>
                             </tr></tbody>';

                        $emailTemplateVariables = [];
                        $emailTemplateVariables['myvar1'] = $orderProductInfo;
                        $emailTemplateVariables['myvar2'] = $username;

                        $this->marketplaceEmailHelper->sendLowStockNotificationMail(
                            $emailTemplateVariables,
                            $senderInfo,
                            $receiverInfo
                        );
                    }
                }
            }
            $shippingCharges = $info->getShippingCharges();
            $couponAmount = $info->getCouponAmount();
            $totalCod = 0;

            if ($paymentCode == 'mpcashondelivery') {
                $totalCod = $info->getCodCharges();
                $codRow = "<tr class='subtotal'>
                            <th colspan='3'>".__('Cash On Delivery Charges')."</th>
                            <td colspan='3'><span>".
                    $order->formatPrice($totalCod).
                    '</span></td>
                            </tr>';
            } else {
                $codRow = '';
            }
            $discount_description = $order->getDiscountDescription();
            $discountLabel = '';
            if ($discount_description !== "") {
                $discountLabel = " (".$discount_description.")";
            }
            $orderInfo = $orderInfo."<tfoot class='order-totals'>
                                <tr class='subtotal'>
                                    <th colspan='3'>".__('Shipping & Handling Charges')."</th>
                                    <td colspan='3'><span>".
                $order->formatPrice($shippingCharges)."</span></td>
                                </tr>
                                <tr class='subtotal'>
                                    <th colspan='3'>".__('Discount').$discountLabel."</th>
                                    <td colspan='3'><span> -".
                $order->formatPrice($couponAmount).
                "</span></td>
                                </tr>
                                <tr class='subtotal'>
                                    <th colspan='3'>".__('Tax Amount')."</th>
                                    <td colspan='3'><span>".
                $order->formatPrice($totalTaxAmount).'</span></td>
                                </tr>'.$codRow."
                                <tr class='subtotal'>
                                    <th colspan='3'>".__('Grandtotal')."</th>
                                    <td colspan='3'><span>".
                $order->formatPrice(
                    $totalprice +
                    $totalTaxAmount +
                    $shippingCharges +
                    $totalCod -
                    $couponAmount
                ).'</span></td>
                                </tr></tfoot>';

            $emailTemplateVariables = [];
            if ($shippingInfo != '') {
                $isNotVirtual = 1;
            } else {
                $isNotVirtual = 0;
            }
            $emailTempVariables['myvar1'] = $order->getRealOrderId();
            $emailTempVariables['myvar2'] = $order['created_at'];
            $emailTempVariables['myvar4'] = $billinginfo;
            $emailTempVariables['myvar5'] = $payment;
            $emailTempVariables['myvar6'] = $shippingInfo;
            $emailTempVariables['isNotVirtual'] = $isNotVirtual;
            $emailTempVariables['myvar9'] = $shippingDes;
            $emailTempVariables['myvar8'] = $orderInfo;
            $emailTempVariables['myvar3'] = $username;

            if ($this->marketplaceHelper->getOrderApprovalRequired()) {
                $emailTempVariables['seller_id'] = $info['seller_id'];
                $emailTempVariables['order_id'] = $lastOrderId;
                $emailTempVariables['sender_name'] = $senderInfo['name'];
                $emailTempVariables['sender_email'] = $senderInfo['email'];
                $emailTempVariables['receiver_name'] = $receiverInfo['name'];
                $emailTempVariables['receiver_email'] = $receiverInfo['email'];

                $orderPendingMailsCollection = $this->orderPendingMailsFactory->create();
                $orderPendingMailsCollection->setData($emailTempVariables);
                $orderPendingMailsCollection->setCreatedAt($this->date->gmtDate());
                $orderPendingMailsCollection->setUpdatedAt($this->date->gmtDate());
                $orderPendingMailsCollection->save();
                $order->setOrderApprovalStatus(0)->save();
            } else {
                $this->marketplaceEmailHelper->sendPlacedOrderEmail(
                    $emailTempVariables,
                    $senderInfo,
                    $receiverInfo
                );
            }
        }
    }

    /**
     * Seller Product Sales Calculation Method.
     *
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return void
     */
    public function applyProductSalesCalculation(OrderInterface $order): void
    {
        /**
         * Marketplace Order details save before Observer
         */
        $this->eventManager->dispatch('mp_order_save_before', ['order' => $order]);
        /**
         * Get Current Store Currency Rate
         */
        $currentCurrencyCode = $order->getOrderCurrencyCode();
        $baseCurrencyCode = $order->getBaseCurrencyCode();
        $allowedCurrencies = $this->marketplaceHelper->getConfigAllowCurrencies();
        $rates = $this->marketplaceHelper->getCurrencyRates(
            $baseCurrencyCode,
            array_values($allowedCurrencies)
        );
        if (empty($rates[$currentCurrencyCode])) {
            $rates[$currentCurrencyCode] = 1;
        }

        $lastOrderId = $order->getId();

        /**
         * Marketplace Credit Management module Observer
         */
        $this->eventManager->dispatch('mp_discount_manager', ['order' => $order]);
        $this->eventManager->dispatch('mp_advance_commission_rule', ['order' => $order]);

        $sellerData = $this->getSellerProductData($order, $rates[$currentCurrencyCode]);

        $sellerProArr = $sellerData['seller_pro_arr'];
        $sellerTaxArr = $sellerData['seller_tax_arr'];
        $sellerCouponArr = $sellerData['seller_coupon_arr'];

        $taxToSeller = $this->marketplaceHelper->getConfigTaxManage();
        $shippingAll = [
            'title' => $order->getShippingDescription(),
            'cost' => $order->getShippingAmount(),
        ];

        try {
            $shippingAllCount = 0;
            if (is_array($shippingAll)) {
                $shippingAllCount = count($shippingAll);
            }
        } catch (Exception $e) {
            $this->marketplaceHelper->logDataInLogger(
                sprintf("Observer_SalesOrderPlaceAfterObserver productSalesCalculation : %s", $e->getMessage())
            );
            $shippingAllCount = false;
        }
        foreach ($sellerProArr as $key => $value) {
            if ($key) {
                $productIds = implode(',', $value);
                $data = [
                    'order_id' => $lastOrderId,
                    'product_ids' => $productIds,
                    'seller_id' => $key,
                    'total_tax' => $sellerTaxArr[$key],
                    'tax_to_seller' => $taxToSeller,
                ];
                if (!$shippingAllCount && $key == 0) {
                    $shippingCharges = $order->getBaseShippingAmount();
                    $data = [
                        'order_id' => $lastOrderId,
                        'product_ids' => $productIds,
                        'seller_id' => $key,
                        'shipping_charges' => $shippingCharges,
                        'total_tax' => $sellerTaxArr[$key],
                        'tax_to_seller' => $taxToSeller,
                    ];
                }
                if (!empty($sellerCouponArr) && !empty($sellerCouponArr[$key])) {
                    $data['coupon_amount'] = $sellerCouponArr[$key];
                }
                $collection = $this->ordersFactory->create();
                $collection->setData($data);
                $collection->setCreatedAt($this->date->gmtDate());
                $collection->setSellerPendingNotification(1);
                $collection->setUpdatedAt($this->date->gmtDate());
                if ((int) $collection->getInvoiceId() === 0) {
                    $invoice = $this->invoiceManager->getPaidInvoiceForOrder((int) $order->getId());
                    if ($invoice) {
                        $collection->setInvoiceId((int) $invoice->getEntityId());
                    }
                }
                $collection->save();
                $sellerOrderId = $collection->getId();
                $this->notificationHelper->saveNotification(
                    Notification::TYPE_ORDER,
                    $sellerOrderId,
                    $lastOrderId
                );
            }
        }
        /**
         * Marketplace Order details save after Observer
         */
        $this->eventManager->dispatch('mp_order_save_after', ['order' => $order]);
    }

    /**
     * Get Seller's Product Data.
     *
     * @param \Magento\Sales\Model\Order $order
     * @param $ratesPerCurrency
     * @return array
     */
    public function getSellerProductData(OrderInterface $order, $ratesPerCurrency): array
    {
        $lastOrderId = $order->getId();

        $sellerProArr = [];
        $sellerTaxArr = [];
        $sellerCouponArr = [];
        $isShippingFlag = [];

        foreach ($order->getAllItems() as $item) {
            $itemData = $item->getData();
            $sellerId = $this->getSellerIdPerProduct($item);
            if ($sellerId && $sellerId != '') {
                $calculationStatus = true;
                if ($itemData['product_type'] == 'bundle') {
                    $productOptions = $item->getProductOptions();
                    $calculationStatus = !empty($productOptions['product_calculations']);
                }
                if ($calculationStatus) {
                    $isShippingFlag = $this->getShippingFlag($item, $sellerId, $isShippingFlag);

                    $price = $itemData['base_price'];

                    $taxamount = $itemData['base_tax_amount'];
                    $qty = $item->getQtyOrdered();

                    $totalamount = $qty * $price;

                    $advanceCommissionRule = $this->getAdvanceCommissionRule($order);
                    $commission = $this->getCommission($sellerId, $totalamount, $item, $advanceCommissionRule);

                    $actparterprocost = $totalamount - $commission;
                } else {
                    if (empty($isShippingFlag[$sellerId])) {
                        $isShippingFlag[$sellerId] = 0;
                    }
                    $price = 0;
                    $taxamount = 0;
                    $qty = $item->getQtyOrdered();
                    $totalamount = 0;
                    $commission = 0;
                    $actparterprocost = 0;
                }

                $collectionsave = $this->salesListFactory->create();
                $collectionsave->setMageproductId($item->getProductId());
                $collectionsave->setOrderItemId($item->getItemId());
                $collectionsave->setParentItemId($item->getParentItemId());
                $collectionsave->setOrderId($lastOrderId);
                $collectionsave->setMagerealorderId($order->getIncrementId());
                $collectionsave->setMagequantity($qty);
                $collectionsave->setSellerId($sellerId);
                $collectionsave->setCpprostatus(Saleslist::PAID_STATUS_PENDING);
                $collectionsave->setMagebuyerId($order->getCustomerId());
                $collectionsave->setMageproPrice($price);
                $collectionsave->setMageproName($item->getName());
                if ($totalamount != 0) {
                    $collectionsave->setTotalAmount($totalamount);
                    $commissionRate = ($commission * 100) / $totalamount;
                } else {
                    $collectionsave->setTotalAmount($price);
                    /**
                     * Get Global Commission Rate for Admin
                     */
                    $commissionRate = $this->marketplaceHelper->getConfigCommissionRate();
                }
                $collectionsave->setTotalTax($taxamount);
                if (!$this->marketplaceHelper->isSellerCouponModuleInstalled()) {
                    if (isset($itemData['base_discount_amount'])) {
                        $baseDiscountAmount = $itemData['base_discount_amount'];
                        $collectionsave->setIsCoupon(1);
                        $collectionsave->setAppliedCouponAmount($baseDiscountAmount);

                        if (!isset($sellerCouponArr[$sellerId])) {
                            $sellerCouponArr[$sellerId] = 0;
                        }
                        $sellerCouponArr[$sellerId] = $sellerCouponArr[$sellerId] + $baseDiscountAmount;
                    }
                }
                $collectionsave->setTotalCommission($commission);
                $collectionsave->setActualSellerAmount($actparterprocost);
                $collectionsave->setCommissionRate($commissionRate);
                $collectionsave->setCurrencyRate($ratesPerCurrency);
                if (isset($isShippingFlag[$sellerId])) {
                    $collectionsave->setIsShipping($isShippingFlag[$sellerId]);
                }
                $collectionsave->setCreatedAt($this->date->gmtDate());
                $collectionsave->setUpdatedAt($this->date->gmtDate());
                $collectionsave->save();
                $qty = 0;
                if (!isset($sellerTaxArr[$sellerId])) {
                    $sellerTaxArr[$sellerId] = 0;
                }
                $sellerTaxArr[$sellerId] = $sellerTaxArr[$sellerId] + $taxamount;
                if ($price != 0.0000) {
                    if (!isset($sellerProArr[$sellerId])) {
                        $sellerProArr[$sellerId] = [];
                    }
                    array_push($sellerProArr[$sellerId], $item->getProductId());
                } else {
                    if (!$item->getParentItemId()) {
                        if (!isset($sellerProArr[$sellerId])) {
                            $sellerProArr[$sellerId] = [];
                        }
                        array_push($sellerProArr[$sellerId], $item->getProductId());
                    }
                }
            }
        }

        return [
            'seller_pro_arr' => $sellerProArr,
            'seller_tax_arr' => $sellerTaxArr,
            'seller_coupon_arr' => $sellerCouponArr,
        ];
    }

    /**
     * @param \Webkul\Marketplace\Api\Data\SaleslistInterface $item
     * @param array $result
     * @return array
     */
    public function getProductOptionData(SaleslistInterface $item, array $result = []): array
    {
        $productOptionsData = $this->ordersHelper->getProductOptions($item->getProductOptions());
        if ($options = $productOptionsData) {
            if (isset($options['options'])) {
                $result = array_merge($result, $options['options']);
            }
            if (isset($options['additional_options'])) {
                $result = array_merge($result, $options['additional_options']);
            }
            if (isset($options['attributes_info'])) {
                $result = array_merge($result, $options['attributes_info']);
            }
        }

        return $result;
    }

    /**
     * Get Order Product Name Html Data Method.
     *
     * @param array $result
     * @param string $productName
     *
     * @return string
     */
    public function getProductNameHtml(array $result, string $productName): string
    {
        if ($_options = $result) {
            $proOptionData = '<dl class="item-options">';
            foreach ($_options as $_option) {
                $proOptionData .= sprintf("<dt>%s</dt>", $_option['label']);
                $proOptionData .= sprintf("<dd>%s", $_option['value']);
                $proOptionData .= '</dd>';
            }
            $proOptionData .= '</dl>';
            $productName = sprintf("%s<br/>%s", $productName, $proOptionData);
        } else {
            $productName = sprintf("%s<br/>", $productName);
        }

        return $productName;
    }

    /**
     * Get Seller ID Per Product.
     *
     * @param \Magento\Sales\Model\Order\Item $item
     *
     * @return int
     */
    public function getSellerIdPerProduct(Item $item): int
    {
        $infoBuyRequest = $item->getProductOptionByCode('info_buyRequest');
        $mpassignproductId = $infoBuyRequest['mpassignproduct_id'] ?? 0;
        if ($mpassignproductId) {
            $sellerId = $this->marketplaceHelper->getSellerIdByProductId($mpassignproductId);
        } elseif (array_key_exists('seller_id', $infoBuyRequest)) {
            $sellerId = $infoBuyRequest['seller_id'];
        } else {
            $sellerId = '';
        }
        if ($sellerId == '') {
            $collectionProduct = $this->productFactory->create()
                ->getCollection()
                ->addFieldToFilter(
                    'mageproduct_id',
                    $item->getProductId()
                );
            foreach ($collectionProduct as $value) {
                $sellerId = $value->getSellerId();
            }
        }
        if ($sellerId == '') {
            $sellerId = 0;
        }

        return (int)$sellerId;
    }

    /**
     * Get Shipping Flag Per Seller Method.
     *
     * @param \Magento\Sales\Model\Order\Item $item
     * @param int $sellerId
     * @param array $isShippingFlag
     *
     * @return array
     */
    public function getShippingFlag(Item $item, int $sellerId, array $isShippingFlag = []): array
    {
        $shippingFlag = [];
        if (($item->getProductType() != 'virtual') && ($item->getProductType() != 'downloadable')) {
            $shippingFlag[$sellerId] = (int)!isset($isShippingFlag[$sellerId]);
        }

        return $shippingFlag;
    }

    /**
     * @param int $sellerId
     * @param float $totalAmount
     * @param \Magento\Sales\Model\Order\Item $item
     * @param array $advanceCommissionRule
     * @return float
     */
    public function getCommission(int $sellerId, float $totalAmount, Item $item, array $advanceCommissionRule): float
    {
        /**
         * Get Global Commission Rate for Admin
         */
        $percent = $this->marketplaceHelper->getConfigCommissionRate();

        $salesPartner = $this->partnerSalesFactory->create()
            ->getCollection()
            ->addFieldToFilter(
                'seller_id',
                $sellerId
            )
            ->addFieldToFilter(
                'commission_status',
                1
            )
            ->addFieldToSelect(
                'commission_rate'
            )
            ->addFieldToSelect(
                'commission_type'
            )
            ->addFieldToSelect(
                'commission_fixed_amount'
            )->getFirstItem();

        $commissionRate = $salesPartner->getCommissionRate();
        $commissionType = $salesPartner->getCommissionType() ?: 'percentage';
        $commissionFixedAmount = $salesPartner->getCommissionFixedAmount() ?: 0;

        if ($commissionRate != null || $commissionFixedAmount > 0) {
            if ($commissionType === 'fixed') {
                // Fixed commission per product - multiply by quantity
                $quantity = $item->getQtyOrdered();
                $commission = $commissionFixedAmount * $quantity;
            } else {
                // Percentage commission
                $commission = ($totalAmount * $commissionRate) / 100;
            }
        } else {
            $commission = ($totalAmount * $percent) / 100;
        }
        if ($this->moduleManager->isOutputEnabled('Webkul_MpAdvancedCommission')) {
            if (!$this->marketplaceHelper->getUseCommissionRule()) {
                $wholeData['id'] = $item->getProductId();
                $this->eventManager->dispatch('mp_advance_commission', [$wholeData]);

                /**
                 * @todo Find where the commission session data is set and use that logic instead of getting the value from session
                 */
                $advanceCommission = $this->customerSession->getData('commission');
                if (!empty($advanceCommission)) {
                    $percent = $advanceCommission;
                    $commType = $this->marketplaceHelper->getCommissionType();
                    if ($commType == 'fixed') {
                        $commission = $percent;
                    } else {
                        $commission = ($totalAmount * $advanceCommission) / 100;
                    }
                    if ($commission > $totalAmount) {
                        $commission = $totalAmount * $this->marketplaceHelper->getConfigCommissionRate() / 100;
                    }
                }
            } else {
                if (count($advanceCommissionRule) && isset($advanceCommissionRule[$item->getId()])) {
                    if ($advanceCommissionRule[$item->getId()]['type'] == 'fixed') {
                        $commission = $advanceCommissionRule[$item->getId()]['amount'];
                    } else {
                        $commission =
                            ($totalAmount * $advanceCommissionRule[$item->getId()]['amount']) / 100;
                    }
                }
            }
        }

        return (float)$commission;
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return array
     */
    private function getAdvanceCommissionRule(OrderInterface $order): array
    {
        $commission = [];
        if ($this->commissionHelper->getUseCommissionRule()) {
            $sellerData = $this->commissionHelper->getSellerData($order);
            $isPriceRoundOff = $this->scopeConfig->getValue(
                'mpadvancedcommission/options/commission_calculation',
                ScopeInterface::SCOPE_STORE
            );
            foreach ($sellerData as $sellerId => $row) {
                foreach ($row['details'] as $item) {
                    $commissionRuleCollection = $this->commissionRulesFactory->create()->getCollection();
                    if ($isPriceRoundOff) {
                        $commissionRuleCollection = $commissionRuleCollection
                            ->addFieldToFilter(
                                "price_from",
                                ["lteq" => round($item['price'])]
                            )
                            ->addFieldToFilter(
                                "price_to",
                                ["gteq" => round($item['price'])]
                            );
                    } else {
                        $commissionRuleCollection = $commissionRuleCollection
                            ->addFieldToFilter(
                                "price_from",
                                ["lteq" => $item['price']]
                            )
                            ->addFieldToFilter(
                                "price_to",
                                ["gteq" => $item['price']]
                            );
                    }

                    if (empty($commissionRuleCollection->getSize())) {
                        if ($isPriceRoundOff) {
                            $commissionRuleCollection = $commissionRuleCollection
                                ->addFieldToFilter(
                                    "price_from",
                                    ["lteq" => round($row['total'])]
                                )
                                ->addFieldToFilter(
                                    "price_to",
                                    ["gteq" => round($row['total'])]
                                );
                        } else {
                            $commissionRuleCollection = $commissionRuleCollection
                                ->addFieldToFilter(
                                    "price_from",
                                    ["lteq" => $row['total']]
                                )
                                ->addFieldToFilter(
                                    "price_to",
                                    ["gteq" => $row['total']]
                                );
                        }
                    }
                    foreach ($commissionRuleCollection as $commissionRule) {
                        if ($commissionRule->getCommissionType() != "percent") {
                            if ($item['price'] > 0) {
                                $commission[$item['item_id']] = [
                                    "amount" => $commissionRule->getAmount() > $row['total'] ?
                                        $row['total'] : $commissionRule->getAmount() * $item['qty'],
                                    "type" => $commissionRule->getCommissionType(),
                                ];
                            } else {
                                $commission[$item['item_id']] = [
                                    "amount" => 0,
                                    "type" => $commissionRule->getCommissionType(),
                                ];
                            }

                        } else {
                            $commission[$item['item_id']] = [
                                "amount" => $commissionRule->getAmount(),
                                "type" => $commissionRule->getCommissionType(),
                            ];
                        }
                        break;
                    }
                }
            }
        }

        return $commission;
    }
}