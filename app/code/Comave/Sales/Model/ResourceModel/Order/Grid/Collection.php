<?php
declare(strict_types=1);

namespace Comave\Sales\Model\ResourceModel\Order\Grid;

use Magento\Catalog\Model\Product;
use Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult;
use Magento\Sales\Model\ResourceModel\Order\Grid\Collection as OrderGridCollection;

class Collection extends OrderGridCollection
{
    private const SKU_FIELD = 'sku';
    private const EAN_FIELD = 'ean';
    private const SKU_FILTER_FLAG = 'sku_filter_added';
    private const EAN_FILTER_FLAG = 'ean_filter_added';
    private const SALES_ORDER_ITEM_TABLE = 'sales_order_item';

    /**
     * Add field to filter with special handling for SKU and EAN fields.
     *
     * @param string|array $field
     * @param string|int|array|null $condition
     * @return Collection
     */
    public function addFieldToFilter(
        $field,
        $condition = null
    ): Collection {
        if ($field === self::SKU_FIELD && !$this->getFlag(self::SKU_FILTER_FLAG)) {
            return $this->addSkuFilter($condition);
        }

        if ($field === self::EAN_FIELD && !$this->getFlag(self::EAN_FILTER_FLAG)) {
            return $this->addEanFilter($condition);
        }

        return parent::addFieldToFilter($field, $condition);
    }

    /**
     * Add SKU filter to the collection.
     *
     * @param string|int|array|null $condition
     * @return Collection
     */
    private function addSkuFilter($condition): Collection
    {
        $this->setFlag(self::SKU_FILTER_FLAG, true);
        
        $this->getSelect()->joinLeft(
            ['soi_sku' => $this->getTable(self::SALES_ORDER_ITEM_TABLE)],
            'main_table.entity_id = soi_sku.order_id',
            []
        );

        $searchValue = $this->extractSearchValue($condition);
        $this->getSelect()->where('soi_sku.sku = ?', $searchValue);
        $this->getSelect()->group('main_table.entity_id');

        return $this;
    }

    /**
     * Add EAN filter to the collection.
     *
     * @param string|int|array|null $condition
     * @return Collection
     */
    private function addEanFilter($condition): Collection
    {
        $this->setFlag(self::EAN_FILTER_FLAG, true);
        
        $eanAttrId = $this->getEanAttributeId();
        if (!$eanAttrId) {
            // No EAN attribute found, return empty result set
            $this->getSelect()->where('FALSE');
            return $this;
        }

        $this->getSelect()->joinLeft(
            ['soi_ean' => $this->getTable(self::SALES_ORDER_ITEM_TABLE)],
            'main_table.entity_id = soi_ean.order_id',
            []
        );

        $this->getSelect()->joinLeft(
            ['cpe_ean' => $this->getTable('catalog_product_entity')],
            'soi_ean.product_id = cpe_ean.entity_id',
            []
        );

        // Build the correct join condition based on our working SQL
        if ($this->hasRowIdColumn()) {
            $joinCondition = $this->getConnection()->quoteInto(
                'cpe_ean.row_id = ean_attr.row_id AND ean_attr.attribute_id = ? AND ean_attr.store_id = 0',
                $eanAttrId
            );
        } else {
            $joinCondition = $this->getConnection()->quoteInto(
                'cpe_ean.entity_id = ean_attr.row_id AND ean_attr.attribute_id = ? AND ean_attr.store_id = 0',
                $eanAttrId
            );
        }

        $this->getSelect()->joinLeft(
            ['ean_attr' => $this->getTable('catalog_product_entity_varchar')],
            $joinCondition,
            []
        );

        $searchValue = $this->extractSearchValue($condition);
        $this->getSelect()->where('ean_attr.value = ?', $searchValue);
        $this->getSelect()->group('main_table.entity_id');

        return $this;
    }

    /**
     * Extract search value from condition.
     *
     * @param mixed $condition
     * @return string
     */
    private function extractSearchValue($condition): string
    {
        if (is_array($condition) && isset($condition['like'])) {
            return trim($condition['like'], '%');
        }
        
        return (string)$condition;
    }





    /**
     * Get EAN attribute ID.
     *
     * @return int|null
     */
    private function getEanAttributeId(): ?int
    {
        $connection = $this->getConnection();

        try {
            $select = $connection->select()
                ->from(
                    ['ea' => $connection->getTableName('eav_attribute')],
                    ['attribute_id']
                )
                ->where('ea.entity_type_id = ?', Product::ENTITY)
                ->where('ea.attribute_code = ?', 'ean');

            return $connection->fetchOne($select) ?: null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if catalog_product_entity has row_id column.
     *
     * @return bool
     */
    private function hasRowIdColumn(): bool
    {
        $connection = $this->getConnection();

        return $connection->tableColumnExists(
            $connection->getTableName('catalog_product_entity'),
            'row_id'
        );
    }
}
