<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="comave_marketplace" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Marketplace Settings</label>
            <tab>comave</tab>
            <resource>Magento_Config::config</resource>
            <group id="product_settings" translate="label" type="text" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Configurable Product Settings</label>
                <field id="disable_seller_create_attribute" translate="label comment" sortOrder="2" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Disable Create Attribute for Configurable Products</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="inventory_settings" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Inventory Settings</label>
                <field id="admin_low_stock_threshold" translate="label comment" sortOrder="2" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Low Stock Quantity Admin/Seller Grid</label>
                    <validate>validate-number</validate>
                </field>
            </group>
            <group id="is_seller_editable_settings" translate="label" type="text" sortOrder="710"
                   showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Seller Product Attribute Settings</label>
                <field id="editable_attributes" translate="label" sortOrder="10" type="multiselect"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Attributes Editable by Sellers</label>
                    <source_model>Comave\Marketplace\Model\Config\Source\ProductAttributes</source_model>
                    <comment><![CDATA[Select which product attributes should be editable by sellers]]></comment>
                </field>
            </group>
        </section>
    </system>
</config>
