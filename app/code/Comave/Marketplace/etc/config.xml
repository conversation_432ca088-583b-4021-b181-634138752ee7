<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <comave_marketplace>
            <product_settings>
                <disable_seller_create_attribute>1</disable_seller_create_attribute>
            </product_settings>
            <inventory_settings>
                <low_stock_notification>0</low_stock_notification>
                <admin_low_stock_threshold>5</admin_low_stock_threshold>
            </inventory_settings>
            <is_seller_editable_settings>
                <editable_attributes>sku,name,price</editable_attributes>
            </is_seller_editable_settings>
        </comave_marketplace>
    </default>
</config>
