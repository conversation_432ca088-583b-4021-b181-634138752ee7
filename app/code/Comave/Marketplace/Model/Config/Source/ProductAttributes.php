<?php
declare(strict_types=1);

namespace Comave\Marketplace\Model\Config\Source;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Data\OptionSourceInterface;
use Psr\Log\LoggerInterface;

class ProductAttributes implements OptionSourceInterface
{
    /**
     * @param ProductAttributeRepositoryInterface $productAttributeRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return array
     */
    public function toOptionArray(): array
    {
        $options = [];

        try {
            $searchCriteria = $this->searchCriteriaBuilder->create();
            $attributes = $this->productAttributeRepository->getList($searchCriteria);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return $options;
        }

        foreach ($attributes->getItems() as $attribute) {
            $label = $attribute->getDefaultFrontendLabel() ?: $attribute->getAttributeCode();

            $options[] = [
                'value' => $attribute->getAttributeCode(),
                'label' => $label,
            ];
        }

        usort(
            $options,
            static fn(array $a, array $b): int => strcmp($a['label'], $b['label'])
        );

        return $options;
    }
}
