<?php

declare(strict_types=1);

namespace Comave\Marketplace\Model\Plugin\Action;

use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Message\ManagerInterface;

class ControllerPathValidator
{
    private \Magento\Framework\App\ResponseInterface $response;

    private \Magento\Framework\UrlInterface $url;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     */
    public function __construct(
        private readonly Context $context,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly ManagerInterface $messageManager
    ) {
        $this->response = $context->getResponse();
        $this->url = $context->getUrl();
    }

    /**
     * Redirect to customer page if action is not allowed in the current group
     *
     * @param \Magento\Framework\App\ActionInterface $subject
     * @param \Magento\Framework\App\RequestInterface $request
     * @return void
     */
    public function beforeDispatch(ActionInterface $subject, RequestInterface $request): void
    {
        $disabledSellerCreateAttribute = $this->scopeConfig->getValue(
            'comave_marketplace/product_settings/disable_seller_create_attribute',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        if (!$disabledSellerCreateAttribute) {
            return;
        }

        if ($request->getFullActionName() !== 'marketplace_product_attribute_new') {
            return;
        }

        $url = $this->url->getUrl('marketplace/account/dashboard');
        $this->response->setRedirect($url);
        $this->messageManager->addErrorMessage(
            __('The action to manage configurable attributes has been temporarily disabled.')
        );
        $this->response->sendResponse();
    }
}
