<?php
declare(strict_types=1);

namespace Comave\Marketplace\Console\Command;

use Comave\Marketplace\Service\SellerEditableFlagUpdater;
use Magento\Framework\Console\Cli;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateEditableAttributes extends Command
{
    /**
     * @param SellerEditableFlagUpdater $sellerEditableFlagUpdater
     * @param LoggerInterface $logger
     * @param string|null $name
     */
    public function __construct(
        private readonly SellerEditableFlagUpdater $sellerEditableFlagUpdater,
        private readonly LoggerInterface $logger,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('comave:seller:update-edditabe-attributes')
            ->setDescription('Set is_seller_editable=1 on approved product attributes');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $success = $this->sellerEditableFlagUpdater->updateEditableAttributes();

        if ($success) {
            $output->writeln('Seller-editable attributes updated successfully.');
            return Cli::RETURN_SUCCESS;
        }

        $output->writeln('Some attributes could not be updated.');
        return Cli::RETURN_FAILURE;
    }
}
