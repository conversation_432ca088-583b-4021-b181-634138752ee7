<?php
declare(strict_types=1);

namespace Comave\Marketplace\Service;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class EditableAttributesProvider
{
    private const string XML_PATH_EDITABLE_ATTRIBUTES = 'comave_marketplace/is_seller_editable_settings/editable_attributes';

    /**
     * EditableAttributesProvider constructor.
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig
    ) {}

    /**
     * Returns an array of product attribute codes that are editable by sellers.
     *
     * @param null|int $storeId
     * @return string[]
     */
    public function getEditableAttributes($storeId = null): array
    {
        $value = $this->scopeConfig->getValue(
            self::XML_PATH_EDITABLE_ATTRIBUTES,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );

        if (is_string($value)) {
            return array_filter(array_map('trim', explode(',', $value)));
        }

        return [];
    }
}
