<?php
declare(strict_types=1);

namespace Comave\Marketplace\Service;

use Comave\Marketplace\Service\EditableAttributesProvider;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Catalog\Model\Product;
use Psr\Log\LoggerInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

class SellerEditableFlagUpdater
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param \Comave\Marketplace\Service\EditableAttributesProvider $editableAttributesProvider
     * @param EavSetupFactory $eavSetupFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EditableAttributesProvider $editableAttributesProvider,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return bool
     */
    public function updateEditableAttributes(): bool
    {
        $attributeCodes = $this->editableAttributesProvider->getEditableAttributes();
        if (empty($attributeCodes)) {
            $this->logger->info('No editable attributes found to update.');
            return true;
        }

        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $success = true;

        foreach ($attributeCodes as $attributeCode) {
            try {
                $eavSetup->updateAttribute(
                    Product::ENTITY,
                    $attributeCode,
                    SellerAttributePermission::ATTRIBUTE_IS_SELLER_EDITABLE,
                    SellerAttributePermission::STATUS_ENABLED
                );
            } catch (\Exception $exception) {
                $succes = false;
                $this->logger->error(
                    sprintf(
                        'Unable to update is_seller_editable for attributeCode "%s": %s',
                        $attributeCode,
                        $exception->getMessage()
                    )
                );
            }
        }

        return $success;
    }
}
