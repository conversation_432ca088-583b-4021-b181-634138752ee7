<?php
declare(strict_types=1);

namespace Comave\Marketplace\Service;

use Magento\Framework\App\RequestInterface;
use Magento\Catalog\Model\ResourceModel\Eav\AttributeFactory;
use Comave\Marketplace\Service\EditableAttributesProvider;
use Magento\Framework\Message\ManagerInterface;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Backend\Model\View\Result\RedirectFactory;

class RestrictSellerAttributeValidator
{
    private int $invalidAttributeId = 0;
    private string $invalidAttributeCode = '';

    /**
     * @param AttributeFactory $attributeFactory
     * @param EditableAttributesProvider $editableAttributesProvider
     * @param RedirectFactory $redirectFactory
     * @param ManagerInterface $messageManager
     */
    public function __construct(
        private readonly AttributeFactory $attributeFactory,
        private readonly EditableAttributesProvider $editableAttributesProvider,
        private readonly RedirectFactory $redirectFactory,
        private readonly ManagerInterface $messageManager
    ) {}

    /**
     * @param RequestInterface $request
     * @return bool
     */
    public function validate(RequestInterface $request): bool
    {
        $attributeId = (int)$request->getParam('attribute_id');
        $isSellerEditable = $request->getParam('is_seller_editable') ?? null;

        if (!$attributeId || $isSellerEditable === null || (int)$isSellerEditable === 0) {
            return true;
        }

        $attribute = $this->attributeFactory->create()->load($attributeId);
        if (!$attribute->getId()) {
            return true;
        }

        $attributeCode = $attribute->getAttributeCode();
        $editableAttributes = $this->editableAttributesProvider->getEditableAttributes();

        if (!in_array($attributeCode, $editableAttributes, true)) {
            $this->invalidAttributeId = $attributeId;
            $this->invalidAttributeCode = $attributeCode;
            return false;
        }

        return true;
    }

    /**
     * @return Redirect
     */
    public function getRedirect(): Redirect
    {
        $this->messageManager->addErrorMessage(
            __(
                'The "Seller can update" property cannot be enabled for the attribute "%1".',
                $this->invalidAttributeCode
            )
        );

        $redirect = $this->redirectFactory->create();
        $redirect->setPath(
            'catalog/product_attribute/edit',
            [
                'attribute_id' => $this->invalidAttributeId
            ]
        );

        return $redirect;
    }
}
