# Marketplace Module
This Magento 2 module extends the marketplace functionality by providing tools for managing sellers, products

## Main Features
- Manage editable product attributes for sellers
- Save low stock threshold per seller
- Advanced search within category mappings
- CLI command support for Magento
- Custom helper methods for seller-related business logic
- Admin interface integration for managing seller products

## Magento CLI Commands
The module includes CLI commands for interacting with the system via the command line.
Updates the list of product attributes that sellers are allowed to edit.
`comave:seller:update-edditabe-attributes`

