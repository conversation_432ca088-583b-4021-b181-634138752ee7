<?php
declare(strict_types=1);

namespace Comave\Marketplace\Plugin\Catalog\Controller\Adminhtml\Product\Attribute;

use Magento\Catalog\Controller\Adminhtml\Product\Attribute\Save;
use Comave\Marketplace\Service\RestrictSellerAttributeValidator;

class RestrictSellerEditableAttribute
{
    /**
     * @param RestrictSellerAttributeValidator $restrictSellerAttributeValidator
     */
    public function __construct(
        private readonly RestrictSellerAttributeValidator $restrictSellerAttributeValidator,
    ) {}

    /**
     * @param Save $subject
     * @param \Closure $proceed
     * @return \Magento\Backend\Model\View\Result\Redirect|mixed
     */
    public function aroundExecute(
        Save $subject,
        \Closure $proceed
    ) {
        if (!$this->restrictSellerAttributeValidator->validate($subject->getRequest())) {
            return $this->restrictSellerAttributeValidator->getRedirect();
        }

        return $proceed();
    }
}
