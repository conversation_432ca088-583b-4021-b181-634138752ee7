<?php
declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Catalog\Model\Product;
use Comave\Marketplace\Service\SellerEditableFlagUpdater;
use Comave\Marketplace\Service\SellerAttributePermission;
use Psr\Log\LoggerInterface;

class SetSellerEditableFlagForMandatoryAttributes implements DataPatchInterface
{
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly SellerEditableFlagUpdater $sellerEditableFlagUpdater
    ) {}

    /**
     * {@inheritdoc}
     */
    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        $this->sellerEditableFlagUpdater->updateEditableAttributes();
        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }
}
