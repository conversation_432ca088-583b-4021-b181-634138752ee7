<?php
declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Comave\Marketplace\Model\FixtureManager;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Customer\Model\Customer;
use Comave\Marketplace\Setup\Patch\Data\InstallWebkulMissingAttributesV2;
use Psr\Log\LoggerInterface;

class RemoveWebkulAttributes implements DataPatchInterface
{
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly FixtureManager $fixtureManager,
        private readonly LoggerInterface $logger,
    ) {}

    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $attributes = $this->fixtureManager->getData('webkul-remove-attributes');
        } catch (\Exception $exception) {
            $this->logger->error(
                sprintf(
                    'Unable to load webkul-remove-attributes: %s',
                    $exception->getMessage()
                )
            );
            $this->moduleDataSetup->getConnection()->endSetup();
            return;
        }

        /** @var \Magento\Eav\Setup\EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        foreach ($attributes as $attribute) {
            if (!isset($attribute['attribute_code'])) {
                $this->logger->warning('Missing attribute_code');
                continue;
            }
            $eavSetup->removeAttribute(Customer::ENTITY, $attribute['attribute_code']);
            $this->logger->info(
                sprintf(
                    "Removed attribute '%s'",
                    $attribute['attribute_code']
                )
            );
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [
            InstallWebkulMissingAttributesV2::class
        ];
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }
}
