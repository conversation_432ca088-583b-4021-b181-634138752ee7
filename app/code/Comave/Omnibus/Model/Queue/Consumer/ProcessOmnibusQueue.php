<?php
declare(strict_types=1);

namespace Comave\Omnibus\Model\Queue\Consumer;

use Comave\Omnibus\Api\OmnibusProcessMessageInterface;
use Comave\Omnibus\Service\PriceHistoryProcessor;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Class ProcessOmnibusQueue
 * Handles messages for Omnibus price history processing
 */
class ProcessOmnibusQueue
{
    public function __construct(
        private readonly PriceHistoryProcessor $priceHistoryProcessor,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process queue message
     *
     * @param OmnibusProcessMessageInterface $message
     * @return void
     */
    public function process(OmnibusProcessMessageInterface $message): void
    {
        try {
            $this->priceHistoryProcessor->processProduct(
                $message->getProductId(),
                $message->getStoreId()
            );
        } catch (NoSuchEntityException|LocalizedException|InputException $e) {
            $this->logger->warning(
                'Failed to process Omnibus queue message: ' . $e->getMessage(),
                [
                    'product_id' => $message->getProductId(),
                    'store_id' => $message->getStoreId(),
                    'exception' => $e
                ]
            );
        } catch (\Throwable $e) {
            $this->logger->error(
                'Unexpected error during Omnibus queue processing: ' . $e->getMessage(),
                ['exception' => $e]
            );
        }
    }
}
