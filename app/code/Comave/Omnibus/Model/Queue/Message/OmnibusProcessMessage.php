<?php
declare(strict_types=1);

namespace Comave\Omnibus\Model\Queue\Message;

use Comave\Omnibus\Api\OmnibusProcessMessageInterface;
use JsonSerializable;

/**
 * Class OmnibusProcessMessage
 * DTO for handling Omnibus price history messages in the queue
 */
class OmnibusProcessMessage implements OmnibusProcessMessageInterface, JsonSerializable
{
    /**
     * @var int
     */
    private int $productId;

    /**
     * @var int
     */
    private int $storeId;

    /**
     * Constructor to hydrate message from array (used by queue system)
     *
     * @param array $data
     */
    public function __construct(array $data = [])
    {
        $this->productId = (int)($data['product_id'] ?? 0);
        $this->storeId = (int)($data['store_id'] ?? 0);
    }

    /**
     * Get the product ID
     *
     * @return int
     */
    public function getProductId(): int
    {
        return $this->productId;
    }

    /**
     * Set the product ID
     *
     * @param int $productId
     * @return OmnibusProcessMessageInterface
     */
    public function setProductId(int $productId): OmnibusProcessMessageInterface
    {
        $this->productId = $productId;
        return $this;
    }

    /**
     * Get the store ID
     *
     * @return int
     */
    public function getStoreId(): int
    {
        return $this->storeId;
    }

    /**
     * Set the store ID
     *
     * @param int $storeId
     * @return OmnibusProcessMessageInterface
     */
    public function setStoreId(int $storeId): OmnibusProcessMessageInterface
    {
        $this->storeId = $storeId;
        return $this;
    }

    /**
     * Specify data to be serialized to JSON
     *
     * @return array
     */
    public function jsonSerialize(): array
    {
        return [
            'product_id' => $this->productId,
            'store_id' => $this->storeId,
        ];
    }
}
