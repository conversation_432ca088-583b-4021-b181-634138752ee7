<?php
declare(strict_types=1);

namespace Comave\Omnibus\Model\Attribute\Backend;

use Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;

/**
 * Backend model for lowest_price_last_30_days attribute
 * Prevents manual editing by preserving the original value during save operations
 */
class LowestPrice extends AbstractBackend
{
    /**
     * Attribute code for the lowest price in the last 30 days
     * @var string
     */
    private const ATTRIBUTE_CODE = 'lowest_price_last_30_days';
    /**
     * @var string
     */
    private const SET_BY_SYSTEM_ATTRIBUTE = 'lowest_price_last_30_days_set_by_system';

    /**
     * Prevent manual updates by retaining the original value
     *
     * @param DataObject $object
     * @return $this
     */
    public function beforeSave($object)
    {
        if (
            $object->hasDataChanges() &&
            $object->dataHasChangedFor(self::ATTRIBUTE_CODE) &&
            !$object->getData(self::SET_BY_SYSTEM_ATTRIBUTE)
        ) {
            throw new LocalizedException(
                __('This attribute is system-managed and cannot be manually edited.')
            );
        }

        return parent::beforeSave($object);
    }
}
