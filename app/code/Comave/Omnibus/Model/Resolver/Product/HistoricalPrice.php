<?php
declare(strict_types=1);

namespace Comave\Omnibus\Model\Resolver\Product;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;

/**
 * GraphQL resolver to fetch price history and lowest price for a product
 * according to the EU Omnibus directive
 */
class HistoricalPrice implements ResolverInterface
{
    private const TABLE_NAME = 'comave_omnibus_price_history';
    private const LOWEST_PRICE_ATTRIBUTE = 'lowest_price_last_30_days';

    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger,
        private readonly ProductRepositoryInterface $productRepository
    ) {}

    /**
     * Resolve product historical price data according to EU Omnibus directive
     *
     * This method fetches the lowest price within the last 30 days for a given product
     * and its complete price history. It retrieves the lowest price from the product's
     * attribute, gets the timestamp when this lowest price was first recorded, and
     * returns the complete price history from the last 30 days. All data is filtered
     * by product ID and store ID to ensure correct store-specific pricing.
     *
     * @param Field $field GraphQL field configuration
     * @param mixed $context GraphQL context containing store and other information
     * @param ResolveInfo $info GraphQL resolve information
     * @param array|null $value Parent resolver value containing product model
     * @param array|null $args GraphQL field arguments
     * @return array Returns array with lowest_price, currency, timestamp, and price_history
     * @throws \Exception When product model is not provided or database errors occur
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        try {
            if (!isset($value['model'])) {
                $this->logger->error("Historical price resolver: No product model provided");
                return $this->emptyResult();
            }

            $originalProduct = $value['model'];
            $productId = (int) $originalProduct->getId();
            $storeId = (int) $context->getExtensionAttributes()->getStore()->getId();
            $product = $this->productRepository->getById($productId, false, $storeId);
            $lowestPriceRaw = $product->getData(self::LOWEST_PRICE_ATTRIBUTE);
            $lowestPrice = is_numeric($lowestPriceRaw) ? (float) $lowestPriceRaw : null;
            $currency = $this->storeManager->getStore($storeId)->getCurrentCurrencyCode();
            $priceHistory = $this->getPriceHistory($productId, $storeId);
            $timestamp = $lowestPrice ? $this->getLowestPriceTimestamp($productId, $storeId, $lowestPrice) : null;

            return [
                'lowest_price' => $lowestPrice,
                'currency' => $currency,
                'timestamp' => $timestamp,
                'price_history' => $priceHistory
            ];

        } catch (\Exception $e) {
            $this->logger->error("Historical price resolver error: " . $e->getMessage(), ['exception' => $e]);
            return $this->emptyResult();
        }
    }

    /**
     * Get price history for a product in the last 30 days
     *
     * @param int $productId
     * @param int $storeId
     * @return array
     */
    private function getPriceHistory(int $productId, int $storeId): array
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $table = $this->resourceConnection->getTableName(self::TABLE_NAME);

            $thirtyDaysAgo = (new \DateTime('-30 days'))->format('Y-m-d 00:00:00');
            $now = (new \DateTime())->format('Y-m-d 23:59:59');

            $select = $connection->select()
                ->from($table, ['price', 'recorded_at'])
                ->where('product_id = ?', $productId)
                ->where('store_id = ?', $storeId)
                ->where('recorded_at >= ?', $thirtyDaysAgo)
                ->where('recorded_at <= ?', $now)
                ->order('recorded_at ASC');

            $rows = $connection->fetchAll($select);

            return array_map(fn ($row) => [
                'price' => (float)$row['price'],
                'timestamp' => $row['recorded_at']
            ], $rows);
        } catch (\Exception $e) {
            $this->logger->error("Error fetching price history: " . $e->getMessage(), ['exception' => $e]);
            return [];
        }
    }

    /**
     * Get timestamp when the lowest price was first recorded
     *
     * @param int $productId
     * @param int $storeId
     * @param float $lowestPrice
     * @return string|null
     */
    private function getLowestPriceTimestamp(int $productId, int $storeId, float $lowestPrice): ?string
    {
        try {

            $connection = $this->resourceConnection->getConnection();
            $table = $this->resourceConnection->getTableName(self::TABLE_NAME);

            $select = $connection->select()
                ->from($table, ['recorded_at'])
                ->where('product_id = ?', $productId)
                ->where('store_id = ?', $storeId)
                ->where('price = ?', $lowestPrice)
                ->order('recorded_at ASC')
                ->limit(1);

            $timestamp = $connection->fetchOne($select);
            return $timestamp ?: null;
        } catch (\Exception $e) {
            $this->logger->error("Error fetching lowest price timestamp: " . $e->getMessage(), ['exception' => $e]);
            return null;
        }
    }

    /**
     * Return empty result structure
     *
     * @return array
     */
    private function emptyResult(): array
    {
        return [
            'lowest_price' => null,
            'currency' => null,
            'timestamp' => null,
            'price_history' => []
        ];
    }
}
