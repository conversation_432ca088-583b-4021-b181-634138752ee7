<!--@subject Price Drop Alert: {{var product.getName()}} @-->
<!--@vars {
"var product":"Product object with getName() method",
"var drop_count":"Number of price drop",
"var threshold":"Configured threshold",
"var store":"Store object with getName() method",
"var alert_time":"Formatted alert timestamp"
} @-->

{{template config_path="design/email/header_template"}}

<div style="padding: 30px; max-width: 600px; margin: 0 auto;">
    <p><em>This is an automatically generated email, please do not reply.</em></p>

    <p>Hello Admin,</p>

    <p>A product has exceeded the configured price drop alert threshold. Please review the details below:</p>

    <h3>Product Information</h3>
    <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
        <tr>
            <td><strong>Product Name:</strong></td>
            <td>{{var product.getName()}}</td>
        </tr>
        <tr>
            <td><strong>SKU:</strong></td>
            <td>{{var product.getSku()}}</td>
        </tr>
    </table>

    <h3>Alert Details</h3>
    <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
        <tr>
            <td><strong>Price drop (Last 30 days):</strong></td>
            <td><strong>{{var drop_count}}</strong></td>
        </tr>
        <tr>
            <td><strong>Configured Threshold:</strong></td>
            <td>{{var threshold}}</td>
        </tr>
        <tr>
            <td><strong>Alert Time:</strong></td>
            <td>{{var alert_time}}</td>
        </tr>
    </table>

    <hr>
    <p><small>This is an automated notification from your Magento store's Omnibus monitoring system.</small></p>
</div>

{{template config_path="design/email/footer_template"}}
