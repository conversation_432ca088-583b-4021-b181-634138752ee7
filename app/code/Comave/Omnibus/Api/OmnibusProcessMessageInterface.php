<?php
declare(strict_types=1);

namespace Comave\Omnibus\Api;
/**
 * Interface for Omnibus process messages in the message queue
 */
interface OmnibusProcessMessageInterface
{
    /**
     * Get the product ID associated with this message
     *
     * @return int
     */
    public function getProductId(): int;

    /**
     * Set the product ID for this message
     *
     * @param int $productId
     * @return OmnibusProcessMessageInterface
     */
    public function setProductId(int $productId): OmnibusProcessMessageInterface;

    /**
     * Get the store ID associated with this message
     *
     * @return int
     */
    public function getStoreId(): int;

    /**
     * Set the store ID for this message
     *
     * @param int $storeId
     * @return OmnibusProcessMessageInterface
     */
    public function setStoreId(int $storeId): OmnibusProcessMessageInterface;
}
