<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_omnibus_price_history" resource="default" engine="innodb" comment="Omnibus Directive Product Price History">
        <column name="id" xsi:type="int" unsigned="true" nullable="false" identity="true" comment="ID"/>
        <column name="product_id" xsi:type="int" nullable="false" comment="Product ID"/>
        <column name="sku" xsi:type="varchar" length="64" nullable="false" comment="Product SKU"/>
        <column name="store_id" xsi:type="smallint" unsigned="true" nullable="false" comment="Store ID"/>
        <column name="price" xsi:type="decimal" scale="4" precision="12" nullable="false" comment="Product Price"/>
        <column name="recorded_at" xsi:type="timestamp" default="CURRENT_TIMESTAMP" nullable="false" comment="Recorded At"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>

        <index referenceId="COMAVE_OMNIBUS_PRODUCT_ID_IDX" indexType="btree">
            <column name="product_id"/>
        </index>
        <index referenceId="COMAVE_OMNIBUS_STORE_ID_IDX" indexType="btree">
            <column name="store_id"/>
        </index>
        <index referenceId="COMAVE_OMNIBUS_PRODUCT_STORE_DATE_IDX" indexType="btree">
            <column name="product_id"/>
            <column name="store_id"/>
            <column name="recorded_at"/>
        </index>
    </table>
</schema>
