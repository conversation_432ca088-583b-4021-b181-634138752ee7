<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\Omnibus\Api\OmnibusProcessMessageInterface" type="Comave\Omnibus\Model\Queue\Message\OmnibusProcessMessage"/>
    
    <type name="Magento\Framework\MessageQueue\MergerFactory">
        <arguments>
            <argument name="mergers" xsi:type="array">
                <item name="omnibus_price_history_process" xsi:type="string">Magento\Framework\MessageQueue\Merger\DefaultMerger</item>
            </argument>
        </arguments>
    </type>
</config>
