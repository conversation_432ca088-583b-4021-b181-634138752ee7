interface ProductInterface {
    historical_price: HistoricalPrice @doc(description: "Historical price information for EU Omnibus Directive compliance")
    @resolver(class: "\\Comave\\Omnibus\\Model\\Resolver\\Product\\HistoricalPrice")
}

type HistoricalPrice {
    lowest_price: Float @doc(description: "The lowest price in the last 30 days before the current sale")
    currency: String! @doc(description: "The currency code for the price")
    timestamp: String @doc(description: "The timestamp when the lowest price was recorded (ISO-8601)")
    price_history: [PriceHistoryItem] @doc(description: "List of all price changes in the last 30 days")
}

type PriceHistoryItem {
    price: Float! @doc(description: "The historical price value")
    timestamp: String! @doc(description: "The timestamp when this price was recorded (ISO-8601)")
}
