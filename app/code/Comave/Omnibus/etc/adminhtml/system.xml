<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="omnibus" translate="label" sortOrder="100" showInDefault="1" showInWebsite="1">
            <label>Omnibus Directive</label>
            <tab>general</tab>
            <resource>Comave_Marketplace::config</resource>

            <group id="alert" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1">
                <label>Admin Price Update Alert</label>

                <field id="recipient_email" translate="label comment" type="text" sortOrder="10"
                       showInDefault="1" showInWebsite="1">
                    <label>Alert Recipient Email</label>
                    <comment>Leave empty to disable alert delivery. Email is sent when price changes exceed the configured threshold.</comment>
                    <validate>validate-email</validate>
                </field>

                <field id="price_drop_threshold" translate="label comment" type="text" sortOrder="20"
                       showInDefault="1" showInWebsite="1">
                    <label>Price Drop Alert Threshold</label>
                    <comment>Send alert if a product's price drops more than this many times in 30 days.</comment>
                    <validate>validate-number validate-zero-or-greater</validate>
                </field>
            </group>
        </section>
    </system>
</config>
