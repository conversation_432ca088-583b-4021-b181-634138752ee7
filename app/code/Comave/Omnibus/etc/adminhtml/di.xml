<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <virtualType name="Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\Pool">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="lowestPriceModifier" xsi:type="array">
                    <item name="class" xsi:type="string">Comave\Omnibus\Ui\DataProvider\Product\Form\Modifier\LowestPrice</item>
                    <item name="sortOrder" xsi:type="number">9999</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
