<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="omnibus_price_history_consumer"
            queue="omnibus_price_history_process"
            connection="amqp"
            onlySpawnWhenMessageAvailable="1"
            handler="Comave\Omnibus\Model\Queue\Consumer\ProcessOmnibusQueue::process" />
</config>
