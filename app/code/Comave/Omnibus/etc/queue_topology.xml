<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/topology.xsd">
    <exchange name="comave.omnibus.price.history" type="topic" connection="amqp">
        <binding id="omnibusProcessBinding"
                 topic="omnibus.price.history.process"
                 destinationType="queue"
                 destination="omnibus_price_history_process"/>
    </exchange>
</config>
