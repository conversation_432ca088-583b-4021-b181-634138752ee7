<?php

namespace Comave\Omnibus\Ui\DataProvider\Product\Form\Modifier;

use Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\AbstractModifier;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Stdlib\ArrayManager;
use Magento\Store\Model\StoreManagerInterface;

class LowestPrice extends AbstractModifier
{
    private const ATTRIBUTE_CODE = 'lowest_price_last_30_days';
    private const ATTRIBUTE_CONTAINER = 'container_' . self::ATTRIBUTE_CODE;
    private const ATTRIBUTE_META_PATH =
        'product-details/children/' .
        self::ATTRIBUTE_CONTAINER . '/children/' .
        self::ATTRIBUTE_CODE . '/arguments/data/config';

    public function __construct(
        private readonly ArrayManager $arrayManager,
        private readonly RequestInterface $request
    ) {}

    public function modifyData(array $data): array
    {
        return $data;
    }

    public function modifyMeta(array $meta): array
    {
        $storeId = (int) $this->request->getParam('store', 0);

        if ($storeId === 0) {
            $meta = $this->arrayManager->remove(
                self::ATTRIBUTE_META_PATH,
                $meta
            );

            $meta = $this->arrayManager->remove(
                'product-details/children/' . self::ATTRIBUTE_CONTAINER,
                $meta
            );

            return $meta;
        }

        if ($this->arrayManager->get(self::ATTRIBUTE_META_PATH, $meta) !== null) {
            $meta = $this->arrayManager->merge(
                self::ATTRIBUTE_META_PATH,
                $meta,
                [
                    'disabled' => true,
                    'default' => null,
                    'service' => false,
                    'placeholder' => __('Not yet set by system')
                ]
            );
        }

        return $meta;
    }
}
