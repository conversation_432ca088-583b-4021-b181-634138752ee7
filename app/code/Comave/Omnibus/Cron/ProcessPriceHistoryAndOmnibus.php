<?php
declare(strict_types=1);

namespace Comave\Omnibus\Cron;

use Comave\Omnibus\Model\Queue\Message\OmnibusProcessMessage;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Cron job to queue products for Omnibus Directive price history processing
 */
class ProcessPriceHistoryAndOmnibus
{
    private const TOPIC_NAME = 'omnibus.price.history.process';
    private const BATCH_SIZE = 500;
    private const EU_COUNTRIES = [
        'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'PL', 'SE', 'FI', 'AT',
        'IE', 'PT', 'GR', 'CZ', 'HU', 'SK', 'RO', 'BG', 'HR', 'SI',
        'LT', 'LV', 'EE', 'LU', 'MT', 'CY'
    ];

    public function __construct(
        private readonly CollectionFactory $productCollectionFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly PublisherInterface $publisher,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Execute the cron job to queue products for Omnibus Directive price history processing
     *
     * This method processes all active products from EU stores and queues them for
     * Omnibus Directive compliance processing. It batches products by store and
     * publishes them to the message queue for asynchronous processing.
     *
     * @return void
     * @throws \Throwable When an error occurs during product queueing
     */
    public function execute(): void
    {
        $startTime = microtime(true);
        try {
            $storeIds = array_filter(
                array_map(
                    fn(Store $store) => $this->isEUStore($store) ? (int)$store->getId() : null,
                    $this->storeManager->getStores()
                )
            );

            foreach ($storeIds as $storeId) {
                $this->queueStoreProducts($storeId);
            }
        } catch (\Throwable $e) {
            $this->logger->error('Error queueing products for Omnibus Directive', ['exception' => $e]);
        }
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        $this->logger->info(
            'Omnibus Directive product queueing job finished',
            [
                'finished_at' => (new \DateTime())->format('Y-m-d H:i:s'),
                'duration_seconds' => round($duration, 2)
            ]
        );
    }

    /**
     * Queue products for the specified store
     *
     * @param int $storeId
     * @return void
     */
    private function queueStoreProducts(int $storeId): void
    {
        $collection = $this->productCollectionFactory->create();
        $collection->addStoreFilter($storeId)
            ->addAttributeToSelect('entity_id')
            ->addAttributeToFilter('status', 1)
            ->addAttributeToFilter('visibility', ['neq' => 1])
            ->setPageSize(1000);

        $pages = $collection->getLastPageNumber();

        for ($currentPage = 1; $currentPage <= $pages; $currentPage++) {
            $collection->setCurPage($currentPage);
            $collection->load();

            $batch = [];

            foreach ($collection as $product) {
                $batch[] = new OmnibusProcessMessage([
                    'product_id' => (int)$product->getId(),
                    'store_id' => $storeId,
                ]);

                if (count($batch) >= self::BATCH_SIZE) {
                    $this->publishBatch($batch);
                    $batch = [];
                }
            }

            if (!empty($batch)) {
                $this->publishBatch($batch);
            }

            $collection->clear();
        }
    }

    /**
     * Publish batch of messages to queue
     *
     * @param array $messages
     * @return void
     */
    private function publishBatch(array $messages): void
    {
        foreach ($messages as $message) {
            $this->publisher->publish(self::TOPIC_NAME, $message);
        }
    }

    /**
     * Check if store is in EU country
     *
     * @param Store $store
     * @return bool
     */
    private function isEUStore(Store $store): bool
    {
        $countryCode = strtoupper($store->getConfig('general/country/default'));
        return in_array($countryCode, self::EU_COUNTRIES, true);
    }
}
