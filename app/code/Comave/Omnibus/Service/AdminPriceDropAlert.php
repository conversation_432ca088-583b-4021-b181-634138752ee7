<?php
// This file is a complete implementation plan with all pieces needed to add:
// 1. Admin email alert when a product changes price more than X times in the last 30 days.
// 2. Configurable threshold in admin.
// 3. Email template support.

namespace Comave\Omnibus\Service;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Store\Model\App\Emulation;
use Psr\Log\LoggerInterface;

class AdminPriceDropAlert
{
    private const CONFIG_THRESHOLD_PATH = 'omnibus/alert/price_drop_threshold';
    private const CONFIG_RECIPIENT_PATH = 'omnibus/alert/recipient_email';
    private const DEFAULT_THRESHOLD = 10;
    private const EMAIL_TEMPLATE_ID = 'omnibus_price_drop_admin_alert';
    private const TABLE_NAME = 'comave_omnibus_price_history';

    /**
     * AdminPriceDropAlert constructor.
     *
     * @param ResourceConnection $resource
     * @param ProductRepositoryInterface $productRepository
     * @param TransportBuilder $transportBuilder
     * @param StoreManagerInterface $storeManager
     * @param Emulation $appEmulation
     * @param LoggerInterface $logger
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private readonly ResourceConnection $resource,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly TransportBuilder $transportBuilder,
        private readonly StoreManagerInterface $storeManager,
        private readonly Emulation $appEmulation,
        private readonly LoggerInterface $logger,
        private readonly ScopeConfigInterface $scopeConfig
    ) {}

    /**
     * Check if the product has had more than the configured threshold of price drops
     * in the last 30 days and send an email alert to the admin if it has.
     *
     * @param string $productId
     * @param int $storeId
     * @return void
     */
    public function checkAndNotify(string $productId, int $storeId): void
    {
        $recipientEmail = (string) $this->scopeConfig->getValue(self::CONFIG_RECIPIENT_PATH, 'store', $storeId);
        if (empty($recipientEmail)) {
            return;
        }

        $threshold = (int) $this->scopeConfig->getValue(self::CONFIG_THRESHOLD_PATH, 'store', $storeId);
        if ($threshold <= 0) {
            $threshold = self::DEFAULT_THRESHOLD;
        }

        $priceDrops = $this->countPriceDrops($productId, $storeId);

        if ($priceDrops > $threshold) {
            try {
                $product = $this->productRepository->getById($productId, false, $storeId);
                $this->appEmulation->startEnvironmentEmulation($storeId);
                try {
                    $transport = $this->transportBuilder
                        ->setTemplateIdentifier(self::EMAIL_TEMPLATE_ID)
                        ->setTemplateOptions([
                            'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                            'store' => $storeId
                        ])
                        ->setTemplateVars([
                            'product' => $product,
                            'drop_count' => $priceDrops,
                            'threshold' => $threshold,
                            'alert_time' => (new \DateTime())->format('Y-m-d H:i:s')
                        ])
                        ->setFromByScope('general')
                        ->addTo($recipientEmail)
                        ->getTransport();

                    $transport->sendMessage();
                } finally {
                    $this->appEmulation->stopEnvironmentEmulation();
                }
            } catch (\Throwable $e) {
                $this->logger->error("Failed to send admin price drop alert for product {$productId} in store {$storeId}: " . $e->getMessage());
            }
        }
    }

    /**
     * Count the number of price drops in the last 30 days
     *
     * @param string $productId
     * @param int $storeId
     * @return int
     */
    private function countPriceDrops(string $productId, int $storeId): int
    {
        $since = (new \DateTime('-30 days'))->format('Y-m-d');
        $connection = $this->resource->getConnection();
        $table = $this->resource->getTableName(self::TABLE_NAME);

        $subQuery = $connection->select()
            ->from($table, [
                'price',
                'prev_price' => new \Zend_Db_Expr('LAG(price) OVER (ORDER BY recorded_at)')
            ])
            ->where('product_id = ?', $productId)
            ->where('store_id = ?', $storeId)
            ->where('recorded_at >= ?', $since)
            ->order('recorded_at ASC');

        $select = $connection->select()
            ->from(['sub' => $subQuery], ['drop_count' => new \Zend_Db_Expr('COUNT(*)')])
            ->where('sub.price < sub.prev_price');

        $result = $connection->fetchOne($select);

        return (int) ($result ?: 0);
    }
}
