<?php
declare(strict_types=1);

namespace Comave\Omnibus\Service;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Comave\Omnibus\Service\AdminPriceDropAlert;
use Psr\Log\LoggerInterface;

/**
 * Class PriceHistoryProcessor
 * Handles the processing of product price history for the Omnibus directive
 */
class PriceHistoryProcessor
{
    /**
     * @var string
     */
    private const TABLE_NAME = 'comave_omnibus_price_history';
    /**
     * @var string
     */
    private const LOWEST_PRICE_ATTRIBUTE = 'lowest_price_last_30_days';
    /**
     * @var string
     */
    private const SET_BY_SYSTEM_ATTRIBUTE = 'lowest_price_last_30_days_set_by_system';

    /**
     * @param ResourceConnection $resource
     * @param ProductRepositoryInterface $productRepository
     * @param DateTime $date
     * @param LoggerInterface $logger
     * @param AdminPriceDropAlert $adminPriceDropAlert
     */
    public function __construct(
        private readonly ResourceConnection $resource,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly DateTime $date,
        private readonly LoggerInterface $logger,
        private readonly AdminPriceDropAlert $adminPriceDropAlert
    ) {
    }

    /**
     * Process a single product for price history tracking and lowest price update
     *
     * @param int $productId
     * @param int $storeId
     * @return void
     * @throws NoSuchEntityException
     */
    public function processProduct(int $productId, int $storeId): void
    {
        try {
            $product = $this->productRepository->getById($productId, false, $storeId);
            $table = $this->resource->getTableName(self::TABLE_NAME);

            $this->trackProductPrice($product, $storeId, $table);
            $this->updateLowestHistoricalPrice($product, $storeId, $table);
        } catch (NoSuchEntityException $e) {
            $this->logger->error("Error processing product ID {$productId}: " . $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->critical("Unexpected error processing product ID {$productId}: " . $e->getMessage());
        }
    }

    private function isDiscountActive(?string $fromDate, ?string $toDate): bool
    {
        if (!$fromDate) {
            return false;
        }
        $now = new \DateTime();
        $from = new \DateTime($fromDate);
        $to = $toDate ? new \DateTime($toDate) : null;
        return $now >= $from && (!$to || $now <= $to);
    }

    /**
     * Track the current price of a product
     *
     * @param Product $product
     * @param int $storeId
     * @param string $table
     * @return void
     */
    private function trackProductPrice(Product $product, int $storeId, string $table): void
    {
        $productId = (string)$product->getId();
        $sku = $product->getSku();
        $connection = $this->resource->getConnection();

        $price = (float)$product->getFinalPrice();

        $lastPrice = $connection->fetchOne(
            $connection->select()
                ->from($table, ['price'])
                ->where('product_id = ?', $productId)
                ->where('store_id = ?', $storeId)
                ->order('recorded_at DESC')
                ->limit(1)
        );

        if ((float)$lastPrice !== $price) {
            try {
                $recordedAt = $this->date->gmtDate();
                $connection->insert($table, [
                    'product_id' => $productId,
                    'sku' => $sku,
                    'store_id' => $storeId,
                    'price' => $price,
                    'recorded_at' => $recordedAt,
                ]);
                if ($price < (float)$lastPrice) {
                    $this->adminPriceDropAlert->checkAndNotify($productId, $storeId);
                }
            } catch (\Exception $e) {
                $this->logger->error("Failed to record price for product {$sku}: " . $e->getMessage());
            }
        }
    }

    /**
     * Update the lowest historical price for a product if a discount is active
     *
     * @param Product $product
     * @param int $storeId
     * @param string $table
     * @return void
     */
    private function updateLowestHistoricalPrice(Product $product, int $storeId, string $table): void
    {
        $productId = (string)$product->getId();
        $sku = $product->getSku();
        $connection = $this->resource->getConnection();
        $specialPrice = (float)$product->getSpecialPrice();
        $fromDate = $product->getSpecialFromDate();
        $toDate = $product->getSpecialToDate();
        $currentLowestPrice = (float)$product->getData(self::LOWEST_PRICE_ATTRIBUTE);
        $lowestAlreadySet = $currentLowestPrice > 0;

        if (!$this->isDiscountActive($fromDate, $toDate)) {
            if ($lowestAlreadySet) {
                try {
                    $this->saveNewestLowestPrice($product, $storeId, null);
                } catch (\Exception $e) {
                    $this->logger->error("Failed to clear lowest price attribute for product {$sku}: " . $e->getMessage());
                }
            }
            return;
        }

        $now = new \DateTime();
        $thirtyDaysAgo = (clone $now)->modify('-30 days');
        $start = $thirtyDaysAgo->format('Y-m-d 00:00:00');
        $end = $now->format('Y-m-d H:i:s');
        $select = $connection->select()
            ->from($table, ['price'])
            ->where('product_id = ?', $productId)
            ->where('store_id = ?', $storeId)
            ->where('recorded_at >= ?', $start)
            ->where('recorded_at <= ?', $end)
            ->where('price != ?', $specialPrice)
            ->order('price ASC')
            ->limit(1);
        $lowest = $connection->fetchOne($select);
        if ($lowest && $currentLowestPrice != $lowest) {
            try {
                $this->saveNewestLowestPrice($product, $storeId, $lowest);
            } catch (\Exception $e) {
                $this->logger->error("Failed to update lowest price attribute for product {$sku}: " . $e->getMessage());
            }
        }
    }

    /**
     * Save product attribute using repository
     *
     * @param Product $product
     * @param int $storeId
     * @param mixed $value
     * @return void
     */
    private function saveNewestLowestPrice(Product $product, int $storeId, $value): void
    {
        try {
            $product->setStoreId($storeId);
            $product->setData(self::LOWEST_PRICE_ATTRIBUTE, $value);
            $product->setData(self::SET_BY_SYSTEM_ATTRIBUTE, true);
            $this->productRepository->save($product);
        } catch (\Exception $e) {
            $this->logger->error("Error saving attribute for product {$product->getSku()}: " . $e->getMessage());
            throw $e;
        }
    }
}
