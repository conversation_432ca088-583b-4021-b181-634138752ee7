<?php
declare(strict_types=1);

namespace Comave\Omnibus\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

/**
 * Class AddLowestPriceAttribute
 * Creates product attribute for storing the lowest price in the last 30 days
 * Used for EU Omnibus Directive compliance
 * This attribute is system-managed by cron and not editable by users
 */
class AddLowestPriceAttribute implements DataPatchInterface
{
    private const ATTRIBUTE_CODE = 'lowest_price_last_30_days';

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    public function apply(): void
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $eavSetup->removeAttribute(Product::ENTITY, self::ATTRIBUTE_CODE);

        $eavSetup->addAttribute(
            Product::ENTITY,
            self::ATTRIBUTE_CODE,
            [
                'type' => 'decimal',
                'label' => 'Lowest Price Last 30 Days',
                'input' => 'price',
                'required' => false,
                'sort_order' => 110,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'visible' => true,
                'user_defined' => false,
                'visible_on_front' => false,
                'used_in_product_listing' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'apply_to' => 'simple,configurable,virtual,bundle,downloadable',
                'note' => 'System-generated value for EU Omnibus Directive compliance. This field is automatically calculated and cannot be modified manually.',
                'readonly' => true,
                'input_renderer' => 'Magento\Catalog\Block\Adminhtml\Product\Helper\Form\Price',
                'is_visible_in_api' => true,
                'backend' => 'Comave\Omnibus\Model\Attribute\Backend\LowestPrice',
            ]
        );
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
