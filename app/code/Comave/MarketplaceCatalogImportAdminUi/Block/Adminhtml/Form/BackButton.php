<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Block\Adminhtml\Form;

use Magento\Backend\Model\UrlInterface;
use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;

class BackButton implements ButtonProviderInterface
{
    /**
     * @param \Magento\Backend\Model\UrlInterface $urlBuilder
     */
    public function __construct(private readonly UrlInterface $urlBuilder)
    {
    }

    /**
     * @return array{
     *     label: \Magento\Framework\Phrase,
     *     on_click: string,
     *     class: string,
     *     sort_order: int
     * }
     */
    public function getButtonData(): array
    {
        $backUrl = $this->urlBuilder->getUrl(
            'export_entity/list/index'
        );

        return [
            'label' => __('Back'),
            'on_click' => sprintf("location.href = '%s';", $backUrl),
            'class' => 'back',
            'sort_order' => 10
        ];
    }
}
