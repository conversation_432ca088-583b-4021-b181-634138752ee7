<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Block\Adminhtml\Form;

use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;

class SaveButton implements ButtonProviderInterface
{
    /**
     * @return array{
     *     label: \Magento\Framework\Phrase,
     *     class: string,
     *     data_attribute: array{mage-init: array{button: array<string, string>}, form-role: string},
     *     sort_order: int
     * }
     */
    public function getButtonData(): array
    {
        return [
            'label' => __('Save'),
            'class' => 'save primary',
            'data_attribute' => [
                'mage-init' => ['button' => ['event' => 'save']],
                'form-role' => 'save',
            ],
            'sort_order' => 30,
        ];
    }
}
