<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Ui\Column;

use Magento\Ui\Component\Listing\Columns\Column;
use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;

class EntityActions extends Column
{
    /**
     * @param array{data?: array{items: array{id: string}[]}} $dataSource
     *
     * @return array{data?: array{items: mixed[]}}
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) { //phpcs:ignore
                $item[$this->getData('name')] = [
                    'delete' => [
                        'href' => $this->context->getUrl(
                            'export_entity/template/delete',
                            [
                                'id' => $item[ExportEntityInterface::ID],
                            ]
                        ),
                        'label' => __('Delete'),
                    ],
                    'view' => [
                        'href' => $this->context->getUrl(
                            'export_entity/template/edit',
                            [
                                'id' => $item[ExportEntityInterface::ID],
                            ]
                        ),
                        'label' => __('Edit'),
                    ]
                ];
            }
        }

        return $dataSource;
    }
}
