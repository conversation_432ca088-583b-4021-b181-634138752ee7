<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Ui\Column;

use Magento\Directory\Api\CountryInformationAcquirerInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class Countries extends Column
{
    /**
     * @param ContextInterface $context
     * @param CountryInformationAcquirerInterface $countryInformationAcquirer
     * @param UiComponentFactory $uiComponentFactory
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        private readonly CountryInformationAcquirerInterface $countryInformationAcquirer,
        UiComponentFactory $uiComponentFactory,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * @param array{data?: array{items: array{id: string}[]}} $dataSource
     *
     * @return array{data?: array{items: mixed[]}}
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) { //phpcs:ignore
                $item[$this->getData('name')] = $this->prepareCountries(
                    (string) $item[$this->getData('name')]
                );
            }
        }

        return $dataSource;
    }

    /**
     * @param string $countries
     * @return string
     */
    private function prepareCountries(string $countries): string
    {
        if (empty($countries)) {
            return '';
        }

        $countriesArr = explode(',', $countries);
        $list = '<ul>';

        foreach ($countriesArr as $countryId) {
            $countryInfo = $this->countryInformationAcquirer->getCountryInfo($countryId);
            $list .= sprintf(
                '<li>%s (%s)</li>',
                $countryInfo->getFullNameEnglish(),
                $countryId
            );
        }

        $list .= '</ul>';

        return $list;
    }
}
