<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Controller\Adminhtml\Template;

use Comave\MarketplaceCatalogImport\Model\ResourceModel\ExportEntity\Collection;
use Comave\MarketplaceCatalogImport\Model\ResourceModel\ExportEntity\CollectionFactory;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterfaceFactory;
use Comave\MarketplaceCatalogImport\Api\ExportEntityRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;

class Save extends \Magento\Backend\App\Action implements HttpPostActionInterface
{
    public const string ADMIN_RESOURCE =
        'Comave_MarketplaceCatalogImportAdminUi::manage_import_templates';

    /**
     * @param Context $context
     * @param ExportEntityInterfaceFactory $modelFactory
     * @param ExportEntityRepositoryInterface $exportEntityRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        Context $context,
        private readonly ExportEntityInterfaceFactory $modelFactory,
        private readonly ExportEntityRepositoryInterface $exportEntityRepository,
        private readonly CollectionFactory $collectionFactory,
    ) {
        parent::__construct($context);
    }

    /**
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute(): Redirect
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setPath('export_entity/list/index');
        /** @var mixed[]|null $params */
        $params = $this->getRequest()->getParam('general');

        if (!$params) {
            $this->getMessageManager()->addErrorMessage(
                (string) __('Invalid request')
            );

            return $resultRedirect;
        }

        try {
            /** @var string|null $id */
            $id = $params[ExportEntityInterface::ID] ?? null;
            $template = !empty($id) ?
                $this->exportEntityRepository->get(
                    (int) $id,
                ) :
                $this->modelFactory->create();

            $countryIds = implode(',', $params[ExportEntityInterface::COUNTRY_ID]);

            if (empty($id)) {
                unset($params['id']);
            }

            $this->checkExistingTemplates($params, $id);

            $template->setEnabled(
                (bool) $params[ExportEntityInterface::ENABLED]
            )->setCountryId(
                $countryIds
            )->setAttributeConfiguration(
                $params[ExportEntityInterface::ATTRIBUTE_CONFIGURATION]
            )->setName(
                strip_tags(
                    $params[ExportEntityInterface::NAME]
                ) ?? 'comave_import_template'
            );

            $this->exportEntityRepository->save($template);
            $this->getMessageManager()->addSuccessMessage(
                (string) __('Successfully saved the export template')
            );
        } catch (\Exception $e) {
            $this->getMessageManager()->addErrorMessage(
                $e->getMessage()
            );
            $resultRedirect->setRefererUrl();
        }

        return $resultRedirect;
    }

    /**
     * @param array $params
     * @param string|null $id
     * @return void
     * @throws LocalizedException
     */
    private function checkExistingTemplates(
        array $params,
        ?string $id = null
    ): void {
        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();

        if (!empty($id)) {
            $collection->addFieldToFilter(
                ExportEntityInterface::ID,
                ['neq' => $id]
            );
        }

        $conditionString = [];

        foreach ($params[ExportEntityInterface::COUNTRY_ID] as $countryId) {
            $conditionString[] = sprintf(
                'FIND_IN_SET("%s",%s)',
                $countryId,
                ExportEntityInterface::COUNTRY_ID,
            );
        }

        $whereCond = implode(' OR ', $conditionString);
        $collection->getSelect()->where(new \Zend_Db_Expr("($whereCond)"));

        if ($collection->getSize()) {
            throw new LocalizedException(
                __('There is a template for the given country list, please review your configurations')
            );
        }
    }
}
