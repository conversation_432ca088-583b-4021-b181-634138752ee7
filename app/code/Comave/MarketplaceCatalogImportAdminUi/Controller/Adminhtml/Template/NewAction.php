<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Controller\Adminhtml\Template;

use Magento\Framework\App\Action\HttpGetActionInterface;

class NewAction extends \Magento\Backend\App\Action implements HttpGetActionInterface
{
    public const string ADMIN_RESOURCE = 'Comave_MarketplaceCatalogImportAdminUi::manage_import_templates';
    /**
     * @inheritDoc
     */
    public function execute()
    {
        $this->_forward('edit'); //@phpstan-ignore-line
    }
}
