<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Controller\Adminhtml\Template;

use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\ExportEntityRepositoryInterface;

class Delete extends \Magento\Backend\App\Action implements HttpPostActionInterface, HttpGetActionInterface
{
    public const string ADMIN_RESOURCE = 'Comave_MarketplaceCatalogImportAdminUi::manage_import_templates';

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Comave\MarketplaceCatalogImport\Api\ExportEntityRepositoryInterface $exportEntityRepository
     */
    public function __construct(
        Context $context,
        private readonly ExportEntityRepositoryInterface $exportEntityRepository
    ) {
        parent::__construct($context);
    }

    /**
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute(): Redirect
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setPath('export_entity/list/index');
        /** @var string $id */
        $id = $this->getRequest()->getParam(ExportEntityInterface::ID);

        if (!$id) {
            $this->getMessageManager()->addErrorMessage(
                (string) __('Invalid request')
            );

            return $resultRedirect;
        }

        try {
            $this->exportEntityRepository->deleteById((int) $id);
            $this->getMessageManager()->addSuccessMessage(
                (string) __('Successfully deleted the template')
            );
        } catch (\Exception $e) {
            $this->getMessageManager()->addErrorMessage(
                $e->getMessage()
            );
        }

        return $resultRedirect;
    }
}
