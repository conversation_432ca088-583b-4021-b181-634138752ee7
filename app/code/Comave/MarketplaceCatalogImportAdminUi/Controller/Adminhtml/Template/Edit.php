<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Controller\Adminhtml\Template;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Registry;
use Magento\Framework\View\Result\Page;
use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\ExportEntityRepositoryInterface;


class Edit extends Action implements HttpGetActionInterface
{
    public const string ADMIN_RESOURCE = 'Comave_MarketplaceCatalogImportAdminUi::manage_import_templates';
    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Comave\MarketplaceCatalogImport\Api\ExportEntityRepositoryInterface $exportEntityRepository
     * @param \Magento\Framework\Registry $registry
     */
    public function __construct(
        Context $context,
        private readonly ExportEntityRepositoryInterface $exportEntityRepository,
        private readonly Registry $registry
    ) {
        parent::__construct($context);
    }

    /**
     * @return \Magento\Framework\View\Result\Page|\Magento\Framework\Controller\Result\Redirect
     */
    public function execute(): Page|Redirect
    {
        /** @var \Magento\Framework\View\Result\Page $resultPage */
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        /** @var \Magento\Framework\Controller\Result\Redirect $redirectResult */
        $redirectResult = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        /** @var string|null $exportTemplateId */
        $exportTemplateId = $this->getRequest()->getParam(ExportEntityInterface::ID);
        $resultPage->getConfig()->getTitle()->set(
            (string) __('New template')
        );

        if (!empty($exportTemplateId)) {
            try {
                $exportTemplate = $this->exportEntityRepository->get(
                    (int) $exportTemplateId
                );
                $this->registry->register('current_template', $exportTemplate);
                $resultPage->getConfig()->getTitle()->set(
                    (string) __('Edit Import Template')
                );
            } catch (\Exception $e) {
                $this->getMessageManager()->addErrorMessage(
                    $e->getMessage()
                );

                return $redirectResult->setRefererUrl();
            }
        }

        return $resultPage;
    }
}
