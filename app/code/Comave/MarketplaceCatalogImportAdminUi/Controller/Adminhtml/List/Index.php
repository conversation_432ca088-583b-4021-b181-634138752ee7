<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Controller\Adminhtml\List;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\Page;

class Index extends \Magento\Backend\App\Action implements HttpGetActionInterface
{
    public const string ADMIN_RESOURCE = 'Comave_MarketplaceCatalogImportAdminUi::manage_import_templates';
    /**
     * @return \Magento\Framework\View\Result\Page
     */
    public function execute(): Page
    {
        /** @var \Magento\Framework\View\Result\Page $resultPage */
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $resultPage->getConfig()->getTitle()->set(
            (string) __('Seller CSV Import Template List')
        );

        return $resultPage;
    }
}
