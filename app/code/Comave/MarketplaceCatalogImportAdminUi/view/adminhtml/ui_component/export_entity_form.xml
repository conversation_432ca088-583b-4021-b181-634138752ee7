<?xml version="1.0"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">export_entity_form.export_entity_form_data_source</item>
        </item>
        <item name="label" translate="true" xsi:type="string">General Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="save" class="Comave\MarketplaceCatalogImportAdminUi\Block\Adminhtml\Form\SaveButton"/>
            <button name="back" class="Comave\MarketplaceCatalogImportAdminUi\Block\Adminhtml\Form\BackButton"/>
        </buttons>
        <namespace>export_entity_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>export_entity_form.export_entity_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="export_entity_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="export_entity/template/save"/>
        </settings>
        <dataProvider name="export_entity_form_data_source"
                      class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="general" sortOrder="10">
        <settings>
            <label translate="true">General Information</label>
        </settings>
        <field name="id" sortOrder="1" formElement="hidden">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">id</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Entity ID</label>
                <dataScope>id</dataScope>
            </settings>
        </field>
        <field name="name" sortOrder="10" formElement="input">
            <settings>
                <dataType>string</dataType>
                <label translate="true">Template Name</label>
                <dataScope>general.name</dataScope>
            </settings>
        </field>
        <field name="enabled" sortOrder="15" formElement="checkbox">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>string</dataType>
                <label translate="true">Enabled</label>
                <dataScope>general.enabled</dataScope>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="number">0</map>
                            <map name="true" xsi:type="number">1</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
        <field name="country_id" formElement="multiselect">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <dataScope>general.country_id</dataScope>
                <label translate="true">Applies to Country</label>
            </settings>
            <formElements>
                <multiselect>
                    <settings>
                        <options class="Magento\Directory\Model\ResourceModel\Country\Collection"/>
                    </settings>
                </multiselect>
            </formElements>
        </field>
    </fieldset>
    <fieldset name="attribute_configuration" sortOrder="30">
        <settings>
            <label translate="true">Attribute Configuration</label>
            <dataScope>general.attribute_configuration</dataScope>
        </settings>

        <dynamicRows name="attribute_config">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="renderDefaultRecord" xsi:type="string">false</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <pageSize>10</pageSize>
                <additionalClasses>
                    <class name="admin__field-wide">true</class>
                    <class name="admin__fieldset">true</class>
                </additionalClasses>
                <dndConfig>
                    <param name="enabled" xsi:type="string">false</param>
                </dndConfig>
                <addButtonLabel translate="true">Add New Configuration</addButtonLabel>
                <componentType>dynamicRows</componentType>
                <recordTemplate>record</recordTemplate>
            </settings>
            <container name="record" component="Magento_Ui/js/dynamic-rows/record">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="isTemplate" xsi:type="boolean">true</item>
                        <item name="is_collection" xsi:type="boolean">true</item>
                        <item name="componentType" xsi:type="string">container</item>
                        <item name="dataScope" xsi:type="string">record</item>
                    </item>
                </argument>
                <field name="attribute_code" component="Magento_Ui/js/form/element/select" formElement="select" sortOrder="10">
                    <settings>
                        <label translate="true">Attribute</label>
                        <dataType>text</dataType>
                        <dataScope>attribute_code</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Comave\MarketplaceCatalogImportAdminUi\Model\Config\Source\ProductAttributes"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <field name="requirement" component="Magento_Ui/js/form/element/select" formElement="select" sortOrder="20">
                    <settings>
                        <label translate="true">Requirement</label>
                        <dataType>text</dataType>
                        <dataScope>requirement</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Comave\MarketplaceCatalogImportAdminUi\Model\Config\Source\Requirements"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <actionDelete>
                    <settings>
                        <label translate="true">Actions</label>
                        <componentType>actionDelete</componentType>
                        <dataType>text</dataType>
                        <additionalClasses>
                            <class name="data-grid-actions-cell">true</class>
                        </additionalClasses>
                    </settings>
                </actionDelete>
            </container>
        </dynamicRows>
    </fieldset>
</form>
