<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">export_entity_list.export_entity_list_data_source</item>
            <item name="deps" xsi:type="string">export_entity_list.export_entity_list_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>export_entity_list_columns</spinner>
        <deps>
            <dep>export_entity_list.export_entity_list_data_source</dep>
        </deps>
        <buttons>
            <button name="add">
                <url path="export_entity/template/new"/>
                <class>save primary</class>
                <label translate="true">Add New Template</label>
            </button>
        </buttons>
    </settings>
    <dataSource name="export_entity_list_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Comave_MarketplaceCatalogImport::manage_import_templates</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="export_entity_list_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <filters name="listing_filters"/>
        <exportButton name="export_button">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">export_entity_list.export_entity_list.export_entity_list_columns.ids</item>
                </item>
            </argument>
        </exportButton>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="export_entity_list_columns">
        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">55</item>
                    <item name="indexField" xsi:type="string">id</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="id">
            <settings>
                <label translate="true">ID</label>
                <dataType>text</dataType>
            </settings>
        </column>
        <column name="name">
            <settings>
                <label translate="true">Name</label>
                <filter>text</filter>
                <dataType>text</dataType>
            </settings>
        </column>
        <column name="enabled" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <label translate="true">Enabled</label>
                <filter>select</filter>
                <dataType>select</dataType>
                <options class="Magento\Config\Model\Config\Source\Yesno"/>
            </settings>
        </column>
        <column name="country_id" class="Comave\MarketplaceCatalogImportAdminUi\Ui\Column\Countries">
            <settings>
                <label translate="true">Applies to countries</label>
                <filter>text</filter>
                <sorting>false</sorting>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
                <dataType>text</dataType>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created At</label>
                <dateFormat>dd-MM-Y H:m:s</dateFormat>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated At</label>
                <dateFormat>dd-MM-Y H:m:s</dateFormat>
            </settings>
        </column>
        <actionsColumn name="actions" class="Comave\MarketplaceCatalogImportAdminUi\Ui\Column\EntityActions">
            <settings>
                <indexField>id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>
