<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Plugin;

use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Magento\Framework\Api\Search\SearchCriteriaInterface;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Magento\Framework\View\Element\UiComponent\DataProvider\Reporting;

class UnserialiseFields
{
    /**
     * @param Reporting $reporting
     * @param AbstractCollection $collection
     * @param SearchCriteriaInterface $searchCriteria
     * @return AbstractCollection
     */
    public function afterSearch(
        Reporting $reporting,
        AbstractCollection $collection,
        SearchCriteriaInterface $searchCriteria
    ): AbstractCollection {
        if ($searchCriteria->getRequestName() !== 'export_entity_form_data_source') {
            return $collection;
        }

        foreach ($collection->getItems() as $item) {
            if (empty($item->getData(ExportEntityInterface::ATTRIBUTE_CONFIGURATION))) {
                continue;
            }

            $item->setData(
                ExportEntityInterface::ATTRIBUTE_CONFIGURATION,
                json_decode($item->getData(ExportEntityInterface::ATTRIBUTE_CONFIGURATION), true)
            );
        }

        return $collection;
    }
}
