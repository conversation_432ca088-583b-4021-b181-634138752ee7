<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="export_entity_list_data_source" xsi:type="string">ExportEntitiesGrid</item>
                <item name="export_entity_form_data_source" xsi:type="string">ExportEntitiesFormDataSource</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="ExportEntitiesFormDataSource" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="const">\Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface::TABLE_NAME</argument>
            <argument name="resourceModel" xsi:type="string">Comave\MarketplaceCatalogImport\Model\ResourceModel\ExportEntity</argument>
        </arguments>
    </virtualType>

    <virtualType name="ExportEntitiesGrid" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="const">\Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface::TABLE_NAME</argument>
            <argument name="resourceModel" xsi:type="string">Comave\MarketplaceCatalogImport\Model\ResourceModel\ExportEntity</argument>
        </arguments>
    </virtualType>
</config>
