<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Comave_MarketplaceCatalogImportAdminUi::manage_export_templates"
             title="Manage Seller Catalog CSV Import Templates"
             module="Comave_MarketplaceCatalogImportAdminUi"
             resource="Comave_MarketplaceCatalogImportAdminUi::manage_export_templates"
             parent="Magento_Catalog::catalog"
             action="export_entity/list/index"
             sortOrder="99"/>
    </menu>
</config>
