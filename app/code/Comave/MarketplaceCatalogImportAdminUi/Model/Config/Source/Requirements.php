<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Model\Config\Source;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Data\OptionSourceInterface;

class Requirements implements OptionSourceInterface
{
    public const int REQUIRED = 1;
    public const int OPTIONAL = 0;
    /**
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            [
                'value' => self::OPTIONAL,
                'label' => __('Optional')
            ],
            [
                'value' => self::REQUIRED,
                'label' => __('Required')
            ]
        ];
    }
}
