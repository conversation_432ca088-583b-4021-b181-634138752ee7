<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Model\Config\Source;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Data\OptionSourceInterface;

class ProductAttributes implements OptionSourceInterface
{
    /**
     * @param ProductAttributeRepositoryInterface $productAttributeRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
    ) {
    }

    /**
     * @return array
     */
    public function toOptionArray(): array
    {
        $list = [];
        $productAttributes = $this->productAttributeRepository->getList(
            $this->searchCriteriaBuilder->create()
        );

        foreach ($productAttributes->getItems() as $attribute) {
            $list[] = [
                'value' => $attribute->getAttributeCode(),
                'label' => sprintf(
                    '%s (%s)',
                    $attribute->getDefaultFrontendLabel(),
                    $attribute->getAttributeCode()
                )
            ];
        }

        return $list;
    }
}
