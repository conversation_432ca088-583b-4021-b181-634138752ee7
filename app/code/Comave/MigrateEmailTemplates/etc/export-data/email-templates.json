[{"template_id": "2", "template_code": "<PERSON><PERSON> - WeltPixel", "template_text": "        <!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\r\n<head>\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta name=\"viewport\" content=\"initial-scale=1.0, width=device-width\" />\r\n    <meta name=\"x-apple-disable-message-reformatting\" />\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\r\n    <style type=\"text/css\">\r\n        {{var template_styles|raw}}\r\n\r\n        {{css file=\"css/email.css\"}}\r\n    </style>\r\n</head>\r\n<body style=\"padding:0 !important;\">\r\n{{inlinecss file=\"css/email-inline.css\"}}\r\n\r\n<!-- Begin wrapper table -->\r\n<table class=\"wrapper\" width=\"100%\">\r\n    <tr>\r\n        <td class=\"wrapper-inner\" align=\"center\">\r\n            <table class=\"main\" align=\"center\">\r\n                <tr>\r\n                    <td class=\"header\" align=\"center\" style=\"padding: 20px;\">\r\n                        {{layout handle=\"light_logo\" area=\"frontend\"}}\r\n                    </td>\r\n                </tr>\r\n                <tr>\r\n                    <td>\r\n                        \r\n                    </td>\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"main-content\" style=\"padding:0 !important\">\r\n                        <!-- Begin Content -->", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"<PERSON>er\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2024-05-28 05:00:29", "orig_template_code": "design_email_header_template", "orig_template_variables": "{\"var logo_height\":\"Email Logo Image Height\",\"var logo_width\":\"Email Logo Image Width\",\"var template_styles|raw\":\"Template CSS\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "5", "template_code": "<PERSON><PERSON> - WeltPixel", "template_text": "<!--@subject {{trans \"Footer\"}} @-->\n<!--@vars {\n\"var store.frontend_name\":\"Store Name\"\n} @-->\n\n<!-- End Content -->\n</td>\n</tr>\n<tr>\n    <table style=\"width: 660px; border-collapse: unset;\" cellpadding=\"5\" align=\"center\">\n        <tr style=\"height: 35px\"><td colspan=\"2\"></td></tr>\n        <tr style=\"border-top: 2px solid #cfd4d4\"><td colspan=\"2\"></td></tr>\n        <tr style=\"\">\n            <td style=\"vertical-align: middle; text-align: center; width: 45%; \">\n                {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_social_media_email_block\" }}\n                <a  style=\"font-weight: bold; padding: 0 10px;text-decoration: none;color: #5f6368;font-size:13px;\" href=\"https://dev.comave.com/terms-and-conditions\" target=\"_blank\" >{{trans 'TERMS & CONDITIONS'}}</a>\n                <a  style=\" font-weight: bold; padding: 0 10px;text-decoration: none;color:#5f6368;font-size:13px;\" href=\"https://dev.comave.com/contact-us\" target=\"_blank\" >{{trans 'CONTACT'}}</a>\n\n            </td>\n            <td style=\"vertical-align: middle; text-align: center;width: 45%;\">\n                <!--  {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_social_media_email_block\" }}-->\n\n                {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"email_footer_media_block\" }}\n            </td>\n        </tr>\n        <tr style=\"border-bottom: 2px solid #cfd4d4\"><td colspan=\"2\"></td></tr>\n    </table>\n</tr>\n\n<tr>\n    <td>\n        <div style=\"padding: 10px 0; text-align: center; font-size: 1em; color:#fff !important; background:#5C5D60; width: 660px; margin:auto;margin-top: 10px !important;\">\n            <p style=\"text-align: center; font-size: 1em; color:#fff !important;; background: #5C5D60; width:660px; margin:auto;\">ComAve | Société à responsabilité limitée, 33, Rue du Puits Romain L-8070 Bertrange  Luxembourg</p>\n            <h3 style=\"display:inline-block !important; margin:0 !important; color:#fff !important;\">{{trans 'Copyright © 2025 ComAve. All Rights Reserved'}}</h3>\n    </td>\n</tr>\n\n\n</table>\n</td>\n</tr>\n</table>\n<!-- End wrapper table -->\n</body>\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Footer\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2025-05-19 18:39:03", "orig_template_code": "design_email_footer_template", "orig_template_variables": "{\"var store.frontend_name\":\"Store Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "8", "template_code": "New Order - WeltPixel", "template_text": "{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\r\n{{layout handle=\"order_markup\" order=$order order_id=$order_id area=\"frontend\"}}\r\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\r\n    <tbody>\r\n        <tr>\r\n            <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\r\n                <h3 style=\"text-align: center; text-transform: uppercase;\">\r\n                    {{trans 'We\\'re on it.'}}\r\n                </h3>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\r\n                <h1 style=\"text-align: center; margin: 0 !important\">\r\n                    {{trans 'We just received your order!'}}\r\n                </h1>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\r\n                <h3 style=\"text-align: center;\">\r\n                    {{trans 'ORDER NUMBER: <span class=\"no-link\">%increment_id</span>' increment_id=$order.increment_id |raw}}\r\n                </h3>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\r\n    <tbody>\r\n        <tr>\r\n            <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n                <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\r\n                    {{trans 'Stay close! We will send you update along the way!'}}\r\n                </h2>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"margin-left: 0px\">\r\n                <p>\r\n                    {{trans 'You can view the entire status of your order by checking <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"margin-left: 0px\">\r\n                <p>\r\n                    {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n<table style=\"width: 660px\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            {{depend order_data.email_customer_note}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var order_data.email_customer_note|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n            {{layout handle=\"weltpixel_sales_email_order_items\" order=$order order_id=$order_id area=\"frontend\"}}\r\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\r\n                <tr>\r\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\r\n                        <h3>{{trans \"BILLING ADDRESS\"}}</h3>\r\n                        <p>{{var formattedBillingAddress|raw}}</p>\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\r\n                        <h3>{{trans \"SHIPPING ADDRESS\"}}</h3>\r\n                        <p>{{var formattedShippingAddress|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important\">\r\n                        <h3>{{trans \"PAYMENT METHOD\"}}</h3>\r\n                        {{var payment_html|raw}}\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\r\n                        <h3>{{trans \"SHIPPING METHOD\"}}</h3>\r\n                        <p>{{var order.shipping_description}}</p>\r\n                        {{if shipping_msg}}\r\n                        <p>{{var shipping_msg}}</p>\r\n                        {{/if}}\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\" align=\"center\">\r\n            <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n                <tr>\r\n                    <td>\r\n                        <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                            <tr>\r\n                                <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                                    <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\r\n                                </td>\r\n                            </tr>\r\n                        </table>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\" style=\"padding-top: 35px\">\r\n            {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_custom_block_returns\"}}\r\n        </td>\r\n    </tr>\r\n</table>\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your %store_name order confirmation\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2025-01-10 09:37:47", "orig_template_code": "new_order_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var order_data.email_customer_note\":\"Email Order Note\",\"var order.billing_address.name\":\"Guest Customer Name\",\"var order.created_at_formatted\":\"Order Created At (datetime)\",\"var order.increment_id\":\"Order Id\",\"layout handle=\\\"sales_email_order_items\\\" order=$order\":\"Order Items Grid\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\",\"var shipping_msg\":\"Shipping message\"}", "is_legacy": "1", "template_preheader": "We just received your order! Stay close! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by checking your account."}, {"template_id": "11", "template_code": "New Order for Guest - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"order_markup\" order=$order order_id=$order_id area=\"frontend\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans 'We\\'re on it.'}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'We just received your order!'}}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans 'ORDER NUMBER: <span class=\"no-link\">%increment_id</span>' increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Stay close! We will send you update along the way!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend order_data.email_customer_note}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var order_data.email_customer_note|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n\n            {{layout handle=\"weltpixel_sales_email_order_items\" order=$order order_id=$order_id area=\"frontend\"}}\n\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                <tr>\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"BILLING ADDRESS\"}}</h3>\n                        <p>{{var formattedBillingAddress|raw}}</p>\n                    </td>\n                    {{depend order_data.is_not_virtual}}\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"SHIPPING ADDRESS\"}}</h3>\n                        <p>{{var formattedShippingAddress|raw}}</p>\n                    </td>\n                    {{/depend}}\n                </tr>\n                <tr>\n                    <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"PAYMENT METHOD\"}}</h3>\n                        {{var payment_html|raw}}\n                    </td>\n                    {{depend order_data.is_not_virtual}}\n                    <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"SHIPPING METHOD\"}}</h3>\n                        <p>{{var order.shipping_description}}</p>\n                        {{if shipping_msg}}\n                        <p>{{var shipping_msg}}</p>\n                        {{/if}}\n                    </td>\n                    {{/depend}}\n                </tr>\n            </table>\n        </td>\n    </tr>\n    <tr>\n        <td colspan=\"2\" style=\"padding-top: 35px\">\n            {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_custom_block_returns\"}}\n        </td>\n    </tr>\n</table>\n\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your %store_name order confirmation\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "new_order_guest_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var order_data.email_customer_note\":\"Email Order Note\",\"var order.billing_address.name\":\"Guest Customer Name\",\"var order.created_at_formatted\":\"Order Created At (datetime)\",\"var order.increment_id\":\"Order Id\",\"layout handle=\\\"sales_email_order_items\\\" order=$order\":\"Order Items Grid\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\",\"var shipping_msg\":\"Shipping message\"}", "is_legacy": "1", "template_preheader": "We just received your order! Stay close! We will send you updates along the way once we dispatch your product(s).You can view the entire status of your orders by creating an account."}, {"template_id": "14", "template_code": "Order Update - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"order_markup\" order=$order order_id=$order_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$order_data.customer_name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans\n                \"ORDER NUMBER: %increment_id \" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your order has been updated!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'You can check the status of your order by logging into <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n            </p>\n        </td>\n    </tr>\n    <tr>\n        <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n            <tr>\n                <td>\n                    <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                        <tr>\n                            <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\n                            </td>\n                        </tr>\n                    </table>\n                </td>\n            </tr>\n        </table>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name order\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "order_update_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var comment\":\"Order Comment\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\"}", "is_legacy": "1", "template_preheader": "We just updated your order information! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by checking your account."}, {"template_id": "17", "template_code": "Order Update for Guest - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"order_markup\" order=$order order_id=$order_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                 {{trans \"Hi %name,\" name=$billing.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans\n                \"ORDER NUMBER: %increment_id \" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your order has been updated!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name order\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "order_update_guest_weltpixel", "orig_template_variables": "{\"var billing.name\":\"Guest Customer Name\",\"var comment\":\"Order Comment\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\"}", "is_legacy": "1", "template_preheader": "We just updated your order information! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by creating an account."}, {"template_id": "20", "template_code": "New Invoice - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"invoice_markup\" invoice=$invoice invoice_id=$invoice_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$order_data.customer_name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"Here\\’s your Invoice :)\" }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your Invoice #%invoice_id' invoice_id=$invoice.increment_id |raw}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'You can view the entire status of your order by checking <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n            </p>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend order_data.email_customer_note}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var order_data.email_customer_note|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n\n            {{layout handle=\"weltpixel_sales_email_order_invoice_items\" invoice=$invoice order=$order  invoice_id=$invoice_id order_id=$order_id area=\"frontend\"}}\n\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                <tr>\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"BILLING ADDRESS\"}}</h3>\n                        <p>{{var formattedBillingAddress|raw}}</p>\n                    </td>\n                    {{depend order_data.is_not_virtual}}\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"SHIPPING ADDRESS\"}}</h3>\n                        <p>{{var formattedShippingAddress|raw}}</p>\n                    </td>\n                    {{/depend}}\n                </tr>\n                <tr>\n                    <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"PAYMENT METHOD\"}}</h3>\n                        {{var payment_html|raw}}\n                    </td>\n                    {{depend order_data.is_not_virtual}}\n                    <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"SHIPPING METHOD\"}}</h3>\n                        <p>{{var order.shipping_description}}</p>\n                        {{if shipping_msg}}\n                        <p>{{var shipping_msg}}</p>\n                        {{/if}}\n                    </td>\n                    {{/depend}}\n                </tr>\n            </table>\n        </td>\n    </tr>\n    <tr>\n        <td colspan=\"2\" align=\"center\">\n            <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n                <tr>\n                    <td>\n                        <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                            <tr>\n                                <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                    <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\n                                </td>\n                            </tr>\n                        </table>\n                    </td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Invoice for your %store_name order\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "new_invoice_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var comment\":\"Invoice Comment\",\"var invoice.increment_id\":\"Invoice Id\",\"layout area=\\\"frontend\\\" handle=\\\"sales_email_order_invoice_items\\\" invoice=$invoice order=$order\":\"Invoice Items Grid\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": "We just issued the invoice for your order! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by checking your account."}, {"template_id": "23", "template_code": "New Invoice for Guest - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"invoice_markup\" invoice=$invoice invoice_id=$invoice_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center;text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$billing.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"Here\\’s your Invoice :)\" }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your Invoice #%invoice_id' invoice_id=$invoice.increment_id |raw}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend order_data.email_customer_note}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var order_data.email_customer_note|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n\n            {{layout handle=\"weltpixel_sales_email_order_invoice_items\" invoice=$invoice order=$order invoice_id=$invoice_id order_id=$order_id area=\"frontend\"}}\n\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                <tr>\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"BILLING ADDRESS\"}}</h3>\n                        <p>{{var formattedBillingAddress|raw}}</p>\n                    </td>\n                    {{depend order_data.is_not_virtual}}\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"SHIPPING ADDRESS\"}}</h3>\n                        <p>{{var formattedShippingAddress|raw}}</p>\n                    </td>\n                    {{/depend}}\n                </tr>\n                <tr>\n                    <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"PAYMENT METHOD\"}}</h3>\n                        {{var payment_html|raw}}\n                    </td>\n                    {{depend order_data.is_not_virtual}}\n                    <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"SHIPPING METHOD\"}}</h3>\n                        <p>{{var order.shipping_description}}</p>\n                        {{if shipping_msg}}\n                        <p>{{var shipping_msg}}</p>\n                        {{/if}}\n                    </td>\n                    {{/depend}}\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Invoice for your %store_name order\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "new_invoice_guest_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var billing.name\":\"Guest Customer Name\",\"var comment\":\"Invoice Comment\",\"var invoice.increment_id\":\"Invoice Id\",\"layout handle=\\\"sales_email_order_invoice_items\\\" invoice=$invoice order=$order\":\"Invoice Items Grid\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": "We just issued the invoice for your order! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by creating an account."}, {"template_id": "26", "template_code": "Invoice Update - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"invoice_markup\" invoice=$invoice invoice_id=$invoice_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center;text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$order_data.customer_name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"INVOICE %order_status\" order_status=$order_data.frontend_status_label\" }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans\n                \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your order has been updated!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'You can check the status of your order by logging into <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n            </p>\n        </td>\n    </tr>\n    <tr>\n        <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n            <tr>\n                <td>\n                    <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                        <tr>\n                            <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\n                            </td>\n                        </tr>\n                    </table>\n                </td>\n            </tr>\n        </table>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name invoice\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "invoice_update_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var  order_data.customer_name\":\"Customer Name\",\"var comment\":\"Invoice Comment\",\"var invoice.increment_id\":\"Invoice Id\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\"}", "is_legacy": "1", "template_preheader": "We just updated your order invoice! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by checking your account."}, {"template_id": "29", "template_code": "Invoice Update for Guest - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"invoice_markup\" invoice=$invoice invoice_id=$invoice_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center;text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$billing.getName()}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"INVOICE %order_status\" order_status=$order.getStatusLabel()\" }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans\n                \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your order has been updated!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name invoice\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "invoice_update_guest_weltpixel", "orig_template_variables": "{\"var billing.name\":\"Guest Customer Name\",\"var comment\":\"Invoice Comment\",\"var invoice.increment_id\":\"Invoice Id\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\"}", "is_legacy": "1", "template_preheader": "We just updated your order invoice! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by creating an account."}, {"template_id": "32", "template_code": "New Credit Memo - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"creditmemo_markup\" creditmemo=$creditmemo creditmemo_id=$creditmemo_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$order_data.customer_name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"We just credited your order!\" }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your Credit Memo #%creditmemo_id' creditmemo_id=$creditmemo.increment_id |raw}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'You can view the entire status of your order by checking <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n            </p>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n            {{layout handle=\"weltpixel_sales_email_order_creditmemo_items\" creditmemo=$creditmemo order=$order creditmemo_id=$creditmemo_id  order_id=$order_id }}\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                <tr>\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"BILLING ADDRESS\"}}</h3>\n                        <p>{{var formattedBillingAddress|raw}}</p>\n                    </td>\n                     {{depend order_data.is_not_virtual}}\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"SHIPPING ADDRESS\"}}</h3>\n                        <p>{{var formattedShippingAddress|raw}}</p>\n                    </td>\n                    {{/depend}}\n                </tr>\n                <tr>\n                    <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"PAYMENT METHOD\"}}</h3>\n                        {{var payment_html|raw}}\n                    </td>\n                     {{depend order_data.is_not_virtual}}\n                    <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"SHIPPING METHOD\"}}</h3>\n                        <p>{{var order.shipping_description}}</p>\n                        {{if shipping_msg}}\n                        <p>{{var shipping_msg}}</p>\n                        {{/if}}\n                    </td>\n                    {{/depend}}\n                </tr>\n            </table>\n        </td>\n    </tr>\n    <tr>\n        <td colspan=\"2\" align=\"center\">\n            <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n                <tr>\n                    <td>\n                        <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                            <tr>\n                                <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                    <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\n                                </td>\n                            </tr>\n                        </table>\n                    </td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Credit memo for your %store_name order\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "new_creditmemo_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var comment\":\"Credit Memo Comment\",\"var creditmemo.increment_id\":\"Credit Memo Id\",\"layout handle=\\\"sales_email_order_creditmemo_items\\\" creditmemo=$creditmemo order=$order\":\"Credit Memo Items Grid\",\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": "We just issued the credit memo for your order! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by checking your account."}, {"template_id": "35", "template_code": "New Credit Memo for Guest - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"creditmemo_markup\" creditmemo=$creditmemo creditmemo_id=$creditmemo_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$billing.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"We just credited your order!\"}}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your Credit Memo #%creditmemo_id' creditmemo_id=$creditmemo.increment_id |raw}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n            {{layout handle=\"weltpixel_sales_email_order_creditmemo_items\" creditmemo=$creditmemo order=$order creditmemo_id=$creditmemo_id  order_id=$order_id }}\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                <tr>\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"BILLING ADDRESS\"}}</h3>\n                        <p>{{var formattedBillingAddress|raw}}</p>\n                    </td>\n                    {{depend order_data.is_not_virtual}}\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\n                        <h3>{{trans \"SHIPPING ADDRESS\"}}</h3>\n                        <p>{{var formattedShippingAddress|raw}}</p>\n                    </td>\n                    {{/depend}}\n                </tr>\n                <tr>\n                    <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"PAYMENT METHOD\"}}</h3>\n                        {{var payment_html|raw}}\n                    </td>\n                    {{depend order_data.is_not_virtual}}\n                    <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\n                        <h3>{{trans \"SHIPPING METHOD\"}}</h3>\n                        <p>{{var order.shipping_description}}</p>\n                        {{if shipping_msg}}\n                        <p>{{var shipping_msg}}</p>\n                        {{/if}}\n                    </td>\n                    {{/depend}}\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Credit memo for your %store_name order\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "new_creditmemo_guest_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var comment\":\"Credit Memo Comment\",\"var creditmemo.increment_id\":\"Credit Memo Id\",\"layout handle=\\\"sales_email_order_creditmemo_items\\\" creditmemo=$creditmemo order=$order\":\"Credit Memo Items Grid\",\"var billing.name\":\"Guest Customer Name (Billing)\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": "We just issued the credit memo for your order! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by creating an account."}, {"template_id": "38", "template_code": "Credit Memo Update - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"creditmemo_markup\" creditmemo=$creditmemo creditmemo_id=$creditmemo_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$order_data.customer_name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label\" }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your order has been updated!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'You can check the status of your order by logging into <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n            </p>\n        </td>\n    </tr>\n    <tr>\n        <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n            <tr>\n                <td>\n                    <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                        <tr>\n                            <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\n                            </td>\n                        </tr>\n                    </table>\n                </td>\n            </tr>\n        </table>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name credit memo\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "creditmemo_update_weltpixel", "orig_template_variables": "{\"var comment\":\"Credit Memo Comment\",\"var creditmemo.increment_id\":\"Credit Memo Id\",\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\"}", "is_legacy": "1", "template_preheader": "We just updated the credit memo for your order! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by checking your account."}, {"template_id": "41", "template_code": "Credit Memo Update for Guest - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"creditmemo_markup\" creditmemo=$creditmemo creditmemo_id=$creditmemo_id area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$billing.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans\n                \"ORDER NUMBER: #%increment_id.\" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your order has been updated!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name credit memo\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "creditmemo_update_guest_weltpixel", "orig_template_variables": "{\"var comment\":\"Credit Memo Comment\",\"var creditmemo.increment_id\":\"Credit Memo Id\",\"var billing.name\":\"Guest Customer Name\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\"}", "is_legacy": "1", "template_preheader": "We just updated the credit memo for your order! We will send you updates along the way once we dispatch your product(s). You can view the entire status of your order by creating an account."}, {"template_id": "44", "template_code": "New Shipment - WeltPixel", "template_text": "{{template config_path=\"design/email/header_template\"}}\n<p>\n    {{trans \"****This is an automatically generated email, please do not reply****\"}}\n</p>\n<div style=\"padding: 0 10px 0 10px;\">\n\n    <table align=\"center\" style=\"display: block;  text-align:center; width: 660px;\">\n        <tbody style=\"display: block\">\n        <tr>\n            <td align=\"left\" style=\"padding-top: 10px;padding-bottom:10px;\">\n                <p class=\"greeting\"  style=\"padding-top:10px ;padding-left:20px;\">{{trans \"Hello %name,\" name=$order_data.customer_name}}</p>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n    <table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n        <tbody>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'Thank you for shopping with us. We would like to confirm that your item has been shipped. Your order and shipping details are mentioned below.'}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{layout handle=\"sales_email_order_shipment_track\" shipment_id=$shipment_id order_id=$order_id}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'To know more about the order, payment and shipping details, please visit <a href=\"%account_url\">My Orders</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n                </p>\n            </td>\n        </tr>\n\n        <tr style=\"display: block\">\n            <td class=\"dark\"  style=\"display: block; \">\n                <h3 style=\"text-align: left; letter-spacing: 0.025em;padding-left:20px;\">\n                    {{trans \"ORDER NUMBER: %order_id\" order_id=$order.increment_id}}\n                </h3>\n            </td>\n        </tr>\n        <tr>\n            <td align=\"center\" style=\"\">\n                <h2 style=\"text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;\">\n                    {{trans 'ComAve shipment id:  #%shipment_id' shipment_id=$shipment.increment_id}}\n                </h2>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n    <table style=\"width: 660px\">\n        <tr class=\"email-information\">\n            <td>\n                <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                    <tr>\n                        <td class=\"address-details\" style=\"padding-top: 30px !important;padding-left:20px;\">\n                            <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\n                            <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\n                        </td>\n                        {{depend order_data.is_not_virtual}}\n                        <td class=\"address-details\" style=\"padding-top: 30px !important\">\n                            <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\n                            <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\n                        </td>\n                        {{/depend}}\n                    </tr>\n                    <tr>\n                        <td class=\"method-info wp-method-info\" style=\"padding-bottom: 40px !important;padding-left:20px;\">\n                            <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\n                            {{var payment_html|raw}}\n                        </td>\n                        {{depend order_data.is_not_virtual}}\n                        <td class=\"method-info\" style=\"padding-bottom: 40px !important\">\n                            <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\n                            <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\n                            {{if shipping_msg}}\n                            <p style=\"color: #555656;\">{{var shipping_msg}}</p>\n                            {{/if}}\n                        </td>\n                        {{/depend}}\n                    </tr>\n                </table>\n                {{depend comment}}\n                <table class=\"message-info\">\n                    <tr>\n                        <td>\n                            {{var comment|escape|nl2br}}\n                        </td>\n                    </tr>\n                </table>\n                {{/depend}}\n\n                {{layout handle=\"weltpixel_sales_email_order_shipment_items\" shipment=$shipment order=$order shipment_id=$shipment_id order_id=$order_id}}\n            </td>\n        </tr>\n        <tr>\n            <td colspan=\"2\" align=\"center\">\n                <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n                    <tbody style=\"display: block\">\n                    <tr style=\"display: block\">\n                        <td style=\"display: block\">\n                            <table class=\"inner-wrapper\"  cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                                <tr>\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                        <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold; border:unset !important;\">{{trans \"VIEW ORDER\"}}</a>\n                                    </td>\n                                </tr>\n                            </table>\n                        </td>\n                    </tr>\n                    </tbody>\n                </table>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"margin: 10px 0 !important; padding-left:20px;\">\n                    {{trans 'If you need further assistance with you order please contact us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n                </p>\n            </td>\n        </tr>\n    </table>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your %store_name order has shipped\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2025-01-29 09:54:35", "orig_template_code": "new_shipment_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var comment\":\"Shipment Comment\",\"var shipment.increment_id\":\"Shipment Id\",\"layout handle=\\\"sales_email_order_shipment_items\\\" shipment=$shipment order=$order\":\"Shipment Items Grid\",\"block class='Magento\\\\\\\\Framework\\\\\\\\View\\\\\\\\Element\\\\\\\\Template' area='frontend' template='Magento_Sales::email\\/shipment\\/track.phtml' shipment=$shipment order=$order\":\"Shipment Track Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": "Your products are on the way, stay tuned! You can view the entire status of your order by checking your account. Thank you for your for your purchase and hope to shop with us again soon!"}, {"template_id": "47", "template_code": "New Shipment for Guest - WeltPixel", "template_text": "{{template config_path=\"design/email/header_template\"}}\n<div style=\"padding: 0 10px 0 10px;\">\n    <table align=\"center\" style=\"display: block;  text-align:center; width: 660px;\">\n        <tbody style=\"display: block\">\n        <tr>\n            <td align=\"left\" style=\"padding-top: 10px;padding-bottom:10px;\">\n                <p class=\"greeting\"  style=\"padding-top:20px ;padding-left:20px;\">{{trans \"Hello  %customer_name,\" customer_name=$myvar1}}</p>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n    <table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n        <tbody>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'Thank You for shopping with us. We would like to confirm that your item has been shipped. Your order details are available on the link below.'}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'Use this number to track your package: TR897451258'}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'To know more about the order, payment and shipping details please visit <a href=\"%account_url\">My Order</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n                </p>\n            </td>\n        </tr>\n\n        <tr style=\"display: block\">\n            <td class=\"dark\"  style=\"display: block; \">\n                <h3 style=\"text-align: left; letter-spacing: 0.025em;padding-left:20px;\">\n                    {{trans \"ORDER NUMBER: %order_id\" order_id=$order.increment_id}}\n                </h3>\n            </td>\n        </tr>\n        <tr>\n            <td align=\"center\" style=\"\">\n                <h2 style=\"text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;\">\n                    {{trans 'Your Shipment #%shipment_id' shipment_id=$shipment.increment_id}}\n                </h2>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n    <table style=\"width: 660px\">\n        <tr class=\"email-information\">\n            <td>\n                <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                    <tr>\n                        <td class=\"address-details\" style=\"padding-top: 40px !important;padding-left:20px;\">\n                            <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\n                            <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\n                        </td>\n                        {{depend order_data.is_not_virtual}}\n                        <td class=\"address-details\" style=\"padding-top: 40px !important\">\n                            <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\n                            <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\n                        </td>\n                        {{/depend}}\n                    </tr>\n                    <tr>\n                        <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important;padding-left:20px;\">\n                            <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\n                            {{var payment_html|raw}}\n                        </td>\n                        {{depend order_data.is_not_virtual}}\n                        <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\n                            <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\n                            <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\n                            {{if shipping_msg}}\n                            <p style=\"color: #555656;\">{{var shipping_msg}}</p>\n                            {{/if}}\n                        </td>\n                        {{/depend}}\n                    </tr>\n                </table>\n                {{depend comment}}\n                <table class=\"message-info\">\n                    <tr>\n                        <td>\n                            {{var comment|escape|nl2br}}\n                        </td>\n                    </tr>\n                </table>\n                {{/depend}}\n\n                {{layout handle=\"weltpixel_sales_email_order_shipment_items\" shipment=$shipment order=$order shipment_id=$shipment_id order_id=$order_id}}\n\n                {{layout handle=\"sales_email_order_shipment_track\" shipment_id=$shipment_id order_id=$order_id}}\n\n\n            </td>\n        </tr>\n        <tr>\n            <td colspan=\"2\" align=\"center\">\n                <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n                    <tbody style=\"display: block\">\n                    <tr style=\"display: block\">\n                        <td style=\"display: block\">\n                            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                                <tr>\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                        <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\n                                    </td>\n                                </tr>\n                            </table>\n                        </td>\n                    </tr>\n                    </tbody>\n                </table>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"margin: 10px 0 !important; padding-left:20px;\">\n                    {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"margin: 10px 0 !important; padding-left:20px;\">\n                    {{trans '*Please do not reply to this email, as it does not accommodate replies.'}}\n                </p>\n            </td>\n        </tr>\n    </table>\n</div>\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your %store_name order has shipped\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2025-01-29 09:54:35", "orig_template_code": "new_shipment_guest_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var billing.name\":\"Guest Customer Name\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var comment\":\"Shipment Comment\",\"var shipment.increment_id\":\"Shipment Id\",\"layout handle=\\\"sales_email_order_shipment_items\\\" shipment=$shipment order=$order\":\"Shipment Items Grid\",\"block class='Magento\\\\\\\\Framework\\\\\\\\View\\\\\\\\Element\\\\\\\\Template' area='frontend' template='Magento_Sales::email\\/shipment\\/track.phtml' shipment=$shipment order=$order\":\"Shipment Track Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": "Your products are on the way, stay tuned! You can view the entire status of your order by creating an account. Thank you for your for your purchase and hope to shop with us again soon!"}, {"template_id": "50", "template_code": "Shipment Update - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$order_data.customer_name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"ORDER NUMBER: %increment_id \" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your order has been updated!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'You can check the status of your order by logging into <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n            </p>\n        </td>\n    </tr>\n    <tr>\n        <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n            <tr>\n                <td>\n                    <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                        <tr>\n                            <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\n                            </td>\n                        </tr>\n                    </table>\n                </td>\n            </tr>\n        </table>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name shipment\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "shipment_update_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var comment\":\"Order Comment\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\",\"var shipment.increment_id\":\"Shipment Id\"}", "is_legacy": "1", "template_preheader": "We just updated the shipping information for your recent order!  You can view the entire status of your order by checking your account. Thank you for your for your purchase!"}, {"template_id": "53", "template_code": "Shipment Update for Guest - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$billing.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"ORDER NUMBER: %increment_id \" increment_id=$order.increment_id |raw}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n    <tbody>\n    <tr>\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\n                {{trans 'Your order has been updated!'}}\n            </h2>\n        </td>\n    </tr>\n    <tr>\n        <td style=\"margin-left: 0px\">\n            <p>\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n            </p>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<table style=\"width: 660px\">\n    <tr class=\"email-information\">\n        <td>\n            {{depend comment}}\n            <table class=\"message-info\">\n                <tr>\n                    <td>\n                        {{var comment|escape|nl2br}}\n                    </td>\n                </tr>\n            </table>\n            {{/depend}}\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name shipment\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "shipment_update_guest_weltpixel", "orig_template_variables": "{\"var billing.name\":\"Guest Customer Name\",\"var comment\":\"Order Comment\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\",\"var shipment.increment_id\":\"Shipment Id\"}", "is_legacy": "1", "template_preheader": "We just updated the shipping information for your recent order!  You can view the entire status of your order by creating an account. Thank you for your for your purchase!"}, {"template_id": "56", "template_code": "New Account - WeltPixel", "template_text": "{{template config_path=\"design/email/header_template\"}}\n\n\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\n    <tbody style=\"display: block;\">\n    <tr style=\"display: block;\">\n        <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px;\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"Hello %name,\" name=$customer.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr style=\"display: block;\">\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'Welcome To ComAve! ' }}\n            </h1>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<div style=\"padding:0 10px 0 10px;\">\n\n    <p style=\"margin: 20px 0 !important;padding-left:22px;\">\n        {{trans 'We are thrilled to have you join our community. At ComAve, you can participate in exciting tournaments, play multiple games, and earn amazing rewards.\n        To get started, simply log in to your account and explore the endless possibilities.'}}\n    </p>\n    <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n        <tbody style=\"display: block\">\n        <tr style=\"display: block\">\n            <td style=\"display: block\">\n                <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                    <tr>\n                        <td align=\"center\" style=\"padding: 8px 0 !important\">\n                            <a href=\"{{var baseUrl}}login/\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"LOGIN\"}}</a>\n                        </td>\n                    </tr>\n                </table>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n\n    <p  style=\"padding-left:20px;padding-top: 10px;\">{{trans \"If you have any questions or need assistance, our support team is here to help. Feel free to reach out to <NAME_EMAIL> \"}}</p>\n\n\n    <p  style=\"padding-left:20px; padding-top: 10px;\">{{trans \"Regards,\"}}</p>\n    <p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"The ComAve Team\"}}</p>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Welcome to %store_name\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:17", "modified_at": "2025-05-14 08:02:40", "orig_template_code": "customer_new_account_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var customer.email\":\"Customer Email\",\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": "Welcome, your new account is ready ! To sign in to our site, use the credentials during checkout or on the My Account page. You will be able to checkout faster, check orders status, view past orders and more."}, {"template_id": "59", "template_code": "New Account Confirmed - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"customer_account_action\" customer=$customer area=\"frontend\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$customer.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'You\\'re in :)' }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans 'Your account is now active.'}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n<p style=\"margin: 20px 0 !important\">\n    {{trans\n    'To sign in, use these credentials during checkout or when logging into <a href=\"%customer_url\">your account</a>.'\n    customer_url=$this.getUrl($store,'customer/account/',[_nosid:1])\n    |raw}}\n</p>\n<ul>\n    <li><strong>{{trans \"Email:\"}}</strong> {{var customer.email}}</li>\n    <li><strong>{{trans \"Password:\"}}</strong> <em>{{trans \"Password you set when creating account\"}}</em></li>\n</ul>\n<p style=\"margin: 20px 0 !important\">\n    {{trans\n        'Forgot your account password? Click <a href=\"%reset_url\">here</a> to reset it.'\n\n        reset_url=\"$this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])\"\n    |raw}}\n</p>\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n    <tr>\n        <td>\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                <tr>\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\n                        <a href=\"{{var this.getUrl($store,'/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"START SHOPPING\"}}</a>\n                    </td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Welcome to %store_name\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "customer_new_account_confirmed_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var customer.email\":\"Customer Email\",\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": "Welcome, your new account is ready ! To sign in to our site, use the credentials during checkout or on the My Account page. You will be able to checkout faster, check orders status, view past orders and more."}, {"template_id": "62", "template_code": "New Account Without Password - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"customer_account_action\" customer=$customer area=\"frontend\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$customer.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'You\\'re in :)' }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans 'Your account is now active.'}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n<p style=\"margin: 20px 0 !important\">\n    {{trans\n        'To sign in to our site and set a password, click on the <a href=\"%create_password_url\">link</a>:'\n\n        create_password_url=\"$this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])\"\n    |raw}}\n</p>\n<ul>\n    <li><strong>{{trans \"Email:\"}}</strong> {{var customer.email}}</li>\n</ul>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Welcome to %store_name\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "customer_new_account_no_password_weltpixel", "orig_template_variables": "{ \"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\", \"var customer.email\":\"Customer Email\", \"var customer.name\":\"Customer Name\" }", "is_legacy": "1", "template_preheader": "To sign in to our site, use the credentials during checkout or on the My Account page. You will be able to checkout faster, check orders status, view past orders and more."}, {"template_id": "65", "template_code": "New Account Confirmation Key - WeltPixel", "template_text": "{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\r\n{{layout handle=\"customer_account_action\" customer=$customer area=\"frontend\"}}\r\n\r\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\r\n    <tbody>\r\n    <tr>\r\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\r\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\r\n                {{trans \"Hi %name,\" name=$customer.name}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans 'You\\'re almost in :)' }}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans 'Your need to confirm your email.'}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n\r\n\r\n<p style=\"margin: 20px 0 !important\">{{trans \"You must confirm your %customer_email email before you can sign in (link is only valid once):\" customer_email=$customer.email}}</p>\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                        <a href=\"{{var this.getUrl($store,'customer/account/confirm/',[_query:[id:$customer.id,key:$customer.confirmation,back_url:$back_url],_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"CONFIRM YOUR ACCOUNT\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Please confirm your %store_name account\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2025-01-21 13:49:12", "orig_template_code": "customer_new_account_confirmation_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var customer.email\":\"Customer Email\",\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": "To sign in to our site, use the credentials during checkout or on the My Account page. You will be able to checkout faster, check orders status, view past orders and more."}, {"template_id": "68", "template_code": "Remind Password - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"customer_action\" customer=$customer area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hi %name,\" name=$customer.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'Do you want to change your password?' }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans 'Okay, we’re cool with that.'}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n<p style=\"margin: 20px 0 !important\"> {{trans \"If you requested this change, set a new password here:\"}}</p>\n\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n    <tr>\n        <td>\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                <tr>\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\n                        <a href=\"{{var this.getUrl($store,'customer/account/createPassword',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"SET IT!\"}}</a>\n                    </td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n\n<p>{{trans \"If you did not make this request, you can ignore this email and your password will remain the same.\"}}</p>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Reset your %store_name password\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "customer_new_pass_weltpixel", "orig_template_variables": "{\"var this.getUrl(store, 'customer/account/')\":\"Customer Account URL\",\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": "Do you want to change your password? Ok, we’re cool with that! If you did not make this request, you can ignore this email and your password will remain the same."}, {"template_id": "71", "template_code": "Reset Password - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"customer_action\" customer=$customer area=\"frontend\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"SHHH ...\"}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'Passwords are hard to remember!' }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans 'Totally get it.'}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n<p class=\"greeting\" style=\"margin-top: 35px\">{{trans \"Hello,\"}}</p>\n<br>\n\n<p>\n    {{trans \"We have received a request to change the following information associated with your account at %store_name: password.\" store_name=$store.getFrontendName()}}\n    {{trans 'If you have not authorized this action, please contact us immediately at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}{{depend store_phone}} {{trans 'or call us at <a href=\"tel:%store_phone\">%store_phone</a>' store_phone=$store_phone |raw}}{{/depend}}.\n</p>\n<br>\n\n<p>{{trans \"Thanks,<br>%store_name\" store_name=$store.frontend_name) |raw}}</p>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Reset your %store_name password\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "customer_password_reset_confirmation_weltpixel", "orig_template_variables": "{\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": "Do you want to change your password? Ok, we’re cool with that! If you did not make this request, you can ignore this email and your password will remain the same."}, {"template_id": "74", "template_code": "Change Email - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hello,\"}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'Do you want to change your email address?' }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans 'Okay, we’re cool with that.'}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n\n<p style=\"margin: 20px 0 !important\">\n    {{trans \"We have received a request to change the following information associated with your account at %store_name: email.\" store_name=$store.frontend_name}}\n    {{trans 'If you have not authorized this action, please contact us immediately at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n</p>\n\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your %store_name email has been changed\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "customer_change_email_weltpixel", "orig_template_variables": "{}", "is_legacy": "1", "template_preheader": "Do you want to change your email address associated with your account? Ok, we’re cool with that! If you have not authorized this action, please contact us immediately."}, {"template_id": "77", "template_code": "Change Email And Password - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hello,\"}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'Do you want to change your email/password?' }}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans 'Okay, we’re cool with that.'}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n<p style=\"margin: 20px 0 !important\">\n    {{trans \"We have received a request to change the following information associated with your account at %store_name: email, password.\" store_name=$store.frontend_name}}\n    {{trans 'If you have not authorized this action, please contact us immediately at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}.\n</p>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your %store_name email and password has been changed\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "customer_change_email_password_weltpixel", "orig_template_variables": "{}", "is_legacy": "1", "template_preheader": "Do you want to change your email address or password associated with your account? Ok, we’re cool with that! If you have not authorized this action, please contact us immediately."}, {"template_id": "80", "template_code": "Newsletter Confirmation - WeltPixel", "template_text": "{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\r\n{{layout handle=\"subscription_confirmation_action\" area=\"frontend\"}}\r\n\r\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\r\n    <tbody>\r\n    <tr>\r\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\r\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\r\n                {{trans \"Hello,\"}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans 'Nice to meet you :)'}}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans 'Thank you for subscribing to our newsletter.'}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n\r\n<p style=\"margin: 20px 0 !important\">{{trans \"To begin receiving the newsletter, you must first confirm your subscription by clicking on the following link:\"}}</p>\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                        <a href=\"{{var subscriber_data.confirmation_link}}\" style=\"font-weight: bold\">{{trans 'Yes, I do!'}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Newsletter subscription confirmation\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2024-06-21 11:41:07", "orig_template_code": "newsletter_subscription_confirm_weltpixel", "orig_template_variables": "{\"var customer.name\":\"Customer Name\",\"var subscriber_data.confirmation_link\":\"Subscriber Confirmation URL\"}", "is_legacy": "1", "template_preheader": "Hello, nice to meet you! Thank you for subscribing to our newsletter! To begin receiving the newsletter, you must first confirm your subscription by clinking the link in the email."}, {"template_id": "83", "template_code": "Wishlist Share - WeltPixel", "template_text": "\n{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\n{{layout handle=\"wishlist_sharing_action\" area=\"frontend\"}}\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                {{trans \"Hello, %customer_name wants to share this\" customer_name=$customerName}}\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'Wish List'}}\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n                {{trans 'with somebody special like you!'}}\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n\n<p style=\"margin: 25px 0 !important\">{{trans \"To begin see what %customer_name found and wants to share with you click on the button below :)\" customer_name=$customerName }}</p>\n\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n    <tr>\n        <td>\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                <tr>\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\n                        <a href=\"{{var viewOnSiteLink}}\" style=\"font-weight: bold\">{{trans \"View all items\"}}</a>\n                    </td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n\n{{depend message}}\n<table class=\"message-info\">\n    <tr>\n        <td>\n            <h3>{{trans \"Message from %customer_name:\" customer_name=$customerName}}</h3>\n            {{var message|raw}}\n        </td>\n    </tr>\n</table>\n<br />\n{{/depend}}\n\n{{var items|raw}}\n<br/>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Take a look at %customer_name's Wish List\" customer_name=$customerName}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2023-09-05 06:00:18", "orig_template_code": "wishlist_share_notification_weltpixel", "orig_template_variables": "{\"var customerName\":\"Customer Name\",\"var viewOnSiteLink\":\"View Wish List URL\",\"var items|raw\":\"Wish List Items\",\"var message|raw\":\"Wish List Message\"}", "is_legacy": "1", "template_preheader": "The sender wants to share this Wish List with somebody special like you! To begin see what found and wants to share with you click on the button below."}, {"template_id": "86", "template_code": "Forgot Password - WeltPixel", "template_text": "{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}{{layout handle=\"menu_line\" area=\"frontend\"}}\r\n{{layout handle=\"customer_action\" customer=$customer area=\"frontend\"}}\r\n\r\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\r\n    <tbody>\r\n    <tr>\r\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\r\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\r\n                {{trans \"SHHH ...\"}}\r\n\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans 'Passwords are hard to remember!' }}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans 'Totally get it.'}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n\r\n<p class=\"greeting\" style=\"margin-top: 35px\">{{trans \"Hello,\"}}</p>\r\n<br>\r\n<p style=\"margin: 20px 0 !important\"> {{trans \"If you requested this change, set a new password here:\"}}</p>\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                        <a href=\"{{var this.getUrl($store,'customer/account/createPassword',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"SET IT!\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n<p>{{trans \"If you did not make this request, you can ignore this email and your password will remain the same.\"}}</p>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Reset your %store_name password\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-09-05 06:00:18", "modified_at": "2025-01-14 15:11:08", "orig_template_code": "customer_password_reset_confirmation_weltpixel", "orig_template_variables": "{\"var this.getUrl(store, 'customer/account/')\":\"Customer Account URL\",\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": "Forgot your account password?"}, {"template_id": "125", "template_code": "Seller Approval Notification - Comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\n\n<div style=\"padding: 10px 10px 0 10px;\">\n<p class=\"greeting\" style=\"margin-top: 35px;\nmargin-left:20px;\">{{trans \"Hello %customer_name,\" customer_name=$myvar1}}</p>\n\n<p style=\"margin: 10px 0 !important; padding-left:20px; font-size:15px;\">{{trans \"Great news! Your application to become a seller on ComAve has been approved!\" }}</p>\n<p style=\"margin: 10px 0 !important; padding-left:20px;font-size:15px;\"> {{trans \" Welcome to the ComAve family!\"}}</p>\n\n<p  style=\"padding-left:20px;\">{{trans \"We’re thrilled to have you with us and believe your products will be a fantastic addition to our platform. Let’s create something amazing together.\"}}</p>\n\n\n  <p style=\"padding-left:20px;\">{{trans \"You can now log in to your e-store on our website and start managing your sales.\"}}</p>\n\n <p style=\"padding-left:20px;\">{{trans \"If you have any questions or need help, don’t hesitate to reach out to <NAME_EMAIL>.\"}}</p>\n\n\n <p style=\"padding-left:20px;\">{{trans \"We’re excited to see what you’ll bring to our community and look forward to a wonderful partnership.\"}}</p>\n\n <p style=\"padding-left:20px;\">{{trans \"Thank you for choosing ComAve.\"}}</p>\n\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n    <tr>\n        <td>\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"30%\">\n                <tr>\n                    <td align=\"center\" style=\"padding: 8px 0 !important; font-size:15px; background:#0f152e;\">\n                        <a href=\"{{var this.getUrl($store,'customer/account/login/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold; border: unset !important;\">{{trans \"Sign In\"}}</a>\n                    </td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n\n\n<p  style=\"padding-left:20px;font-size:15px;\">{{trans \"Warm regards,\"}}</p>\n<p  style=\"padding-left:20px;font-size:15px; color:#d91f26 !important;font-weight: bold !important;\">{{trans \"The ComAve Team\"}}</p>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Welcome to ComAve - Your Seller Application has been Approved!\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 05:29:07", "modified_at": "2025-03-03 22:59:11", "orig_template_code": "marketplace_email_seller_approve_notification_template", "orig_template_variables": "{\"var myvar1\":\"Customer Name\",\"var myvar2\":\"Login Url\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "128", "template_code": "Order invoiced notification to seller-comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<table style=\"width:96%; margin:auto;margin-top:10px;\">\r\n    <tr class=\"email-intro\">\r\n        <td>\r\n            <p class=\"greeting\" >{{trans \"Hello\"}}{{trans\" %customer_name,\" customer_name=$myvar3}}</p>\r\n            <p>\r\n                {{trans \"I would like to inform you that one of your order has been invoiced.\"}}\r\n                {{trans 'You can check your order by <a href=\"%account_url\">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-summary\">\r\n        <td>\r\n            <h1>{{trans 'Your Order <span class=\"no-link\">#%increment_id</span>' increment_id=$myvar1 |raw}}</h1>\r\n            <p>{{trans 'Placed on <span class=\"no-link\">%created_at</span>' created_at=$myvar2 |raw}}</p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            <table class=\"order-details\">\r\n                <tr>\r\n                    <td class=\"address-details\">\r\n                        <h3>{{trans \"Billing Info\"}}</h3>\r\n                        <p>{{var myvar4|raw}}</p>\r\n                    </td>\r\n                    {{depend isNotVirtual}}\r\n                    <td class=\"address-details\">\r\n                        <h3>{{trans \"Shipping Info\"}}</h3>\r\n                        <p>{{var myvar6|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Payment Method\"}}</h3>\r\n                        {{var myvar5|raw}}\r\n                    </td>\r\n                    {{depend isNotVirtual}}\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Shipping Method\"}}</h3>\r\n                        <p>{{var myvar9|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n            <table class=\"email-items\">\r\n                <thead>\r\n                    <tr>\r\n                        <th class=\"item-info\">{{trans \"Item\"}}</th>\r\n                        <th class=\"item-info\">{{trans \"Sku\"}}</th>\r\n                        <th class=\"item-qty\">{{trans \"Qty\"}}</th>\r\n                        <th class=\"item-price\">{{trans \"Subtotal\"}}</th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    {{var myvar8|raw}}\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your Invoice  #%invoice_id of your order #%order_id On ComAve\" invoice_id=$invoice.increment_id order_id=$order.increment_id}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 05:54:59", "modified_at": "2024-05-28 05:38:22", "orig_template_code": "marketplace_email_order_invoiced_notification_template", "orig_template_variables": "{\"var this.getUrl($store,'marketplace/order/history/',[_nosid:1]) |raw\":\"Order History URL\",\"var isNotVirtual\":\"isNotVirtual\",\"var myvar1 |raw\":\"Order Id\",\"var myvar2 |raw\":\"Order Placed on\",\"var myvar3 |raw\":\"Customer Name\",\"var myvar4|raw\":\"Billing Info\",\"var myvar5|raw\":\"Payment Method\",\"var myvar6|raw\":\"Shipping Info\",\"var myvar8|raw\":\"Product Info\",\"var myvar9|raw\":\"Shipping Method\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "131", "template_code": "<PERSON><PERSON> Disapproved Notifiction - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n<table>\r\n    <tr class=\"email-intro\">\r\n        <td style=\"padding-bottom: 0px !important;\">\r\n            <p class=\"greeting\" style=\"padding-left:20px; margin-top: 8px;\">{{trans \"Hello%customer_name,\" customer_name=$myvar1}}</p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-summary\">\r\n        <td>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            <table class=\"query-details\">\r\n                <tr>\r\n                    <td class=\"product-details\" style=\"padding-left:20px;\">\r\n                        <p>{{trans \"Thank you for registering with ComAve, but after careful consideration, we regret to inform you that we are unable to approve you as a seller on ComAve. This decision is based on a thorough review of various factors, including our current product offerings, market dynamics, and our strategic direction. We appreciate the time and effort you invested in your application. \"}}</p>\r\n  <p>{{trans \"We appreciate your understanding in this matter and wish you the best in your future endeavors.  \"}}</p>\r\n  <p>{{trans \"Thank you once again for considering ComAve as a platform for your products.  \"}}</p>\r\n\r\n <p>{{trans \"If you have any questions or would like specific feedback on your application, please feel free to reach <NAME_EMAIL> .\"}}</p>\r\n\r\n<p>{{trans \"Regards,\"}}</p>\r\n<p  style=\"color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n                     \r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n</div>\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "Sorry - Your Seller Application has been Disapproved!     ", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:23:28", "modified_at": "2024-05-28 04:47:32", "orig_template_code": "marketplace_email_seller_disapprove_notification_template", "orig_template_variables": "{\"var myvar1|raw\":\"Customer Name\",\"var myvar2|raw\":\"Login Url\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "137", "template_code": "Unsubcription Success  backup - Comave", "template_text": "{{var customer.name}}{{template config_path=\"design/email/header_template\"}}\r\n\r\n<dic style=\"padding: 0 10px 0 10px;\">\r\n\r\n<!--<p class=\"greeting\" style= \"margin-left:10px;\">{{trans \"%name,\" name=$customer_name}}</p>-->\r\n<p class=\"greeting\"  style=\"padding-top:20px ;padding-left:11px;\">{{trans \"Hello %name,\" name=$customer.name}}</p>\r\n<p style=\"margin-left : 10px;margin-right:2px;\">{{trans \"You have been unsubscribed from ComAve Community email list. We just wanted you to know that your email address has been removed from our subscriber list and no additional newsletters or promotions will be sent.\"}}</p>\r\n<p style=\"margin-left : 10px;\">{{trans \"If you change your mind then you may subscribe again at any time by visiting our website.\"}}</p>\r\n<p style=\"margin-left : 10px;\">{{trans \"If you still like to learn about new releases, sales, etc. connect with us on the social network(s) of your choice.\"}}</p>\r\n\r\n<p style=\"margin-left : 10px;\">{{trans \"Thank you for being a part of the ComAve community.\"}}</p>\r\n<p style=\"margin-left : 10px;\">{{trans \"Best Wishes,\"}}</p>\r\n<p style=\"margin-left : 10px; color:#d91f26 !important; font-weight:bold;\">{{trans \"ComAve\"}}</p>\r\n\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"We’re sorry to see you go!\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:31:45", "modified_at": "2024-05-28 05:04:41", "orig_template_code": "newsletter_subscription_un_email_template", "orig_template_variables": "{\"template config_path=\\\"design\\/email\\/footer_template\\\"\":\"Email Footer Template\",\"template config_path=\\\"design\\/email\\/header_template\\\"\":\"Email Header Template\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "140", "template_code": "<PERSON>drawal Request -comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<table>\r\n    <tr class=\"email-intro\">\r\n        <td style=\"padding-left: 10px;\">\r\n            <p class=\"greeting\">{{trans \"Hello \"}}</p>\r\n            <p class=\"greeting\">{{trans \"Request to withdraw an amount \"}}{{var amount|raw}}{{trans \" is received from seller %seller.\" seller=$seller}}</p>\r\n            <p>\r\n                {{trans 'Please check after <a href=\"%account_url\">logging into your account</a>.' account_url=$this.getUrl($store,'admin/',[_nosid:1]) |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": ".email-intro{\r\npadding-top:10px;\r\n}", "template_type": "2", "template_subject": "Withdrawal request from <PERSON><PERSON>", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:34:47", "modified_at": "2024-05-28 05:32:12", "orig_template_code": "marketplace_email_withdrawal_request_template", "orig_template_variables": "{\"var seller\":\"Seller Name\",\"var amount|raw\":\"Amount\",\"var this.getUrl($store,'admin/',[_nosid:1]) |raw\":\"Login Url\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "143", "template_code": "Payment Notification - Comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<table>\r\n    <tr class=\"email-intro\" >\r\n        <td style=\"padding-left: 10px;\">\r\n            <p class=\"greeting\" style=\"padding-top: 10px;\">{{trans \"Hello \"}}{{trans \"%customer_name,\" customer_name=$myvar1}}</p>\r\n            <p style=\"padding-top: 10px;\">\r\n                {{trans \"I would like to inform you that your payment for your order(s) has been successfully done.\"}}\r\n                {{trans 'You can check your transaction by <a href=\"%account_url\">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-summary\">\r\n        <td style=\"padding-left: 10px;\">\r\n            <h1 style=\"padding-top: 10px;\">{{trans 'Transaction Id <span class=\"no-link\">#%increment_id</span>' increment_id=$myvar2 |raw}}</h1>\r\n            <p>{{trans 'Placed on <span class=\"no-link\">%created_at</span>' created_at=$myvar3 |raw}}</p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-information\">\r\n        <td style=\"padding-left: 10px;\">\r\n            <table class=\"order-details\">\r\n                <tr>\r\n                    <td class=\"address-details\">\r\n                        <h3>{{trans \"Transaction Id\"}}</h3>\r\n                        <p>{{var myvar2|raw}}</p>\r\n                    </td>\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Transaction Amount\"}}</h3>\r\n                        {{var myvar4|raw}}\r\n                    </td>\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Comment Message\"}}</h3>\r\n                        {{var myvar6|raw}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            <table class=\"email-items\">\r\n                <thead>\r\n                    <tr>\r\n                        <th class=\"item-info\">Order Id #</th>\r\n                        <th class=\"item-info\">Product Name</th>\r\n                        <th class=\"item-qty\">Qty</th>\r\n                        <th class=\"item-price\">Price</th>\r\n                        <th class=\"item-price\">Commission</th>\r\n                        <th class=\"item-price\">Total payout amount</th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    {{var myvar5|raw}}\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "Payment Notification To Seller {{var customer.name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:37:16", "modified_at": "2024-05-28 05:31:29", "orig_template_code": "checkout_payment_failed_template", "orig_template_variables": "{\"var billingAddressHtml|raw\":\"Billing Address\",\"var checkoutType\":\"Checkout Type\",\"var customerEmail\":\"Customer Email\",\"var customer\":\"Customer Name\",\"var dateAndTime\":\"Date and Time of Transaction\",\"var paymentMethod\":\"Payment Method\",\"var shippingAddressHtml|raw\":\"Shipping Address\",\"var shippingMethod\":\"Shipping Method\",\"var items|raw\":\"Shopping Cart Items\",\"var total\":\"Total\",\"var reason\":\"Transaction Failed Reason\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "146", "template_code": "New Reset Password", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n\r\n<p class=\"greeting\" style=\"margin-top: 35px; margin-left:20px;\">{{trans \"Hello, %name\" name=$customer.name}}</p>\r\n<br>\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\"> {{trans \" There was recently a request to change the password for your account.\"}}</p>\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\"> {{trans \"If you requested this change, set a new password here:\"}}</p>\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"50%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important; \">\r\n                        <a href=\"{{var this.getUrl($store,'customer/account/createPassword',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Set Password\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n<p style=\"padding-left:20px;\">{{trans \"If you did not make this request, you can ignore this email and your password will remain the same.\"}}</p>\r\n<p  style=\"padding-left:20px;\">{{trans \"If you didn't initiate this request, please contact us <NAME_EMAIL>\"}}</p>\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;\">{{trans \"Comave\"}}</p>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Reset your %store_name password\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:38:54", "modified_at": "2024-05-28 05:27:00", "orig_template_code": "lix_reset_template", "orig_template_variables": "{\"var myvar1\":\"Customer Name\",\"var myvar2\":\"Login Url\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "149", "template_code": "ushop_header_template", "template_text": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\r\n<head>\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta name=\"viewport\" content=\"initial-scale=1.0, width=device-width\" />\r\n    <meta name=\"x-apple-disable-message-reformatting\" />\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\r\n    <style type=\"text/css\">\r\n        {{var template_styles|raw}}\r\n\r\n        {{css file=\"css/email.css\"}}\r\n    </style>\r\n</head>\r\n<body style=\"padding:0 !important;\">\r\n{{inlinecss file=\"css/email-inline.css\"}}\r\n\r\n<!-- Begin wrapper table -->\r\n<table class=\"wrapper\" width=\"100%\">\r\n    <tr>\r\n        <td class=\"wrapper-inner\" align=\"center\">\r\n            <table class=\"main\" align=\"center\">\r\n                <tr>\r\n               \r\n                    <td class=\"header\" align=\"center\" >\r\n<span style= \"margin-left:25px !important; border-right: 20px !important;padding-right: 5px;\">\r\n                        {{layout handle=\"light_logo\" area=\"frontend\"}}\r\n</span>\r\n\r\n<span style= \"margin-left: 5px !important;display: inline-block;border-left: 5px solid #ccc; margin: 0 54px;height: 62px; padding-left: 5px;\">\r\n                       {{layout handle=\"dark_logo\" area=\"frontend\"}} \r\n</span>\r\n                    </td>\r\n                </tr>\r\n               \r\n                <tr>\r\n                    <td class=\"main-content\" style=\"padding:0 !important\">\r\n                        <!-- Begin Content -->", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"<PERSON>er\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:40:02", "modified_at": "2023-10-04 06:40:02", "orig_template_code": "design_email_header_template", "orig_template_variables": "{\"var logo_url\":\"Email Logo Image URL\",\"var logo_alt\":\"Email Logo Alt Text\",\"var logo_height\":\"Email Logo Image Height\",\"var logo_width\":\"Email Logo Image Width\",\"var template_styles|raw\":\"Template CSS\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "152", "template_code": "Product In Low Stock", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n  <p class=\"greeting\"  style=\"padding-top:20px ;padding-left:20px;\">{{trans \"Hello\"}} {{var customerName}},</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"You’ve received this alert because the product you have added to the Wishlist is low in stock..\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"These types of products sometimes will be back or won’t. Shop quickly as you could. No guarantees that it will stay for too long!\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Add the product to your cart and place the order while it’s available. Product image: Product name (link)\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"If you have any questions, please contact us at <EMAIL>\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"***Please do not reply to this email, as it does not accommodate replies.***\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"If you would prefer not to receive our emails, please click here to unsubscribe.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Thank You.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Regards\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve Team\"}}</p>\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "HURRY! Your favorite product is running out of stock!", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:41:16", "modified_at": "2023-10-04 06:41:16", "orig_template_code": "marketplace_email_low_stock_template", "orig_template_variables": "{\"var this.getUrl($store,'marketplace/product/productlist/',[_nosid:1]) |raw\":\"Customer Account URL\",\"var customer.name\":\"Customer Name\",\"var myvar1|raw\":\"Product Info\",\"var myvar2\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "155", "template_code": "Product Back In Stock!", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n  <p class=\"greeting\"  style=\"padding-top:20px ;padding-left:20px;\">{{trans \"Hello %customer_name,\" customer_name=$customer_name}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"We are happy to notify you that your wait is over. Your Wishlist item(s) is in stock and available now.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Product details: \"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"We don’t guarantee the availability of the product if not purchased fast.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"***Please do not reply to this email, as it does not accommodate replies.***\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"You have received this notification because you are subscribed for our “out of stock” alerts. You can manage your subscriptions on your Dashboard. \"}}</p>\r\n\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"If you would prefer not to receive our emails, please click here to unsubscribe.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Thank You.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Regards\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve Team\"}}</p>\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "Congratulations! It’s back in stock!", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:42:28", "modified_at": "2023-10-04 06:42:28", "orig_template_code": "catalog_productalert_email_stock_template", "orig_template_variables": "{\"var alertGrid|raw\":\"Alert Data Grid\",\"var customerName\":\"Customer Name\",\"template config_path=\\\"design\\/email\\/footer_template\\\"\":\"Email Footer Template\",\"template config_path=\\\"design\\/email\\/header_template\\\"\":\"Email Header Template\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "158", "template_code": "Reset password-Admin", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<div style=\"margin: 10px auto;\">\r\n<p class=\"greeting\" style=\"padding-left: 20px;\">{{trans \"%name,\" name=$customer.name}}</p>\r\n<p style=\"padding-left: 20px;\">{{trans \"There was recently a request to change the password for your account.\"}}</p>\r\n<p style=\"padding-left: 20px;\">{{trans \"If you requested this change, set a new password here:\"}}</p>\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\">\r\n                <tr>\r\n                    <td align=\"center\">\r\n                        <a  style=\"padding-left: 20px;\" href=\"{{var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}\" target=\"_blank\">{{trans \"Set a New Password\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n<p style=\"padding-left: 20px;\" >{{trans \"If you did not make this request, you can ignore this email and your password will remain the same.\"}}</p>\r\n</div>\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Reset your %store_name password\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:43:27", "modified_at": "2024-04-30 09:55:57", "orig_template_code": "lix_reset_template", "orig_template_variables": "{\"var myvar1\":\"Customer Name\",\"var myvar2\":\"Login Url\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "161", "template_code": "Food Email Footer - WeltPixel", "template_text": "<!--@subject {{trans \"Footer\"}} @-->\n<!--@vars {\n\"var store.frontend_name\":\"Store Name\"\n} @-->\n\n<!-- End Content -->\n</td>\n</tr>\n<tr>\n    <table style=\"width: 660px; border-collapse: unset;\" cellpadding=\"5\" align=\"center\">\n        <tr style=\"height: 35px\"><td colspan=\"2\"></td></tr>\n        <tr style=\"border-top: 2px solid #cfd4d4\"><td colspan=\"2\"></td></tr>\n        <tr style=\"\">\n            <td style=\"vertical-align: middle; text-align: center; width: 45%; \">\n                {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_social_media_email_block\" }}\n                <a  style=\"font-weight: bold; padding: 0 10px;text-decoration: none;color: #5f6368;font-size:13px;\" href=\"https://dev.comave.com/terms-and-conditions\" target=\"_blank\" >{{trans 'TERMS & CONDITIONS'}}</a>\n                <a  style=\" font-weight: bold; padding: 0 10px;text-decoration: none;color:#5f6368;font-size:13px;\" href=\"https://dev.comave.com/contact-us\" target=\"_blank\" >{{trans 'CONTACT'}}</a>\n\n            </td>\n            <td style=\"vertical-align: middle; text-align: center;width: 45%;\">\n                <!--  {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_social_media_email_block\" }}-->\n\n                {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"email_footer_media_block\" }}\n            </td>\n        </tr>\n        <tr style=\"border-bottom: 2px solid #cfd4d4\"><td colspan=\"2\"></td></tr>\n    </table>\n</tr>\n\n<tr>\n    <td>\n        <div style=\"padding: 10px 0; text-align: center; font-size: 1em; color:#fff !important; background:#5C5D60; width: 660px; margin:auto;margin-top: 10px !important;\">\n            <p style=\"text-align: center; font-size: 1em; color:#fff !important;; background: #5C5D60; width:660px; margin:auto;\">ComAve | Société à responsabilité limitée, 33, Rue du Puits Romain L-8070 Bertrange  Luxembourg</p>\n            <h3 style=\"display:inline-block !important; margin:0 !important; color:#fff !important;\">{{trans 'Copyright © 2025 ComAve. All Rights Reserved'}}</h3>\n    </td>\n</tr>\n\n\n</table>\n</td>\n</tr>\n</table>\n<!-- End wrapper table -->\n</body>\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Footer\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-10-04 06:46:35", "modified_at": "2025-05-19 18:39:03", "orig_template_code": "design_email_footer_template", "orig_template_variables": "{\"var store.frontend_name\":\"Store Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "167", "template_code": "New Account - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n\r\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\r\n    <tbody style=\"display: block;\">\r\n    <tr style=\"display: block;\">\r\n        <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px;\">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans \"Hello %name,\" name=$customer.name}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block;\">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans 'Welcome To ComAve! ' }}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<div style=\"padding:0 10px 0 10px;\">\r\n\r\n    <p style=\"margin: 20px 0 !important;padding-left:22px;\">\r\n        {{trans 'We are thrilled to have you join our community. At ComAve, you can participate in exciting tournaments, play multiple games, and earn amazing rewards.\r\n        To get started, simply log in to your account and explore the endless possibilities.'}}\r\n    </p>\r\n    <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n        <tbody style=\"display: block\">\r\n        <tr style=\"display: block\">\r\n            <td style=\"display: block\">\r\n                <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                    <tr>\r\n                        <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                            <a href=\"{{var baseUrl}}login/\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"LOGIN\"}}</a>\r\n                        </td>\r\n                    </tr>\r\n                </table>\r\n            </td>\r\n        </tr>\r\n        </tbody>\r\n    </table>\r\n\r\n    <p  style=\"padding-left:20px;padding-top: 10px;\">{{trans \"If you have any questions or need assistance, our support team is here to help. Feel free to reach out to <NAME_EMAIL> \"}}</p>\r\n\r\n\r\n    <p  style=\"padding-left:20px; padding-top: 10px;\">{{trans \"Regards,\"}}</p>\r\n    <p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"The ComAve Team\"}}</p>\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}\r\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Welcome %name to ComAve! Thanks for signing up.\" name=$customer.name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 06:54:19", "modified_at": "2025-05-15 07:01:06", "orig_template_code": "customer_new_account_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var customer.email\":\"Customer Email\",\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "170", "template_code": "New Order - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<p>\r\n          {{trans \"****This is an automatically generated email, please do not reply****\"}}\r\n </p>\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\r\n    <tbody style=\"display: block\">\r\n        <tr style=\"display: block\">\r\n            <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px; \">\r\n                <h3 style=\"text-align: center; text-transform: uppercase;\">\r\n                    {{trans 'Thank You For Your Purchase'}}\r\n                </h3>\r\n            </td>\r\n        </tr>\r\n        <tr style=\"display: block\">\r\n            <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\r\n                <h1 style=\"text-align: center; margin: 0 !important\">\r\n                    {{trans 'We just received your order!'}}\r\n                </h1>\r\n            </td>\r\n        </tr>\r\n        <tr style=\"display: block\">\r\n            <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:8px; \">\r\n                <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                    {{trans 'ORDER NUMBER: <span class=\"no-link\">%increment_id</span>' increment_id=$order.increment_id |raw}}\r\n                </h3>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px;margin-left:12px;\">\r\n    <tbody>\r\n        <tr>\r\n            <td align=\"left\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n        <!--<p class=\"greeting\">{{trans \"Hello %name,\" name=$customer.name}}</p>-->\r\n<p class=\"greeting\">{{trans \"Hello %customer_name,\" customer_name=$order_data.customer_name}}</p>\r\n \r\n\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td>\r\n            <p>\r\n {{trans 'Thank you for shopping with us. Your order is in process. You will receive the invoice email, and once the order is shipped, we will send you an email with your order tracking number.' }}\r\n               </p>\r\n              \r\n\r\n                <p style=\"margin: 10px 0 !important; margin-left:20px;\">\r\n                    {{trans 'If you would like to view the status of your order  <a href=\"%account_url\" style=\"color: blue !important;\">your account</a>'. account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n                </p>\r\n\r\n               <p>\r\n                    <span style= \"color:#d91f26 !important;\">Congratulations! </span>{{trans 'You have earned'}} <span style= \"color:#d91f26 !important;\">\"LIX Rewards\"</span>{{trans ' on this purchase. You can reedem this rewards on your next purchase , or whenever you feel like it.'}} \r\n                </p>\r\n\r\n            </td>\r\n        </tr>\r\n        \r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px;\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            \r\n\r\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\r\n                <tr>\r\n                    <td class=\"address-details\" style=\"padding-top: 20px !important; padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"address-details\" style=\"padding-top: 20px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info wp-method-info\" style=\"padding-bottom:20px !important;padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\r\n                        {{var payment_html|raw}}\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"method-info\" style=\"padding-bottom: 20px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\r\n                        {{if shipping_msg}}\r\n                        <p style=\"color: #555656;\">{{var shipping_msg}}</p>\r\n                        {{/if}}\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n          {{depend order_data.email_customer_note}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var order_data.email_customer_note|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n\r\n            {{layout handle=\"weltpixel_sales_email_order_items\" order=$order order_id=$order_id area=\"frontend\"}}\r\n\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\" align=\"center\">\r\n            <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n                <tbody style=\"display: block\">\r\n                    <tr style=\"display: block\">\r\n                        <td style=\"display: block\">\r\n                            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                                <tr>\r\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                                        <!--<a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>-->\r\n<a href=\"https://mcstaging.comave.com/sales/order/history/\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\r\n\r\n                                    </td>\r\n                                </tr>\r\n                            </table>\r\n                        </td>\r\n                    </tr>\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n<tr>\r\n            <td style=\"margin-left: 0px\">\r\n                <p style=\"margin: 10px 0 !important;padding-left:20px;\">\r\n                    {{trans 'If you have questions about your order, please contact us at <a href=\"mailto:%store_email\" style=\"color: blue !important;\">%store_email</a>' store_email=$store_email |raw}}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n   <tr>\r\n    <td style=\"margin-left: 0px\">\r\n            <p style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                {{trans 'Thank you for shopping with us.'}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n     <!-- <td colspan=\"2\" style=\"padding-top: 35px;padding-left:20px;\">\r\n            {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_custom_block_returns\"}}\r\n        </td>-->\r\n    </tr>\r\n</table>\r\n<p  style=\"padding-left:28px; padding-top: 10px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:28px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Thank You for your order #%increment_id\" increment_id=$order.increment_id}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:40:37", "modified_at": "2024-05-28 05:30:01", "orig_template_code": "new_order_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var order_data.email_customer_note\":\"Email Order Note\",\"var created_at_formatted\":\"Order Created At (datetime)\",\"var order.increment_id\":\"Order Id\",\"layout handle=\\\"sales_email_order_items\\\" order=$order\":\"Order Items Grid\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\",\"var shipping_msg\":\"Shipping message\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "173", "template_code": "New Invoice- comave", "template_text": "{{template config_path=\"design/email/header_template\"}}{{layout handle=\"preheader_section\" area=\"frontend\"}}\r\n<p>\r\n          {{trans \"****This is an automatically generated email, please do not reply****\"}}\r\n </p>\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\r\n<tbody>\r\n <tr>\r\n<td align=\"left\">\r\n            <p class=\"greeting\"  style=\"padding-top:10px ;padding-left:20px;\">{{trans \"Hello %name,\" name=$order_data.customer_name}}</p>\r\n </td>\r\n</tr>\r\n\r\n <td>\r\n            <p style=\"margin: 10px 0 !important; margin-left:20px; margin-right: 2px !important; padding-left: 20px;\">\r\n          {{trans 'Thank you for shopping with ComAve. We have successfully placed your order. You will get a confirmation once your item has been shipped. All your order details are mentioned below. If you would like to view the order status or download the invoice please visit <a href=\"%account_url\">Your Orders</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n </p>\r\n\r\n\r\n<!--<p style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                {{trans 'You can view the entire status of your order by checking <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>-->\r\n        </td>\r\n    </tr>\r\n   \r\n  <tr style=\"display:block;\">\r\n        <td class=\"dark\" align=\"left\" style=\"display:block; \">\r\n            <h3 style=\"text-align: left; letter-spacing: 0.025em;padding-left:20px;\">\r\n                {{trans \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n <tr>\r\n        <td align=\"left\" style=\"\">\r\n            <h2 style=\"text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;\">\r\n                {{trans 'Your Invoice: #%invoice_id' invoice_id=$invoice.increment_id |raw}}\r\n            </h2>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n\r\n<table style=\"width: 660px\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n           \r\n\r\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\r\n                <tr>\r\n                   <td class=\"address-details\" style=\"padding-top: 29px !important; padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"address-details\" style=\"padding-top: 29px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                   <td class=\"method-info wp-method-info\" style=\"padding-bottom: 40px !important;padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\r\n                        {{var payment_html|raw}}\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"method-info\" style=\"padding-bottom: 40px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\r\n                        {{if shipping_msg}}\r\n                        <p style=\"color: #555656;\">{{var shipping_msg}}</p>\r\n                        {{/if}}\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n           {{depend order_data.email_customer_note}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var order_data.email_customer_note|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n\r\n            {{layout handle=\"weltpixel_sales_email_order_invoice_items\" invoice=$invoice order=$order invoice_id=$invoice_id order_id=$order_id area=\"frontend\"}}\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\" align=\"center\">\r\n            <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n                <tbody style=\"display:block;\">\r\n                    <tr style=\"display: block\">\r\n                        <td style=\"display: block\">\r\n                            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                                <tr>\r\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                                        <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\r\n                                    </td>\r\n                                </tr>\r\n                            </table>\r\n                        </td>\r\n                    </tr>\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n <tr>\r\n        <td style=\"margin-left: 0px\">\r\n            <p style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n            </p>\r\n        </td>\r\n </tr>\r\n<tr>\r\n\r\n <tr>\r\n        <!--<td colspan=\"2\" style=\"padding-top: 35px;padding-left:20px;\">\r\n            {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_custom_block_returns\"}}\r\n        </td>-->\r\n    </tr>\r\n\r\n</table>\r\n</div>\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your Invoice  #%invoice_id of your order #%order_id\" invoice_id=$invoice.increment_id order_id=$order.increment_id}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:41:44", "modified_at": "2024-05-28 05:41:31", "orig_template_code": "new_invoice_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var comment\":\"Invoice Comment\",\"var invoice.increment_id\":\"Invoice Id\",\"layout area=\\\"frontend\\\" handle=\\\"sales_email_order_invoice_items\\\" invoice=$invoice order=$order\":\"Invoice Items Grid\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "176", "template_code": "New Order for Guest - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n\r\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\r\n    <tbody style=\"display: block\">\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px; \">\r\n           <h3 style=\"text-align: center; text-transform: uppercase;\">\r\n                  {{trans 'Thank You For Your Purchase'}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans 'We just received your order!'}}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:8px; \">\r\n            <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                {{trans 'ORDER NUMBER: <span class=\"no-link\">%increment_id</span>' increment_id=$order.increment_id |raw}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px;margin-left:12px;\">\r\n    <tbody>\r\n    <tr>\r\n            <td align=\"left\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n                <h2 style=\"margin: 10px 0 !important;\">\r\n              <!-- {{trans \"Hello, %name\" name=$customer.name}} -->\r\n                     {{trans \"Hello\"}} {{trans \" %customer_name,\" customer_name=$myvar3}}\r\n                </h2>\r\n            </td>\r\n        </tr>\r\n   <tr>\r\n            <td style=\"margin-left: 20px\">\r\n                <p style=\"margin: 10px 0 !important; margin-left:20px;\">\r\n                    {{trans 'You can view the entire status of your order by checking <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n    <tr>\r\n         <td style=\"margin-left: 0px\">\r\n                <p style=\"margin: 10px 0 !important; margin-left:20px;\">\r\n                    {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n                </p>\r\n            </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px;\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n           \r\n\r\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\r\n                <tr>\r\n                    <td class=\"address-details\" style=\"padding-top: 20px !important; padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"address-details\" style=\"padding-top: 20px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"method-info wp-method-info\" style=\"padding-bottom: 20px !important;padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\r\n                        {{var payment_html|raw}}\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"method-info\" style=\"padding-bottom: 20px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\r\n                        {{if shipping_msg}}\r\n                        <p style=\"color: #555656;\">{{var shipping_msg}}</p>\r\n                        {{/if}}\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n          {{depend order_data.email_customer_note}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var order_data.email_customer_note|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n\r\n            {{layout handle=\"weltpixel_sales_email_order_items\" order=$order order_id=$order_id area=\"frontend\"}}\r\n        </td>\r\n    </tr>\r\n   <tr>\r\n        <td colspan=\"2\" align=\"center\">\r\n            <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n                <tbody style=\"display: block\">\r\n                    <tr style=\"display: block\">\r\n                        <td style=\"display: block\">\r\n                            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                                <tr>\r\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                                        <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\r\n                                    </td>\r\n                                </tr>\r\n                            </table>\r\n                        </td>\r\n                    </tr>\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n<tr>\r\n            <td style=\"margin-left: 0px\">\r\n                <p style=\"margin: 10px 0 !important; margin-left:20px;\">\r\n                    {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n    \r\n    <tr>\r\n       <!-- <td colspan=\"2\">\r\n            {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_custom_block_returns\"}}\r\n        </td>-->\r\n    </tr>\r\n</table>\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Thank You for your order #%increment_id\" increment_id=$order.increment_id}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:42:48", "modified_at": "2024-05-28 05:42:21", "orig_template_code": "sales_email_order_guest_template", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var order_data.email_customer_note|escape|nl2br\":\"Email Order Note\",\"var order_data.customer_name\":\"Guest Customer Name\",\"var created_at_formatted\":\"Order Created At (datetime)\",\"var order.increment_id\":\"Order Id\",\"layout handle=\\\"sales_email_order_items\\\" order=$order\":\"Order Items Grid\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\",\"var shipping_msg\":\"Shipping message\",\"var store.frontend_name\":\"Store Frontend Name\",\"var store_phone\":\"Store Phone\",\"var store_email\":\"Store Email\",\"var store_hours\":\"Store Hours\",\"var order_data.is_not_virtual\":\"Order Type\",\"var order_id\": \"Order DB Id\",\"var order\":\"Order\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "179", "template_code": "order update - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\r\n    <tbody style=\"display: block; \">\r\n    <tr style=\"display: block; \">\r\n        <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px; \">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans \"Hi %name,\" name=$order_data.customer_name}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block; \">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block; \">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:8px; \">\r\n            <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                {{trans\r\n                \"ORDER NUMBER: %increment_id \" increment_id=$order.increment_id |raw}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\r\n    <tbody>\r\n    <tr>\r\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\r\n                {{trans 'Your order has been updated!'}}\r\n            </h2>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td style=\"margin-left: 0px\">\r\n            <p style=\"margin: 0 0 50px 0 !important;\">\r\n                {{trans 'You can check the status of your order by logging into <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n            <tbody style=\"display: block\">\r\n                <tr style=\"display: block\">\r\n                    <td style=\"display: block\">\r\n                        <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                            <tr>\r\n                                <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                                    <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\r\n                                </td>\r\n                            </tr>\r\n                        </table>\r\n                    </td>\r\n                </tr>\r\n            </tbody>\r\n        </table>\r\n    </tr>\r\n    <tr>\r\n        <td style=\"margin-left: 0px\">\r\n            <p style=\"margin: 50px 0 50px 0 !important;\">\r\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table style=\"width: 660px\">\r\n\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            {{depend comment}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var comment|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name order\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:43:39", "modified_at": "2023-11-01 09:43:39", "orig_template_code": null, "orig_template_variables": null, "is_legacy": "1", "template_preheader": null}, {"template_id": "182", "template_code": "Order Update for Guest - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\r\n    <tbody style=\"display: block\">\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px; \">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans \"Hi %name,\" name=$billing.name}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px;\">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:8px; \">\r\n            <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                {{trans\r\n                \"ORDER NUMBER: %increment_id \" increment_id=$order.increment_id |raw}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\r\n    <tbody>\r\n    <tr>\r\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\r\n                {{trans 'Your order has been updated.'}}\r\n            </h2>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td style=\"margin-left: 0px\">\r\n            <p style=\"\">\r\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table style=\"width: 660px\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            {{depend comment}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var comment|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name order\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:44:28", "modified_at": "2024-05-28 05:43:35", "orig_template_code": "order_update_guest_weltpixel", "orig_template_variables": "{\"var billing.name\":\"Guest Customer Name\",\"var comment\":\"Order Comment\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "185", "template_code": "New Invoice for Guest - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<p>\r\n          {{trans \"****This is an automatically generated email, please do not reply****\"}}\r\n </p>\r\n\r\n<table align=\"center\" style=\"display: block; text-align:center; width: 660px;\">\r\n   \r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\r\n    <tbody>\r\n\r\n <tr>\r\n<td align=\"left\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n                <h2 style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                     {{trans \"Hello\"}} {{trans \" %customer_name,\" customer_name=$myvar3}}\r\n                </h2>\r\n </td>\r\n\r\n</tr>\r\n\r\n\r\n    <tr>\r\n        <td style=\"margin-left: 20px\">\r\n            <p style=\"margin: 10px 0 !important; padding-left:20px;padding-right:20px;\">\r\n          {{trans 'Thank you for shopping with ComAve. We have successfully placed your order. You will get a confirmation once your item has been shipped. All your order details are mentioned below. '}} </p>\r\n         <p style=\"margin: 10px 0 !important; padding-left:20px;padding-right:20px;\">\r\n {{trans 'We will keep updating the order status to you on your email. Please register on <a href=\"%account_url\">ComAve.com</a>  and earn many perks such as reward points,et notifications if product on sale, create a wish list, get discounts, etc.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n </p>\r\n\r\n\r\n<!--<p style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                {{trans 'You can view the entire status of your order by checking <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>-->\r\n        </td>\r\n    </tr>\r\n   \r\n  <tr style=\"display:block;\">\r\n        <td class=\"dark\" align=\"left\" style=\"display:block; \">\r\n            <h3 style=\"text-align: left; letter-spacing: 0.025em;padding-left:20px;\">\r\n                {{trans \"ORDER NUMBER: %increment_id.\" increment_id=$order.increment_id |raw}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n <tr>\r\n        <td align=\"left\" style=\"\">\r\n            <h2 style=\"text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;\">\r\n                {{trans 'Your Invoice: #%invoice_id' invoice_id=$invoice.increment_id |raw}}\r\n            </h2>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n\r\n<table style=\"width: 660px\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n           \r\n\r\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\r\n                <tr>\r\n                   <td class=\"address-details\" style=\"padding-top: 40px !important; padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"address-details\" style=\"padding-top: 40px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                   <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important;padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\r\n                        {{var payment_html|raw}}\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\r\n                        {{if shipping_msg}}\r\n                        <p style=\"color: #555656;\">{{var shipping_msg}}</p>\r\n                        {{/if}}\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n           {{depend order_data.email_customer_note}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var order_data.email_customer_note|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n\r\n            {{layout handle=\"weltpixel_sales_email_order_invoice_items\" invoice=$invoice order=$order invoice_id=$invoice_id order_id=$order_id area=\"frontend\"}}\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\" align=\"center\">\r\n            <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n                <tbody style=\"display:block;\">\r\n                    <tr style=\"display: block\">\r\n                        <td style=\"display: block\">\r\n                            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                                <tr>\r\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                                        <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\r\n                                    </td>\r\n                                </tr>\r\n                            </table>\r\n                        </td>\r\n                    </tr>\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n <tr>\r\n        <td style=\"margin-left: 0px\">\r\n            <p style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n            </p>\r\n        </td>\r\n </tr>\r\n<tr>\r\n<td style=\"margin-left: 0px\">\r\n            <p style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                {{trans 'Thank you for shopping with us.'}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n<tr>\r\n<td style=\"margin-left: 0px\">\r\n  <p style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                {{trans 'Regards'}}\r\n            </p>\r\n            <p style=\"margin: 10px 0 !important; padding-left:20px;\">\r\n                {{trans 'ComAve'}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n <tr>\r\n        <!--<td colspan=\"2\" style=\"padding-top: 35px;padding-left:20px;\">\r\n            {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_custom_block_returns\"}}\r\n        </td>-->\r\n    </tr>\r\n\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your Invoice  #%invoice_id of your order #%order_id\" invoice_id=$invoice.increment_id order_id=$order.increment_id}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:45:43", "modified_at": "2023-11-01 09:45:43", "orig_template_code": null, "orig_template_variables": null, "is_legacy": "1", "template_preheader": null}, {"template_id": "188", "template_code": "New Shipment - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\n<p>\n    {{trans \"****This is an automatically generated email, please do not reply****\"}}\n</p>\n<div style=\"padding: 0 10px 0 10px;\">\n\n    <table align=\"center\" style=\"display: block;  text-align:center; width: 660px;\">\n        <tbody style=\"display: block\">\n        <tr>\n            <td align=\"left\" style=\"padding-top: 10px;padding-bottom:10px;\">\n                <p class=\"greeting\"  style=\"padding-top:10px ;padding-left:20px;\">{{trans \"Hello %name,\" name=$order_data.customer_name}}</p>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n    <table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n        <tbody>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'Thank you for shopping with us. We would like to confirm that your item has been shipped. Your order and shipping details are mentioned below.'}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{layout handle=\"sales_email_order_shipment_track\" shipment_id=$shipment_id order_id=$order_id}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'To know more about the order, payment and shipping details, please visit <a href=\"%account_url\">My Orders</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n                </p>\n            </td>\n        </tr>\n\n        <tr style=\"display: block\">\n            <td class=\"dark\"  style=\"display: block; \">\n                <h3 style=\"text-align: left; letter-spacing: 0.025em;padding-left:20px;\">\n                    {{trans \"ORDER NUMBER: %order_id\" order_id=$order.increment_id}}\n                </h3>\n            </td>\n        </tr>\n        <tr>\n            <td align=\"center\" style=\"\">\n                <h2 style=\"text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;\">\n                    {{trans 'ComAve shipment id:  #%shipment_id' shipment_id=$shipment.increment_id}}\n                </h2>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n    <table style=\"width: 660px\">\n        <tr class=\"email-information\">\n            <td>\n                <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                    <tr>\n                        <td class=\"address-details\" style=\"padding-top: 30px !important;padding-left:20px;\">\n                            <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\n                            <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\n                        </td>\n                        {{depend order_data.is_not_virtual}}\n                        <td class=\"address-details\" style=\"padding-top: 30px !important\">\n                            <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\n                            <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\n                        </td>\n                        {{/depend}}\n                    </tr>\n                    <tr>\n                        <td class=\"method-info wp-method-info\" style=\"padding-bottom: 40px !important;padding-left:20px;\">\n                            <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\n                            {{var payment_html|raw}}\n                        </td>\n                        {{depend order_data.is_not_virtual}}\n                        <td class=\"method-info\" style=\"padding-bottom: 40px !important\">\n                            <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\n                            <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\n                            {{if shipping_msg}}\n                            <p style=\"color: #555656;\">{{var shipping_msg}}</p>\n                            {{/if}}\n                        </td>\n                        {{/depend}}\n                    </tr>\n                </table>\n                {{depend comment}}\n                <table class=\"message-info\">\n                    <tr>\n                        <td>\n                            {{var comment|escape|nl2br}}\n                        </td>\n                    </tr>\n                </table>\n                {{/depend}}\n\n                {{layout handle=\"weltpixel_sales_email_order_shipment_items\" shipment=$shipment order=$order shipment_id=$shipment_id order_id=$order_id}}\n            </td>\n        </tr>\n        <tr>\n            <td colspan=\"2\" align=\"center\">\n                <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n                    <tbody style=\"display: block\">\n                    <tr style=\"display: block\">\n                        <td style=\"display: block\">\n                            <table class=\"inner-wrapper\"  cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                                <tr>\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                        <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold; border:unset !important;\">{{trans \"VIEW ORDER\"}}</a>\n                                    </td>\n                                </tr>\n                            </table>\n                        </td>\n                    </tr>\n                    </tbody>\n                </table>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"margin: 10px 0 !important; padding-left:20px;\">\n                    {{trans 'If you need further assistance with you order please contact us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n                </p>\n            </td>\n        </tr>\n    </table>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your order #%increment_id has been shipped\" increment_id=$order.increment_id}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:46:36", "modified_at": "2025-01-29 09:54:35", "orig_template_code": "new_shipment_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var comment\":\"Shipment Comment\",\"var shipment.increment_id\":\"Shipment Id\",\"layout handle=\\\"sales_email_order_shipment_items\\\" shipment=$shipment order=$order\":\"Shipment Items Grid\",\"block class='Magento\\\\\\\\Framework\\\\\\\\View\\\\\\\\Element\\\\\\\\Template' area='frontend' template='Magento_Sales::email\\/shipment\\/track.phtml' shipment=$shipment order=$order\":\"Shipment Track Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "191", "template_code": "New Shipment Guest- comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\n<div style=\"padding: 0 10px 0 10px;\">\n    <table align=\"center\" style=\"display: block;  text-align:center; width: 660px;\">\n        <tbody style=\"display: block\">\n        <tr>\n            <td align=\"left\" style=\"padding-top: 10px;padding-bottom:10px;\">\n                <p class=\"greeting\"  style=\"padding-top:20px ;padding-left:20px;\">{{trans \"Hello  %customer_name,\" customer_name=$myvar1}}</p>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n    <table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\n        <tbody>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'Thank You for shopping with us. We would like to confirm that your item has been shipped. Your order details are available on the link below.'}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'Use this number to track your package: TR897451258'}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"padding-left:20px;\">\n                    {{trans 'To know more about the order, payment and shipping details please visit <a href=\"%account_url\">My Order</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\n                </p>\n            </td>\n        </tr>\n\n        <tr style=\"display: block\">\n            <td class=\"dark\"  style=\"display: block; \">\n                <h3 style=\"text-align: left; letter-spacing: 0.025em;padding-left:20px;\">\n                    {{trans \"ORDER NUMBER: %order_id\" order_id=$order.increment_id}}\n                </h3>\n            </td>\n        </tr>\n        <tr>\n            <td align=\"center\" style=\"\">\n                <h2 style=\"text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;\">\n                    {{trans 'Your Shipment #%shipment_id' shipment_id=$shipment.increment_id}}\n                </h2>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n    <table style=\"width: 660px\">\n        <tr class=\"email-information\">\n            <td>\n                <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\n                    <tr>\n                        <td class=\"address-details\" style=\"padding-top: 40px !important;padding-left:20px;\">\n                            <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\n                            <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\n                        </td>\n                        {{depend order_data.is_not_virtual}}\n                        <td class=\"address-details\" style=\"padding-top: 40px !important\">\n                            <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\n                            <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\n                        </td>\n                        {{/depend}}\n                    </tr>\n                    <tr>\n                        <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important;padding-left:20px;\">\n                            <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\n                            {{var payment_html|raw}}\n                        </td>\n                        {{depend order_data.is_not_virtual}}\n                        <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\n                            <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\n                            <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\n                            {{if shipping_msg}}\n                            <p style=\"color: #555656;\">{{var shipping_msg}}</p>\n                            {{/if}}\n                        </td>\n                        {{/depend}}\n                    </tr>\n                </table>\n                {{depend comment}}\n                <table class=\"message-info\">\n                    <tr>\n                        <td>\n                            {{var comment|escape|nl2br}}\n                        </td>\n                    </tr>\n                </table>\n                {{/depend}}\n\n                {{layout handle=\"weltpixel_sales_email_order_shipment_items\" shipment=$shipment order=$order shipment_id=$shipment_id order_id=$order_id}}\n\n                {{layout handle=\"sales_email_order_shipment_track\" shipment_id=$shipment_id order_id=$order_id}}\n\n\n            </td>\n        </tr>\n        <tr>\n            <td colspan=\"2\" align=\"center\">\n                <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n                    <tbody style=\"display: block\">\n                    <tr style=\"display: block\">\n                        <td style=\"display: block\">\n                            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                                <tr>\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\n                                        <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\n                                    </td>\n                                </tr>\n                            </table>\n                        </td>\n                    </tr>\n                    </tbody>\n                </table>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"margin: 10px 0 !important; padding-left:20px;\">\n                    {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\n                </p>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"margin-left: 0px\">\n                <p style=\"margin: 10px 0 !important; padding-left:20px;\">\n                    {{trans '*Please do not reply to this email, as it does not accommodate replies.'}}\n                </p>\n            </td>\n        </tr>\n    </table>\n</div>\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your order #%increment_id has been shipped\" increment_id=$order.increment_id}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:47:22", "modified_at": "2025-01-29 09:54:35", "orig_template_code": "new_shipment_guest_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var billing.name\":\"Guest Customer Name\",\"var order.increment_id\":\"Order Id\",\"var payment_html|raw\":\"Payment Details\",\"var comment\":\"Shipment Comment\",\"var shipment.increment_id\":\"Shipment Id\",\"layout handle=\\\"sales_email_order_shipment_items\\\" shipment=$shipment order=$order\":\"Shipment Items Grid\",\"block class='Magento\\\\\\\\Framework\\\\\\\\View\\\\\\\\Element\\\\\\\\Template' area='frontend' template='Magento_Sales::email\\/shipment\\/track.phtml' shipment=$shipment order=$order\":\"Shipment Track Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "194", "template_code": "Shipment Update - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<table align=\"center\" style=\"display: block; text-align:center; width: 660px\">\r\n    <tbody style=\"display: block\">\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\"  style=\"display:block; padding-bottom:8px; padding-top:5px; \">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans \"Hi %name,\" name=$order_data.customer_name}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\" align=\"center\" style=\"display:block; padding-bottom:0px; \">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\" align=\"center\" style=\"display:block; padding-bottom:8px; \">\r\n            <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                {{trans \"ORDER NUMBER: %increment_id \" increment_id=$order.increment_id |raw}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\r\n    <tbody>\r\n    <tr>\r\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\r\n                {{trans 'Your order has been updated!'}}\r\n            </h2>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td style=\"margin-left: 0px\">\r\n            <p style=\"margin: 0 0 50px 0 !important;padding-left:20px;\">\r\n                {{trans 'You can check the status of your order by logging into <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n            <tbody style=\"display: block\">\r\n                <tr style=\"display: block\">\r\n                    <td style=\"display: block\">\r\n                        <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                            <tr>\r\n                                <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                                    <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\r\n                                </td>\r\n                            </tr>\r\n                        </table>\r\n                    </td>\r\n                </tr>\r\n            </tbody>\r\n        </table>\r\n    </tr>\r\n    <tr>\r\n        <td style=\"margin-left: 0px\">\r\n            <p style=\"margin-top: 35px !important;padding-left:20px;\">\r\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table style=\"width: 660px\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            {{depend comment}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var comment|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name shipment\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:48:29", "modified_at": "2024-05-28 05:49:07", "orig_template_code": "shipment_update_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var order_data.customer_name\":\"Customer Name\",\"var comment\":\"Order Comment\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\",\"var shipment.increment_id\":\"Shipment Id\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "197", "template_code": "Shipment Update Guest - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<table align=\"center\" style=\"display: block; text-align:center; width: 660px\">\r\n    <tbody style=\"display: block\">\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px; \">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans \"Hi %name,\" name=$billing.name}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans \"ORDER %order_status\" order_status=$order_data.frontend_status_label}}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr style=\"display: block\">\r\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:8px; \">\r\n            <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                {{trans \"ORDER NUMBER: %increment_id \" increment_id=$order.increment_id |raw}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px\">\r\n    <tbody>\r\n    <tr>\r\n        <td align=\"center\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n            <h2 style=\"text-align: center; margin: 0 0 20px 0 !important\">\r\n                {{trans 'Your order has been updated!'}}\r\n            </h2>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td style=\"margin-left: 0px\">\r\n            <p style=\"padding-left:20px;\">\r\n                {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>\r\n<table style=\"width: 660px\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            {{depend comment}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var comment|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Update to your %store_name shipment\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 09:50:44", "modified_at": "2023-11-01 09:50:44", "orig_template_code": null, "orig_template_variables": "{\"var billing.name\":\"Guest Customer Name\",\"var comment\":\"Order Comment\",\"var order.increment_id\":\"Order Id\",\"var order_data.frontend_status_label\":\"Order Status\",\"var shipment.increment_id\":\"Shipment Id\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "200", "template_code": "Reset Password- Comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n<p class=\"greeting\" style=\"margin-top: 35px; margin-left:20px;\">{{trans \"Hello %name,\" name=$customer.name}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\"> {{trans \" There was recently a request to change the password for your account.\"}}</p>\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\"> {{trans \"If you requested this change, set a new password here:\"}}</p>\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"50%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important; \">\r\n                        <a href=\"{{var this.getUrl($store,'customer/account/createPassword',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold; border: unset !important;\">{{trans \"Set Password\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n<p style=\"padding-left:20px;\">{{trans \"If you did not make this request, you can ignore this email and your password will remain the same.\"}}</p>\r\n<p  style=\"padding-left:20px;\">{{trans \"If you didn't initiate this request, please contact us <NAME_EMAIL>\"}}</p>\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"Comave\"}}</p>\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your %store_name password has been changed\" store_name=$store.frontend_name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 10:02:41", "modified_at": "2024-05-28 05:50:49", "orig_template_code": "customer_reset_pass_weltpixel", "orig_template_variables": "{\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "203", "template_code": "Forgot Password - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px; display:none !important;\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n                \n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n               \n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n              \n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<div style=\"padding: 0 10px 0 10px;\">\n<p class=\"greeting\" style=\"margin-top: 15px; margin-left:20px;\">{{trans \"Hello %name,\" name=$customer.name}}</p>\n\n<p style=\"padding-left:20px;\"> {{trans \" There was recently a request to change the password on your account.\"}}</p>\n<p style=\"padding-left:20px;\"> {{trans \"If you requested this change, then please select the 'Reset Password' box below:\"}}</p>\n\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n    <tr>\n        <td>\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"50%\">\n                <tr>\n                    <td align=\"center\" style=\"padding: 8px 0 !important; \">\n                        {{if is_seller}}\n                            <a href=\"{{config path='web/secure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a>\n                        {{else}}\n                            <a href=\"{{config path='web/secure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a>\n                            <!-- frontend uses unsecure url to be updated when the FE functionality is done -->\n                            <!--a href=\"{{config path='web/unsecure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a-->\n                        {{/if}}\n                    </td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n\n<p  style=\"padding-left:20px;\">{{trans \"If you didn't initiate this request, please contact us <NAME_EMAIL>\"}}</p>\n\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\n<p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Reset ComAve Account Password.\" }}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 10:04:16", "modified_at": "2025-01-15 22:30:08", "orig_template_code": "customer_password_forgot_email_template", "orig_template_variables": "{\"var store.frontend_name\":\"Store Name\",\"var customer.name\":\"Customer Name\",\"var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])\":\"Reset Password URL\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "206", "template_code": "Subscription Success backup - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<!-- <p>{{trans \"You have been successfully subscribed to our newsletter.\"}}</p> -->\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n\r\n<p class=\"greeting\"  style=\"padding-top:20px ;padding-left:20px;\">{{trans \"Hello %name,\" name=$customer.name}}</p></p>\r\n\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Thanks for subscribing to our newsletter, of ComAve! You’ve been added to our mailing list and will now be among the first to hear about new arrivals, special offers and discounts etc.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"As a thank you gift from us, we will credit you with an additional \"}} <span style= \"color:#d91f26 !important;\"> ”5% of LIX Rewards upon completion of your next order.\" </span> </p>\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"25%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                        <a href=\"{{var this.getUrl($store,'/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold; border: unset !important;\">{{trans \"VISIT STORE\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"If you have any queries, please contact <NAME_EMAIL>\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Thank you for shopping with us. \"}}</p>\r\n\r\n<!-- <p style= \"margin-left:20px;\">{{trans '<a href=\"%unsubscription_url\">Unsubscribe</a> from these notifications.' unsubscription_url=$unsubscription_url |raw}}</p> -->\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n</div>\r\n\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Thank you for joining ComAve’s Mailing list\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 10:15:14", "modified_at": "2024-05-28 05:52:42", "orig_template_code": "newsletter_subscription_success_email_template", "orig_template_variables": "{\"template config_path=\\\"design\\/email\\/footer_template\\\"\":\"Email Footer Template\",\"template config_path=\\\"design\\/email\\/header_template\\\"\":\"Email Header Template\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "209", "template_code": "Order Placed Notification - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n\r\n<table style=\"width:96%; margin:auto;margin-top:10px;\">\r\n    <tr class=\"email-intro\">\r\n        <td>\r\n            <p class=\"greeting\" >{{trans \"Hello \"}}{{trans \"%customer_name,\" customer_name=$myvar3}}</p>\r\n            <p>\r\n                {{trans \"We are pleased to inform you that a customer has recently placed an order for your products on ComAve.\"}}\r\n                {{trans 'You can check your order by <a href=\"%account_url\">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-summary\">\r\n        <td>\r\n            <h1>{{trans 'Your Order <span class=\"no-link\">#%increment_id</span>' increment_id=$myvar1 |raw}}</h1>\r\n            <p>{{trans 'Placed on <span class=\"no-link\">%created_at</span>' created_at=$myvar2 |raw}}</p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            <table class=\"order-details\">\r\n                <tr>\r\n                    <td class=\"address-details\">\r\n                        <h3>{{trans \"Billing Info\"}}</h3>\r\n                        <p>{{var myvar4|raw}}</p>\r\n                    </td>\r\n                    {{depend isNotVirtual}}\r\n                    <td class=\"address-details\">\r\n                        <h3>{{trans \"Shipping Info\"}}</h3>\r\n                        <p>{{var myvar6|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Payment Method\"}}</h3>\r\n                        {{var myvar5|raw}}\r\n                    </td>\r\n                    {{depend isNotVirtual}}\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Shipping Method\"}}</h3>\r\n                        <p>{{var myvar9|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n            <table class=\"email-items\">\r\n                <thead>\r\n                    <tr>\r\n                        <th class=\"item-info\">{{trans \"Item\"}}</th>\r\n                        <th class=\"item-info\">{{trans \"Sku\"}}</th>\r\n                        <th class=\"item-qty\">{{trans \"Qty\"}}</th>\r\n                        <th class=\"item-price\">{{trans \"Subtotal\"}}</th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    {{var myvar8|raw}}\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n <p style=\"padding-left:20px;\">{{trans \"Please ensure that you process and fulfill this order promptly. Timely and accurate fulfillment contributes to a positive customer experience, so we appreciate your attention to this matter. \"}}</p>\r\n\r\n <p style=\"padding-left:20px;\">{{trans \"If you encounter any issues or have questions regarding the order, please do not hesitate to reach <NAME_EMAIL> .\"}}</p>\r\n\r\n\r\n <p style=\"padding-left:20px;\">{{trans \"We value your partnership and appreciate your commitment to delivering high-quality products and exceptional service to our customers.\"}}</p>\r\n\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Confirmation of Your Recent Order on ComAve. \"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-01 10:17:34", "modified_at": "2024-05-28 05:53:29", "orig_template_code": "marketplace_email_order_placed_notification_template", "orig_template_variables": "{\"var this.getUrl($store,'marketplace/order/history/',[_nosid:1]) |raw\":\"Order History URL\",\"var isNotVirtual\":\"isNotVirtual\",\"var myvar1 |raw\":\"Order Id\",\"var myvar2 |raw\":\"Order Placed on\",\"var myvar3 |raw\":\"Customer Name\",\"var myvar4|raw\":\"Billing Info\",\"var myvar5|raw\":\"Payment Method\",\"var myvar6|raw\":\"Shipping Info\",\"var myvar8|raw\":\"Product Info\",\"var myvar9|raw\":\"Shipping Method\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "211", "template_code": "Wishlist Share", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n{{layout handle=\"wishlist_sharing_action\" area=\"frontend\"}}\r\n<!--<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px\">\r\n    <tbody>\r\n    <tr>\r\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\r\n            <h3 style=\"text-align: center;\">\r\n                {{trans \"Hi there! %customer_name, has shared their wishlist with you. To view product details you can click on the products listed below.\" customer_name=$customerName}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\r\n            <h1 style=\"text-align: center; margin: 0 !important\">\r\n                {{trans 'Wish List'}}\r\n            </h1>\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\r\n            <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                {{trans 'with somebody special like you!'}}\r\n            </h3>\r\n        </td>\r\n    </tr>\r\n    </tbody>\r\n</table>-->\r\n\r\n<p style=\"margin: 20px 20px !important;\">\r\n {{trans \"Hi there! %customer_name, has shared their wishlist with you. To view product details you can click on the products listed below.\" customer_name=$customerName}}\r\n</p>\r\n         \r\n<!--<p style=\"margin: 25px 0 !important\">{{trans \"To begin see what %customer_name found and wants to share with you click on the button below :)\" customer_name=$customerName }}</p>-->\r\n\r\n<!--<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                        <a href=\"{{var viewOnSiteLink}}\" style=\"font-weight: bold\">{{trans \"View all items\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>-->\r\n\r\n{{depend message}}\r\n<table class=\"message-info\">\r\n    <tr>\r\n        <td>\r\n            <h3>{{trans \"Message from %customer_name:\" customer_name=$customerName}}</h3>\r\n            {{var message|raw}}\r\n        </td>\r\n    </tr>\r\n</table>\r\n<br />\r\n{{/depend}}\r\n\r\n<table style=\"text-align:center; width: 660px; margin-left:20px;\">\r\n<td>\r\n<tr style=\"text-align:center; width: 660px; margin-left:20px;\">\r\n{{var items|raw}}\r\n</tr>\r\n</td>\r\n</table>\r\n<br/>\r\n\r\n<p  style=\"padding-left:28px; padding-top: 10px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:28px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"%customer_name has a ComAve wishlist to share.\" customer_name=$customerName}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-09 11:45:02", "modified_at": "2023-11-13 10:15:09", "orig_template_code": "wishlist_share_notification_weltpixel", "orig_template_variables": "{\"var customerName\":\"Customer Name\",\"var viewOnSiteLink\":\"View Wish List URL\",\"var items|raw\":\"Wish List Items\",\"var message|raw\":\"Wish List Message\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "214", "template_code": "Transaction Notification to the Seller - Comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<div style=\"padding: 10px 10px 0 10px;\">\r\n<table>\r\n    <tr class=\"email-intro\">\r\n        <td>\r\n            <p class=\"greeting\">{{trans \"Hello %customer_name,\" customer_name=$myvar1}}</p>\r\n            <p>\r\n                {{trans \"We are pleased to inform you that the payment for your order(s) on ComAve has been successfully processed.\"}}\r\n                {{trans 'You can check your transaction by <a href=\"%account_url\">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-summary\">\r\n        <td>\r\n            <h1>{{trans 'Transaction Id <span class=\"no-link\">#%increment_id</span>' increment_id=$myvar2 |raw}}</h1>\r\n            <p>{{trans 'Placed on <span class=\"no-link\">%created_at</span>' created_at=$myvar3 |raw}}</p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            <table class=\"order-details\">\r\n                <tr>\r\n                    <td class=\"address-details\">\r\n                        <h3>{{trans \"Transaction Id\"}}</h3>\r\n                        <p>{{var myvar2|raw}}</p>\r\n                    </td>\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Transaction Amount\"}}</h3>\r\n                        {{var myvar4|raw}}\r\n                    </td>\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Comment Message\"}}</h3>\r\n                        {{var myvar6|raw}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            <table class=\"email-items\">\r\n                <thead>\r\n                    <tr>\r\n                        <th class=\"item-info\">Order Id #</th>\r\n                        <th class=\"item-info\">Product Name</th>\r\n                        <th class=\"item-qty\">Qty</th>\r\n                        <th class=\"item-price\">Price</th>\r\n                        <th class=\"item-price\">Commission</th>\r\n                        <th class=\"item-price\">Total payout amount</th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    {{var myvar5|raw}}\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n <p>{{trans \"Thank you for being a valued seller on ComAve. We look forward to continued collaboration and success.\"}}</p>\r\n <p>{{trans \"If you have any questions or concerns regarding the payment or the order itself, please feel free to reach <NAME_EMAIL> \"}}</p>\r\n\r\n\r\n<p>{{trans \"Regards,\"}}</p>\r\n<p  style=\"color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "Payment Confirmation for Your Order on ComAve.", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-21 10:15:43", "modified_at": "2023-11-21 10:45:10", "orig_template_code": "marketplace_email_seller_transaction_notification_template", "orig_template_variables": "{\"var this.getUrl($store,'marketplace/transaction/history/',[_nosid:1]) |raw\":\"transaction history URL\",\"var customer.name\":\"Customer Name\",\"var myvar1\":\"Customer Name\",\"var myvar2|raw\":\"Transaction Id\",\"var myvar3 |raw\":\"Transaction Placed on\",\"var myvar4|raw\":\"Transaction Amount\",\"var myvar5|raw\":\"Transaction Products\",\"var myvar6|raw\":\"Comment Message\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "217", "template_code": "Notify seller when product is low stock - Comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<div style=\"padding: 10px 10px 0 10px;\">\r\n<table>\r\n    <tr class=\"email-intro\">\r\n        <td>\r\n            <p class=\"greeting\">{{trans \"Hello %customer_name,\" customer_name=$myvar2}}</p>\r\n            <p>\r\n                {{trans \"We hope your products are receiving positive attention on ComAve.\"}}\r\n                {{trans 'You can increase the product stock by <a href=\"%account_url\">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>\r\n            <p>\r\n                {{trans \"We wanted to bring to your attention that the stock for one or more of your products is running low. This notification is to ensure that you are aware of the current inventory status and can take any necessary actions to prevent any disruption in fulfilling customer orders.\"}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-summary\">\r\n        <td>\r\n            <h2>{{trans \"Product(s) with Low Stock:\"}}</h2>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            <table class=\"email-items\">\r\n                <thead>\r\n                    <tr>\r\n                        <th class=\"item-info\">{{trans \"Product Name\"}}</th>\r\n                        <th class=\"item-info\">{{trans \"Sku\"}}</th>\r\n                        <th class=\"item-qty\">{{trans \"Stock Quantity\"}}</th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    {{var myvar1|raw}}\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n <p>{{trans \"Please review your inventory and take steps to replenish stock as soon as possible. Thank you for your prompt attention to this matter.\"}}</p>\r\n\r\n\r\n<p>{{trans \"Regards,\"}}</p>\r\n<p  style=\"color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "Urgent: Low Stock Alert for Your Product on ComAve.", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-21 10:41:03", "modified_at": "2023-11-21 10:45:44", "orig_template_code": "marketplace_email_low_stock_template", "orig_template_variables": "{\"var this.getUrl($store,'marketplace/product/productlist/',[_nosid:1]) |raw\":\"Customer Account URL\",\"var customer.name\":\"Customer Name\",\"var myvar1|raw\":\"Product Info\",\"var myvar2\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "220", "template_code": "Ushop - Comave", "template_text": "<!--@subject {{trans \"Welcome to ComAve! Now Track your Vat Refund status here.\"}} @-->\r\n<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\r\n<head>\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta name=\"viewport\" content=\"initial-scale=1.0, width=device-width\" />\r\n    <meta name=\"x-apple-disable-message-reformatting\" />\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\r\n    <style type=\"text/css\">\r\n        {{var template_styles|raw}}\r\n\r\n        {{css file=\"css/email.css\"}}\r\n    </style>\r\n</head>\r\n<body style=\"padding:0 !important;\">\r\n{{inlinecss file=\"css/email-inline.css\"}}\r\n\r\n<!-- Begin wrapper table -->\r\n<table class=\"wrapper\" width=\"100%\">\r\n    <tr>\r\n        <td class=\"wrapper-inner\" align=\"center\">\r\n            <table class=\"main\" align=\"center\">\r\n                <tr>\r\n               \r\n                    <td class=\"header\" align=\"center\"  >\r\n\t\t\t\t\t\t<span style= \"margin-left:25px !important; border-right: 20px !important;padding-right: 5px;\">\r\n                        {{layout handle=\"light_logo\" area=\"frontend\"}}\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t<span style= \"margin-left: 5px !important;display: inline-block;border-left: 5px solid #ccc; margin: 0 54px;height: 62px; padding-left: 5px;\">\r\n                       {{layout handle=\"dark_logo\" area=\"frontend\"}} \r\n\t\t\t\t\t\t</span>\r\n                    </td>\r\n\r\n                </tr>\r\n               \r\n    <tr>\r\n            <td class=\"main-content\" style=\"padding:0 !important\">\r\n                        <!-- Begin Content -->\r\n<div style=\"padding:10px 10px 0 10px;\">\r\n<p>{{trans \"Dear Mrs/Mr %customer_name,\" customer_name=$customer_name}}</p>\r\n<p>{{trans \"TRACK YOUR VAT REFUND FORMS\"}}<a href=\"{{store url=''}}\" target=\"_blank\" style=\"font-weight: bold; color: #0714b7 !important;\"> {{trans \"HERE!\"}}</a></p>\r\n\r\n\r\n<p> {{trans \" Thank you for using Ushop Tax Free service to reclaim your VAT and don’t forget to get a stamp from the Customs by leaving the country and send it in envelope which you got from the shopping staff to our processing team which will process your refund.\"}}</p>\r\n\r\n<p> {{trans \" Please register yourself at ComAve to track your Ushop VAT Refund Form status.\"}}</p>\r\n\r\n<p>{{trans \" ComAve and \"}} <a href=\"https://www.ushoptaxfree.com\" target=\"_blank\" style=\"font-weight: bold; color: #d91f26 !important;\"> {{trans \" Ushop Tax-Free (USTF) \"}} </a>{{trans \" partnership enables travelers to easily track their VAT refunds on their eligible purchases, ensuring they can make the most of their shopping experience while abroad.\"}}</p>\r\n\r\n   \r\n\r\n<p> {{trans \"You can now access ComAve platform to track your VAT Refund status using the credentials below.\"}}</p>\r\n\r\n<p> {{trans \"Visit to ComAve website \"}}<a href=\"{{store url=''}}\" target=\"_blank\" style=\"font-weight: bold;\">{{trans \"www.comave.com\"}}</a></p>\r\n\r\n<p>{{trans \"We look forward to assisting you in tracking the VAT Refund on your purchases.\"}}</p>\r\n\r\n<p>{{trans \"Thank you.\"}}</p>\r\n\r\n<p>{{trans \"Regards,\"}}</p>\r\n<p  style=\"color: #d91f26 !important; font-weight: bold !important;\">{{trans \"ComAve.\"}}</p>\r\n</div>\r\n{{template config_path=\"design/email/footer_template\"}} ", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Welcome to ComAve! Now Track your Vat Refund status here.\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2023-11-23 13:23:20", "modified_at": "2023-11-23 14:27:07", "orig_template_code": "ushop_reset_template", "orig_template_variables": null, "is_legacy": "1", "template_preheader": null}, {"template_id": "233", "template_code": "Welcome - ComAve food", "template_text": "{{template config_path=\"design/email/header_template\"}}\n\n\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\n    <tbody style=\"display: block;\">\n    <tr style=\"display: block;\">\n        <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px;\">\n            <h3 style=\"text-align: center;\">\n                {{trans \"Hello %name,\" name=$customer.name}}\n            </h3>\n        </td>\n    </tr>\n    <tr style=\"display: block;\">\n        <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n                {{trans 'Welcome To ComAve! ' }}\n            </h1>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<div style=\"padding:0 10px 0 10px;\">\n\n    <p style=\"margin: 20px 0 !important;padding-left:22px;\">\n        {{trans 'We are thrilled to have you join our community. At ComAve, you can participate in exciting tournaments, play multiple games, and earn amazing rewards.\n        To get started, simply log in to your account and explore the endless possibilities.'}}\n    </p>\n    <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n        <tbody style=\"display: block\">\n        <tr style=\"display: block\">\n            <td style=\"display: block\">\n                <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n                    <tr>\n                        <td align=\"center\" style=\"padding: 8px 0 !important\">\n                            <a href=\"{{var baseUrl}}login/\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"LOGIN\"}}</a>\n                        </td>\n                    </tr>\n                </table>\n            </td>\n        </tr>\n        </tbody>\n    </table>\n\n    <p  style=\"padding-left:20px;padding-top: 10px;\">{{trans \"If you have any questions or need assistance, our support team is here to help. Feel free to reach out to <NAME_EMAIL> \"}}</p>\n\n\n    <p  style=\"padding-left:20px; padding-top: 10px;\">{{trans \"Regards,\"}}</p>\n    <p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"The ComAve Team\"}}</p>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans 'Welcome to ComAve Foods! '}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-26 10:50:18", "modified_at": "2025-05-14 08:02:40", "orig_template_code": "customer_new_account_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var customer.email\":\"Customer Email\",\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "236", "template_code": "Order Placed Notification - comave food", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n\r\n<table style=\"width:96%; margin:auto;margin-top:10px;\">\r\n    <tr class=\"email-intro\">\r\n        <td>\r\n            <p class=\"greeting\" >{{trans \"Hello \"}}{{trans \"%customer_name,\" customer_name=$myvar3}}</p>\r\n            <p>\r\n                {{trans \"We are pleased to inform you that a customer has recently placed an order for your products on ComAve.\"}}\r\n                {{trans 'You can check your order by <a href=\"%account_url\">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n            </p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-summary\">\r\n        <td>\r\n            <h1>{{trans 'Your Order <span class=\"no-link\">#%increment_id</span>' increment_id=$myvar1 |raw}}</h1>\r\n            <p>{{trans 'Placed on <span class=\"no-link\">%created_at</span>' created_at=$myvar2 |raw}}</p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            <table class=\"order-details\">\r\n                <tr>\r\n                    <td class=\"address-details\">\r\n                        <h3>{{trans \"Billing Info\"}}</h3>\r\n                        <p>{{var myvar4|raw}}</p>\r\n                    </td>\r\n                    {{depend isNotVirtual}}\r\n                    <td class=\"address-details\">\r\n                        <h3>{{trans \"Shipping Info\"}}</h3>\r\n                        <p>{{var myvar6|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Payment Method\"}}</h3>\r\n                        {{var myvar5|raw}}\r\n                    </td>\r\n                    {{depend isNotVirtual}}\r\n                    <td class=\"method-info\">\r\n                        <h3>{{trans \"Shipping Method\"}}</h3>\r\n                        <p>{{var myvar9|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n            <table class=\"email-items\">\r\n                <thead>\r\n                    <tr>\r\n                        <th class=\"item-info\">{{trans \"Item\"}}</th>\r\n                        <th class=\"item-info\">{{trans \"Sku\"}}</th>\r\n                        <th class=\"item-qty\">{{trans \"Qty\"}}</th>\r\n                        <th class=\"item-price\">{{trans \"Subtotal\"}}</th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    {{var myvar8|raw}}\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n <p style=\"padding-left:20px;\">{{trans \"Please ensure that you process and fulfill this order promptly. Timely and accurate fulfillment contributes to a positive customer experience, so we appreciate your attention to this matter. \"}}</p>\r\n\r\n <p style=\"padding-left:20px;\">{{trans \"If you encounter any issues or have questions regarding the order, please do not hesitate to reach <NAME_EMAIL> .\"}}</p>\r\n\r\n\r\n <p style=\"padding-left:20px;\">{{trans \"We value your partnership and appreciate your commitment to delivering high-quality products and exceptional service to our customers.\"}}</p>\r\n\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"You Have A New Order order #%increment_id Placed On ComAve!!\" increment_id=$order.increment_id}}}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-26 10:55:50", "modified_at": "2024-04-26 10:55:50", "orig_template_code": "marketplace_email_order_placed_notification_template", "orig_template_variables": "{\"var this.getUrl($store,'marketplace/order/history/',[_nosid:1]) |raw\":\"Order History URL\",\"var isNotVirtual\":\"isNotVirtual\",\"var myvar1 |raw\":\"Order Id\",\"var myvar2 |raw\":\"Order Placed on\",\"var myvar3 |raw\":\"Customer Name\",\"var myvar4|raw\":\"Billing Info\",\"var myvar5|raw\":\"Payment Method\",\"var myvar6|raw\":\"Shipping Info\",\"var myvar8|raw\":\"Product Info\",\"var myvar9|raw\":\"Shipping Method\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "239", "template_code": "Reset ComAve Foods Account Password. ", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<p style=\"margin-top: 35px; margin-left:20px;\">{{trans \"***Please do not reply to this email, as it does not accommodate replies.***\"}}</p>\r\n\r\n<p class=\"greeting\" style=\"margin-top: 35px; margin-left:20px;\">{{trans \"Hello, %name\" name=$customer.name}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\"> {{trans \" There was recently a request to change the password for your account.\"}}</p>\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\"> {{trans \"If you requested this change, set a new password here:\"}}</p>\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"50%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important; \">\r\n                        <a href=\"{{var this.getUrl($store,'customer/account/createPassword',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n<p style=\"padding-left:20px;\">{{trans \"If you did not make this request, you can ignore this email and your password will remain the same.\"}}</p>\r\n<p  style=\"padding-left:20px;\">{{trans \"If you didn't initiate this request, please contact us <NAME_EMAIL>\"}}</p>\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold;\">{{trans \"ComAve Foods\"}}</p>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Reset ComAve Foods Account Password.\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-26 11:05:11", "modified_at": "2024-04-30 09:54:00", "orig_template_code": "customer_reset_pass_weltpixel", "orig_template_variables": "{\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "242", "template_code": "Newsletter Subscription - comave foods", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n \r\n<p class=\"greeting\"  style=\"padding-top:10px ;padding-left:20px;\">{{trans 'Hello %name,' name=$customer.name}}</p>\r\n\r\n<p style=\"margin: 10px !important;text-align: center; color:#d91f26 !important; font-weight: bold; font-size: 20px !important;\" >{{trans 'Thank You For Signing Up'}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Thank you for signing up to ComAve Foods newsletter. We can’t wait to tell you about new restaurants, new dishes, exciting offers, and discounts.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans 'As a thank you gift, please enjoy your next meal with 10% OFF your order. '}}</p>\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"25%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                        <a href=\"{{var this.getUrl($store,'/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VISIT STORE\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"If you have any questions, please contact us at <EMAIL>\"}}</p>\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;color:#d91f26 !important;;\">{{trans \"ComAve Food.\"}}</p>\r\n\r\n\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Thank You For Signing Up with ComAve Foods.\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-26 11:18:14", "modified_at": "2024-04-30 09:49:00", "orig_template_code": "newsletter_subscription_confirm_weltpixel", "orig_template_variables": "{\"var subscriber_data.confirmation_link\":\"Subscriber Confirmation URL\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "245", "template_code": "New Order-comave foods", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<p>\r\n          {{trans \"***Please do not reply to this email, as it does not accommodate replies***\"}}\r\n </p>\r\n\r\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\r\n    <tbody style=\"display: block\">\r\n        <tr style=\"display: block\">\r\n            <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px; \">\r\n                <h3 style=\"text-align: center; text-transform: uppercase;\">\r\n                    {{trans 'Thank You for ordering'}}{{trans \"%name\" name=$customer.name}}\r\n                </h3>\r\n                 <p>{{trans 'We hope you enjoyed your meal from Food Comave, Please help us serve you better by providing the ratings.'}}</p>\r\n            </td>\r\n        </tr>\r\n        <tr style=\"display: block\">\r\n            <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\r\n                <h1 style=\"text-align: center; margin: 0 !important\">\r\n                    {{trans 'We just received your order!'}}\r\n                </h1>\r\n            </td>\r\n        </tr>\r\n        <tr style=\"display: block\">\r\n            <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:8px; \">\r\n                <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                    {{trans 'ORDER NUMBER: <span class=\"no-link\">%increment_id</span>' increment_id=$order.increment_id |raw}}\r\n                  </h3>\r\n                <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                   {{trans 'delivery date: '}}{{var deliveryDate}}\r\n              </h3>\r\n             <h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                 {{trans 'delivery time: '}}{{var deliveryTime}}\r\n</h3>\r\n<h3 style=\"text-align: center; letter-spacing: 0.025em;\">\r\n                   {{trans 'delivery comment: '}}{{var deliveryComment}}\r\n</h3>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px;margin-left:12px;\">\r\n    <tbody>\r\n        <tr>\r\n            <td align=\"left\" style=\"padding-top: 10px;padding-bottom:10px;\">\r\n                <p class=\"greeting\">{{trans 'Hello %name,' name=$order.getCustomerName()}}</p>           \r\n           </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"margin-left: 20px\">\r\n            <p style=\"margin: 10px 0 !important; margin-left:20px;\">\r\n {{trans 'We have received order <span class=\"no-link\">%increment_id</span>' increment_id=$order.increment_id |raw}}\r\n                    {{trans '. Thank You for ordering from ComAve Food! Looking forward to serving you again. Enjoy your meal!'}}\r\n               </p>\r\n              \r\n                <p style=\"margin: 10px 0 !important; margin-left:20px;\">\r\n                    {{trans 'You can view the entire status of your order by checking <a href=\"%account_url\">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n        \r\n    </tbody>\r\n</table>\r\n<table align=\"center\" style=\"padding-bottom:5px; padding-top:20px; width: 660px;\">\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            \r\n\r\n            <table class=\"order-details\" style=\"border-top: 5px solid #000000\">\r\n                <tr>\r\n                    <td class=\"address-details\" style=\"padding-top: 60px !important; padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"BILLING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedBillingAddress|raw}}</p>\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"address-details\" style=\"padding-top: 60px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING ADDRESS\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var formattedShippingAddress|raw}}</p>\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n                <tr>\r\n                    <td class=\"method-info wp-method-info\" style=\"padding-bottom: 60px !important;padding-left:20px;\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"PAYMENT METHOD\"}}</h3>\r\n                        {{var payment_html|raw}}\r\n                    </td>\r\n                    {{depend order_data.is_not_virtual}}\r\n                    <td class=\"method-info\" style=\"padding-bottom: 60px !important\">\r\n                        <h3 style=\"color: #555656;\">{{trans \"SHIPPING METHOD\"}}</h3>\r\n                        <p style=\"color: #555656;\">{{var order.shipping_description}}</p>\r\n                        {{if shipping_msg}}\r\n                        <p style=\"color: #555656;\">{{var shipping_msg}}</p>\r\n                        {{/if}}\r\n                    </td>\r\n                    {{/depend}}\r\n                </tr>\r\n            </table>\r\n          {{depend order_data.email_customer_note}}\r\n            <table class=\"message-info\">\r\n                <tr>\r\n                    <td>\r\n                        {{var order_data.email_customer_note|escape|nl2br}}\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            {{/depend}}\r\n\r\n            {{layout handle=\"weltpixel_sales_email_order_items\" order=$order order_id=$order_id area=\"frontend\"}}\r\n\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\" align=\"center\">\r\n            <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n                <tbody style=\"display: block\">\r\n                    <tr style=\"display: block\">\r\n                        <td style=\"display: block\">\r\n                            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\r\n                                <tr>\r\n                                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                                        <a href=\"{{var this.getUrl($store,'customer/account/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VIEW ORDER\"}}</a>\r\n                                    </td>\r\n                                </tr>\r\n                            </table>\r\n                        </td>\r\n                    </tr>\r\n                </tbody>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n<tr>\r\n            <td style=\"margin-left: 0px\">\r\n                  <p style=\"margin: 10px 0 !important; margin-left:20px;\">{{trans 'ComAve Food or Delivery representatives will NEVER ask you for your personal information i.e., bank account details, password, OTP, PIN, etc. For your own safety DO NOT share these details with anyone.'}}</p>\r\n\r\n                <p style=\"margin: 10px 0 !important; margin-left:20px;\">\r\n                    {{trans 'If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a>' store_email=$store_email |raw}}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n   <tr>\r\n    <td style=\"margin-left: 0px\">\r\n           <!-- <p style=\"margin: 10px 0 !important;\">\r\n                {{trans 'Regards,'}}\r\n                {{trans 'ComAve Foods.'}}\r\n            </p>-->\r\n<p  style=\"padding-top: 10px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve Team.\"}}</p>\r\n\r\n        </td>\r\n    </tr>\r\n    <tr>\r\n     <!-- <td colspan=\"2\" style=\"padding-top: 35px;padding-left:20px;\">\r\n            {{block class=\"Magento\\Cms\\Block\\Block\" area=\"frontend\" block_id=\"weltpixel_custom_block_returns\"}}\r\n        </td>-->\r\n    </tr>\r\n</table>\r\n\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Your %store_name Order from\" store_name=$store.frontend_name \"is Delivered.\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-26 11:24:05", "modified_at": "2024-04-30 09:48:03", "orig_template_code": "new_order_weltpixel", "orig_template_variables": "{\"var formattedBillingAddress|raw\":\"Billing Address\",\"var order_data.email_customer_note\":\"Email Order Note\",\"var created_at_formatted\":\"Order Created At (datetime)\",\"var order.increment_id\":\"Order Id\",\"layout handle=\\\"sales_email_order_items\\\" order=$order\":\"Order Items Grid\",\"var payment_html|raw\":\"Payment Details\",\"var formattedShippingAddress|raw\":\"Shipping Address\",\"var order.shipping_description\":\"Shipping Description\",\"var shipping_msg\":\"Shipping message\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "248", "template_code": "<PERSON><PERSON> - comave food", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n<p class=\"greeting\" style=\"margin-top: 35px; margin-left:20px;\">{{trans \"Hello %name,\" name=$customer.name}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"We are thrilled to inform you that your application to become a seller on ComAve has been successfully approved!\" }}</p>\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\"> {{trans \" Welcome to the ComAve E-Commerce Website!\"}}</p>\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"We believe that your offerings will be a valuable addition to our platform and that together, we can create a thriving and mutually beneficial partnership. \"}}</p>\r\n\r\n \r\n  <!-- <p style=\"padding-left:20px;\">{{trans \"Please login to your e-store on the ComAve website to manage and enjoy great sales.\"}}</p>.-->\r\n\r\n<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"30%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important; \">\r\n                        <a href=\"{{var this.getUrl($store,'customer/account/login/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold; border: unset !important;\">{{trans \"Sign In\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n\r\n <p style=\"padding-left:20px;\">{{trans \"Thank you for choosing ComAve. Please login to your e-store on the ComAve website to manage and enjoy great sales.\"}}</p>\r\n\r\n <p style=\"padding-left:20px;\">{{trans \"If you encounter any issues or have questions regarding the order, please do not hesitate to reach <NAME_EMAIL>.\"}}</p>\r\n\r\n\r\n <p style=\"padding-left:20px;\">{{trans \"We value your partnership and appreciate your commitment to delivering high-quality products and exceptional service to our customers.\"}}</p>\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n</div>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "Welcome to ComAve - Your Seller Application has been Approved!", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-26 11:29:22", "modified_at": "2024-04-26 11:29:22", "orig_template_code": "marketplace_email_becomeseller_request_notification_template", "orig_template_variables": "{\"var myvar1\":\"Seller Name\",\"var myvar2\":\"Login Url\",\"var myvar3\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "251", "template_code": "<PERSON><PERSON> Disapproved Notifiction - comave foods", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n<div style=\"padding: 0 10px 0 10px;\">\r\n<table>\r\n    <tr class=\"email-intro\">\r\n        <td style=\"padding-bottom: 0px !important;\">\r\n            <p class=\"greeting\" style=\"padding-left:20px; margin-top: 8px;\">{{trans \"Hello %customer_name,\" customer_name=$myvar1}}</p>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-summary\">\r\n        <td>\r\n        </td>\r\n    </tr>\r\n    <tr class=\"email-information\">\r\n        <td>\r\n            <table class=\"query-details\">\r\n                <tr>\r\n                    <td class=\"product-details\" style=\"padding-left:20px;\">\r\n                        <p>{{trans \"Thank you for registering with ComAve, but after careful consideration, we regret to inform you that we are unable to approve you as a seller on ComAve. This decision is based on a thorough review of various factors, including our current product offerings, market dynamics, and our strategic direction. We appreciate the time and effort you invested in your application. \"}}</p>\r\n  <p>{{trans \"We appreciate your understanding in this matter and wish you the best in your future endeavors.  \"}}</p>\r\n  <p>{{trans \"Thank you once again for considering ComAve as a platform for your products.  \"}}</p>\r\n\r\n <p>{{trans \"If you have any questions or would like specific feedback on your application, please feel free to reach <NAME_EMAIL> .\"}}</p>\r\n\r\n<p>{{trans \"Regards,\"}}</p>\r\n<p  style=\"color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\r\n                     \r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>\r\n</div>\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "Sorry - Your Seller Application has been Disapproved!", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-26 11:33:57", "modified_at": "2024-04-26 11:33:57", "orig_template_code": "marketplace_email_seller_disapprove_notification_template", "orig_template_variables": "{\"var myvar1|raw\":\"Customer Name\",\"var myvar2|raw\":\"Login Url\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "254", "template_code": "Reward Points", "template_text": "{{template config_path=\"design/email/header_template\"}}\r\n\r\n<p class=\"greeting\" style= \"margin-left:10px;\">{{trans \"%name,\" name=$customer_name}}</p>\r\n<p style= \"margin-left:10px;\">\r\n    {{trans \"You have %points_balance points that may be used in our store:\" points_balance=$points_balance}}\r\n    <a href=\"{{store url=\"\"}}\">{{var store.frontend_name}}</a>.\r\n</p>\r\n<p style= \"margin-left:10px;\">{{trans '<a href=\"%unsubscription_url\">Unsubscribe</a> from these notifications.' unsubscription_url=$unsubscription_url |raw}}</p>\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Congratulations! %name You've earned Rewards.\" name=$customer.name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-26 11:45:10", "modified_at": "2024-04-26 11:45:10", "orig_template_code": "lix_reward_balance", "orig_template_variables": "{\"var myvar1\":\"Customer Name\",\"var myvar2\":\"Login Url\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "257", "template_code": "Subscription Success - comave", "template_text": "{{var customer.name}}{{template config_path=\"design/email/header_template\"}}\r\n\r\n<!-- <p>{{trans \"You have been successfully subscribed to our newsletter.\"}}</p> -->\r\n\r\n<p class=\"greeting\"  style=\"padding-top:20px ;padding-left:20px;\">{{trans \"Hello %name,\" name=$customer.name}}\r\n</p>\r\n\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Thanks for subscribing to our newsletter, of ComAve!You’ve been added to our mailing list and will now be among the first to hear about special offers and discounts etc.\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"As a thank you gift from us, we have credited \"}} <span style= \"color:#d91f26 !important;\"> ”20LIX Rewards in your LIX wallet.\" </span> </p>\r\n\r\n<!--<table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n    <tr>\r\n        <td>\r\n            <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"25%\">\r\n                <tr>\r\n                    <td align=\"center\" style=\"padding: 8px 0 !important\">\r\n                        <a href=\"{{var this.getUrl($store,'/',[_nosid:1])}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"VISIT STORE\"}}</a>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n        </td>\r\n    </tr>\r\n</table>-->\r\n\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"If you have any questions or need assistance, our support team is here to help. Feel free to reach out to <NAME_EMAIL>\"}}</p>\r\n\r\n<p style=\"margin: 10px 0 !important; padding-left:20px;\">{{trans \"Thank you for shopping with us. \"}}</p>\r\n\r\n<!-- <p style= \"margin-left:20px;\">{{trans '<a href=\"%unsubscription_url\">Unsubscribe</a> from these notifications.' unsubscription_url=$unsubscription_url |raw}}</p> -->\r\n\r\n<p  style=\"padding-left:20px;\">{{trans \"Best Regards,\"}}</p>\r\n<p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"The ComAve Team\"}}</p>\r\n\r\n\r\n\r\n{{template config_path=\"design/email/footer_template\"}}", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Thank you for joining ComAve’s Mailing list\"}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2024-04-30 10:35:38", "modified_at": "2024-06-21 19:49:17", "orig_template_code": "newsletter_subscription_confirm_weltpixel", "orig_template_variables": "{\"var subscriber_data.confirmation_link\":\"Subscriber Confirmation URL\"}", "is_legacy": "1", "template_preheader": null}, {"template_id": "258", "template_code": "{{trans \"Reset ComAve Account Password.\" }}", "template_text": "{{template config_path=\"design/email/header_template\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px; display:none !important;\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<div style=\"padding: 0 10px 0 10px;\">\n    <p class=\"greeting\" style=\"margin-top: 15px; margin-left:20px;\">{{trans \"Hello %name,\" name=$customer.name}}</p>\n\n    <p style=\"padding-left:20px;\"> {{trans \" There was recently a request to change the password on your account.\"}}</p>\n    <p style=\"padding-left:20px;\"> {{trans \"If you requested this change, then please select the 'Reset Password' box below:\"}}</p>\n\n    <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n        <tr>\n            <td>\n                <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"50%\">\n                    <tr>\n                        <td align=\"center\" style=\"padding: 8px 0 !important; \">\n                            {{if is_seller}}\n                            <a href=\"{{config path='web/secure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a>\n                            {{else}}\n                            <a href=\"{{config path='web/secure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a>\n                            <!-- frontend uses unsecure url to be updated when the FE functionality is done -->\n                            <!--a href=\"{{config path='web/unsecure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a-->\n                            {{/if}}\n                        </td>\n                    </tr>\n                </table>\n            </td>\n        </tr>\n    </table>\n\n    <p  style=\"padding-left:20px;\">{{trans \"If you didn't initiate this request, please contact us <NAME_EMAIL>\"}}</p>\n\n    <p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\n    <p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "", "template_sender_name": null, "template_sender_email": null, "added_at": "2025-06-16 16:44:29", "modified_at": "2025-06-16 16:44:29", "orig_template_code": null, "orig_template_variables": "{\"var store.frontend_name\":\"Store Name\",\"var customer.name\":\"Customer Name\",\"var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])\":\"Reset Password URL\"}\n", "is_legacy": "1", "template_preheader": null}, {"template_id": "259", "template_code": "Forgot Password - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\n\n<table align=\"center\" style=\"background-color:#000; text-align:center; width: 660px; display:none !important;\">\n    <tbody>\n    <tr>\n        <td class=\"dark\"  style=\"padding-bottom:8px; padding-top:5px; background-color:#000\">\n            <h3 style=\"text-align: center; text-transform: uppercase;\">\n\n            </h3>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:0px; background-color:#000\">\n            <h1 style=\"text-align: center; margin: 0 !important\">\n\n            </h1>\n        </td>\n    </tr>\n    <tr>\n        <td class=\"dark\" align=\"center\" style=\"padding-bottom:8px; background-color:#000\">\n            <h3 style=\"text-align: center;\">\n\n            </h3>\n        </td>\n    </tr>\n    </tbody>\n</table>\n<div style=\"padding: 0 10px 0 10px;\">\n    <p class=\"greeting\" style=\"margin-top: 15px; margin-left:20px;\">{{trans \"Hello %name,\" name=$customer.name}}</p>\n\n    <p style=\"padding-left:20px;\"> {{trans \" There was recently a request to change the password on your account.\"}}</p>\n    <p style=\"padding-left:20px;\"> {{trans \"If you requested this change, then please select the 'Reset Password' box below:\"}}</p>\n\n    <table class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n        <tr>\n            <td>\n                <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"50%\">\n                    <tr>\n                        <td align=\"center\" style=\"padding: 8px 0 !important; \">\n                            {{if is_seller}}\n                            <a href=\"{{config path='web/secure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a>\n                            {{else}}\n                            <a href=\"{{config path='web/secure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a>\n                            <!-- frontend uses unsecure url to be updated when the FE functionality is done -->\n                            <!--a href=\"{{config path='web/unsecure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"Reset Password\"}}</a-->\n                            {{/if}}\n                        </td>\n                    </tr>\n                </table>\n            </td>\n        </tr>\n    </table>\n\n    <p  style=\"padding-left:20px;\">{{trans \"If you didn't initiate this request, please contact us <NAME_EMAIL>\"}}</p>\n\n    <p  style=\"padding-left:20px;\">{{trans \"Regards,\"}}</p>\n    <p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"ComAve\"}}</p>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Reset ComAve Account Password.\" }}", "template_sender_name": null, "template_sender_email": null, "added_at": "2025-06-19 16:38:02", "modified_at": "2025-06-19 16:38:02", "orig_template_code": "customer_password_forgot_email_template", "orig_template_variables": "{\"var store.frontend_name\":\"Store Name\",\"var customer.name\":\"Customer Name\",\"var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])\":\"Reset Password URL\"}\n", "is_legacy": "1", "template_preheader": null}, {"template_id": "260", "template_code": "New Account - comave", "template_text": "{{template config_path=\"design/email/header_template\"}}\n\n\n<table align=\"center\" style=\"display: block;  text-align:center; width: 660px;border:2px solid;\">\n  <tbody style=\"display: block;\">\n  <tr style=\"display: block;\">\n    <td class=\"dark\"  style=\"display: block; padding-bottom:8px; padding-top:5px;\">\n      <h3 style=\"text-align: center;\">\n        {{trans \"Hello %name,\" name=$customer.name}}\n      </h3>\n    </td>\n  </tr>\n  <tr style=\"display: block;\">\n    <td class=\"dark\" align=\"center\" style=\"display: block; padding-bottom:0px; \">\n      <h1 style=\"text-align: center; margin: 0 !important\">\n        {{trans 'Welcome To ComAve! ' }}\n      </h1>\n    </td>\n  </tr>\n  </tbody>\n</table>\n<div style=\"padding:0 10px 0 10px;\">\n\n  <p style=\"margin: 20px 0 !important;padding-left:22px;\">\n    {{trans 'We are thrilled to have you join our community. At ComAve, you can participate in exciting tournaments, play multiple games, and earn amazing rewards.\n    To get started, simply log in to your account and explore the endless possibilities.'}}\n  </p>\n  <table style=\"display: block\" class=\"button\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n    <tbody style=\"display: block\">\n    <tr style=\"display: block\">\n      <td style=\"display: block\">\n        <table class=\"inner-wrapper\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" width=\"100%\">\n          <tr>\n            <td align=\"center\" style=\"padding: 8px 0 !important\">\n              <a href=\"{{var baseUrl}}login/\" target=\"_blank\" style=\"font-weight: bold\">{{trans \"LOGIN\"}}</a>\n            </td>\n          </tr>\n        </table>\n      </td>\n    </tr>\n    </tbody>\n  </table>\n\n  <p  style=\"padding-left:20px;padding-top: 10px;\">{{trans \"If you have any questions or need assistance, our support team is here to help. Feel free to reach out to <NAME_EMAIL> \"}}</p>\n\n\n  <p  style=\"padding-left:20px; padding-top: 10px;\">{{trans \"Regards,\"}}</p>\n  <p  style=\"padding-left:20px;color:#d91f26 !important;font-weight: bold !important;\">{{trans \"The ComAve Team\"}}</p>\n</div>\n\n{{template config_path=\"design/email/footer_template\"}}\n", "template_styles": null, "template_type": "2", "template_subject": "{{trans \"Welcome %name to ComAve! Thanks for signing up.\" name=$customer.name}}", "template_sender_name": null, "template_sender_email": null, "added_at": "2025-06-19 16:38:02", "modified_at": "2025-06-19 16:38:02", "orig_template_code": "customer_new_account_weltpixel", "orig_template_variables": "{\"var this.getUrl($store, 'customer/account/')\":\"Customer Account URL\",\"var customer.email\":\"Customer Email\",\"var customer.name\":\"Customer Name\"}", "is_legacy": "1", "template_preheader": null}]