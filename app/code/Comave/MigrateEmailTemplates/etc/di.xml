<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave-email-template-export-json" xsi:type="object"> Comave\MigrateEmailTemplates\Console\ExportEmailTemplates</item>
                <item name="comave-email-template-import-json" xsi:type="object">Comave\MigrateEmailTemplates\Console\ImportEmailTemplates</item>
            </argument>
        </arguments>
    </type>
</config>
