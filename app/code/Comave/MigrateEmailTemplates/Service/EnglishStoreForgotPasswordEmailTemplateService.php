<?php
declare(strict_types=1);

namespace Comave\MigrateEmailTemplates\Service;

use Comave\DbClean\Service\EmailTemplateConfigProvider;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

class EnglishStoreForgotPasswordEmailTemplateService
{
    private const CONFIG_PATH_FORGOT_PASSWORD_EMAIL_TEMPLATE = 'customer/password/forgot_email_template';
    private const CONFIG_PATH_ORIG_FORGOT_PASSWORD_TEMPLATE_CODE = 'Forgot Password - comave';
    private const CONFIG_PATH_ORIG_FORGOT_PASSWORD_EMAIL_TEMPLATE = 'customer_password_forgot_email_template';

    /**
     * @param EmailTemplateConfigProvider $emailTemplateConfigProvider
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EmailTemplateConfigProvider $emailTemplateConfigProvider,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return void
     */
    public function updateEnglishStoreForgotPasswordEmailTemplate(): void
    {
        $storeId = $this->emailTemplateConfigProvider->getStoreIdByCode(EmailTemplateConfigProvider::STORE_CODE_EN);
        $forgotPasswordTemplateConfigId = $this->emailTemplateConfigProvider->getEmailTemplateConfigByStore(
            (int)$storeId,
            'websites',
            self::CONFIG_PATH_FORGOT_PASSWORD_EMAIL_TEMPLATE
        );

        $forgotPasswordTemplateId = $this->emailTemplateConfigProvider->getEmailTemplateId(
            self::CONFIG_PATH_ORIG_FORGOT_PASSWORD_TEMPLATE_CODE,
            self::CONFIG_PATH_ORIG_FORGOT_PASSWORD_EMAIL_TEMPLATE
        );

        if ($forgotPasswordTemplateId !== null
            && $forgotPasswordTemplateConfigId !== null
            && $forgotPasswordTemplateId == $forgotPasswordTemplateConfigId['value']) {
            $this->logger->info('EnglishStoreForgotPasswordEmailTemplate is already updated`');
            return;
        }

        if (isset($forgotPasswordTemplateConfigId['config_id'])) {
            $this->emailTemplateConfigProvider->deleteConfigById((int)$forgotPasswordTemplateConfigId['config_id']);
        }

        $this->emailTemplateConfigProvider->setEmailTemplate(
            (int)$storeId,
            self::CONFIG_PATH_FORGOT_PASSWORD_EMAIL_TEMPLATE,
            (string)$forgotPasswordTemplateId,
            ScopeInterface::SCOPE_WEBSITES
        );
    }
}
