<?php
declare(strict_types=1);

namespace Comave\MigrateEmailTemplates\Service;

use Comave\DbClean\Service\EmailTemplateConfigProvider;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

class EnglishStoreNewAccountEmailTemplateService
{
    private const string CONFIG_PATH_NEW_ACCOUNT_EMAIL_TEMPLATE = 'customer/create_account/email_template';
    private const string CONFIG_PATH_ORIG_NEW_ACCOUNT_TEMPLATE_CODE = 'New Account - comave';
    private const string CONFIG_PATH_ORIG_NEW_ACCOUNT_EMAIL_TEMPLATE = 'customer_new_account_weltpixel';

    /**
     * @param EmailTemplateConfigProvider $emailTemplateConfigProvider
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EmailTemplateConfigProvider $emailTemplateConfigProvider,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return void
     */
    public function updateEnglishStoreNewAccountEmailTemplate(): void
    {
        $storeId = $this->emailTemplateConfigProvider->getStoreIdByCode(EmailTemplateConfigProvider::STORE_CODE_EN);
        $emailNewAccountTemplateConfigId = $this->emailTemplateConfigProvider->getEmailTemplateConfigByStore(
            (int)$storeId,
            'websites',
            self::CONFIG_PATH_NEW_ACCOUNT_EMAIL_TEMPLATE
        );

        $newAccountTemplateId = $this->emailTemplateConfigProvider->getEmailTemplateId(
            self::CONFIG_PATH_ORIG_NEW_ACCOUNT_TEMPLATE_CODE,
            self::CONFIG_PATH_ORIG_NEW_ACCOUNT_EMAIL_TEMPLATE
        );

        if ($newAccountTemplateId !== null
            && $emailNewAccountTemplateConfigId !== null
            && $newAccountTemplateId == $emailNewAccountTemplateConfigId['value']) {
            $this->logger->info('EnglishStoreNewAccountEmailTemplate is already updated`');
            return;
        }

        if (isset($emailNewAccountTemplateConfigId['config_id'])) {
            $this->emailTemplateConfigProvider->deleteConfigById((int)$emailNewAccountTemplateConfigId['config_id']);
        }

        $this->emailTemplateConfigProvider->setEmailTemplate(
            (int)$storeId,
            self::CONFIG_PATH_NEW_ACCOUNT_EMAIL_TEMPLATE,
            (string)$newAccountTemplateId,
            ScopeInterface::SCOPE_WEBSITES
        );
    }
}
