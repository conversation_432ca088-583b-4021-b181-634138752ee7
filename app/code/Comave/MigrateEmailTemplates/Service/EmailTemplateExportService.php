<?php
declare(strict_types=1);

namespace Comave\MigrateEmailTemplates\Service;

use Magento\Framework\App\ResourceConnection;
use Comave\MigrateEmailTemplates\Service\ExportPathProvider;
use Psr\Log\LoggerInterface;

class EmailTemplateExportService
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param \Comave\MigrateEmailTemplates\Service\ExportPathProvider $exportPathProvider
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly ExportPathProvider $exportPathProvider,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return int
     */
    public function exportEmailTemplates(): int
    {
        try {
            $emailTemplates = $this->getEmailTemplates();

            if (empty($emailTemplates)) {
                $this->logger->info("No email templates found to export.");
                return 0;
            }
            $exportPath = $this->exportPathProvider->getExportFilePath();
            $this->exportPathProvider->writeEmailTemplatesToFile($emailTemplates);

            $noEmailTemplates = count($emailTemplates);
            $this->logger->info("Exported $noEmailTemplates email templates to $exportPath");

            return $noEmailTemplates;
        } catch (\Exception $exception) {
            $this->logger->error(
                'Error while exporting email templates: '
                . $exception->getMessage()
            );
            return 0;
        }
    }

    /**
     * @return array
     */
    private function getEmailTemplates(): array
    {
        try {
            return $this->resourceConnection->getConnection()->fetchAll(
                $this->resourceConnection->getConnection()
                    ->select()
                    ->from(
                        'email_template',
                        [
                            'template_code',
                            'template_text',
                            'template_styles',
                            'template_type',
                            'template_subject',
                            'template_sender_name',
                            'template_sender_email',
                            'added_at',
                            'modified_at',
                            'orig_template_code',
                            'orig_template_variables',
                            'is_legacy',
                            'template_preheader',
                        ]
                    )
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage());
            return [];
        }
    }
}
