<?php
declare(strict_types=1);

namespace Comave\MigrateEmailTemplates\Service;

use Magento\Framework\App\ResourceConnection;
use Comave\MigrateEmailTemplates\Service\ExportPathProvider;
use Comave\MigrateEmailTemplates\Service\EmailTemplateDeletionService;
use Psr\Log\LoggerInterface;

class EmailTemplateImportService
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param \Comave\MigrateEmailTemplates\Service\ExportPathProvider $exportPathProvider
     * @param \Comave\MigrateEmailTemplates\Service\EmailTemplateDeletionService $emailTemplateDeletionService
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly ExportPathProvider $exportPathProvider,
        private readonly EmailTemplateDeletionService $emailTemplateDeletionService,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return int
     */
    public function importEmailTemplates(): int
    {
        try {
            $importFilePath = $this->exportPathProvider->getExportFilePath();
            $emailTemplates = $this->exportPathProvider->getEmailTemplatesFromExportFile();

            if (empty($emailTemplates)) {
                $this->logger->error(
                    sprintf(
                        'Import file is empty or contains no templates: %s',
                        $importFilePath
                    )
                );
                return 0;
            }

            $this->emailTemplateDeletionService->deleteExistingEmailTemplates();
            $insertedEmailTemplates = $this->insertEmailTemplates($emailTemplates);
            $removedDuplicateTemplates = $this->emailTemplateDeletionService->removeDuplicateTemplatesByOrigCode();
            return $insertedEmailTemplates - $removedDuplicateTemplates;
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf(
                    'Error importing email templates: %s',
                    $e->getMessage()
                ),
            );
            return 0;
        }
    }

    /**
     * @param array $emailTemplates
     * @return int
     */
    private function insertEmailTemplates(array $emailTemplates): int
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('email_template');

        $importedEmailTemplates = 0;
        foreach ($emailTemplates as $template) {
            try {
                $connection->insertOnDuplicate($tableName, $template, array_keys($template));
                $importedEmailTemplates++;
            } catch (\Exception $e) {
                $this->logger->error(
                    sprintf(
                        'Failed to insert template: %s',
                        $e->getMessage()
                    )
                );
            }
        }

        $this->logger->info(
            sprintf(
                'Imported %d email templates',
                $importedEmailTemplates,
            )
        );

        return $importedEmailTemplates;
    }
}
