<?php
declare(strict_types=1);

namespace Comave\MigrateEmailTemplates\Service;

use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class EmailTemplateDeletionService
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * @return void
     */
    public function deleteExistingEmailTemplates(): void
    {
        try {
            $this->resourceConnection->getConnection()->delete(
                $this->resourceConnection->getTableName('email_template')
            );
        } catch (\Exception $exception) {
            $this->logger->error(
                sprintf(
                    'Could not delete existing email templates: %s',
                    $exception->getMessage()
                )
            );
        }
    }

    /**
     * @return int
     */
    public function removeDuplicateTemplatesByOrigCode(): int
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $connection->getTableName('email_template');
        $subSelect = $connection->select()
            ->from(['et2' => $tableName], ['template_id'])
            ->where('et2.orig_template_code = et1.orig_template_code')
            ->order('et2.modified_at DESC')
            ->limit(1);

        $keepSelect = $connection->select()
            ->from(['et1' => $tableName], ['template_id'])
            ->where('et1.template_id = (' . $subSelect . ')');

        $duplicatesSelect = $connection->select()
            ->from($tableName, ['template_id'])
            ->where('orig_template_code IS NOT NULL')
            ->where('template_id NOT IN (?)', $keepSelect);

        $duplicateIds = $connection->fetchCol($duplicatesSelect);

        if (!empty($duplicateIds)) {
            $where = ['template_id IN (?)' => $duplicateIds];
            $connection->delete($tableName, $where);
            $this->logger->info(
                sprintf(
                    'Removed %d duplicate email templates by original template code.',
                    count($duplicateIds),
                )
            );
            return count($duplicateIds);
        }

        return 0;
    }
}
