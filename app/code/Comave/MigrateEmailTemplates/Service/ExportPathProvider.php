<?php
declare(strict_types=1);

namespace Comave\MigrateEmailTemplates\Service;

use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Magento\Framework\Module\Dir;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

class ExportPathProvider
{
    private const EXPORT_SOURCE_DIR = 'export-data';
    private const EXPORT_FILE_NAME = 'email-templates.json';

    /**
     * @param DirReader $dirReader
     * @param File $fileDriver
     * @param IoFile $ioFile
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly DirReader $dirReader,
        private readonly File $fileDriver,
        private readonly IoFile $ioFile,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return string
     */
    public function getExportDirectory(): string
    {
        $exportPath = $this->dirReader->getModuleDir(
                Dir::MODULE_ETC_DIR,
                'Comave_MigrateEmailTemplates'
            ) . '/' . self::EXPORT_SOURCE_DIR . '/';

        if (!$this->fileDriver->isExists($exportPath)) {
            $this->fileDriver->createDirectory($exportPath);
        }

        return $exportPath;
    }

    /**
     * @return string
     */
    public function getExportFileName(): string
    {
        return self::EXPORT_FILE_NAME;
    }

    /**
     * @return string
     */
    public function getExportFilePath(): string
    {
        return $this->getExportDirectory() . self::EXPORT_FILE_NAME;
    }

    /**
     * @param string $filePath
     * @return bool
     */
    public function fileExists(string $filePath): bool
    {
        return $this->fileDriver->isExists($filePath);
    }

    public function writeEmailTemplatesToFile(array $emailTemplates): void
    {
        $exportFilePath = $this->getExportFilePath();

        $json = json_encode($emailTemplates, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        $this->ioFile->write($exportFilePath, $json);

        $this->logger->info('Exported ' . count($emailTemplates) . " email templates to $exportFilePath");
    }

    public function getEmailTemplatesFromExportFile(): array
    {
        $filePath = $this->getExportFilePath();

        if (!$this->fileExists($filePath)) {
            $this->logger->error('Import file not found at: '. $filePath);
            throw new LocalizedException('Email template import file does not exist.');
        }

        $json = $this->ioFile->read($filePath);
        $emailTemplates = json_decode($json, true);

        if (!is_array($emailTemplates)) {
            $this->logger->error('Invalid JSON format in import file:'. $filePath);
            throw new LocalizedException(__('Email template import file contains invalid JSON.'));
        }

        return $emailTemplates;
    }
}
