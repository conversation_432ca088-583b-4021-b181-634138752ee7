<?php
declare(strict_types=1);

namespace Comave\MigrateEmailTemplates\Console;

use Comave\MigrateEmailTemplates\Service\EmailTemplateImportService;
use Magento\Framework\App\State;
use Symfony\Component\Console\Command\Command;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Comave\MigrateEmailTemplates\Service\ImportEmailTemplatesService;
use Magento\Framework\App\Area;
use Comave\MigrateEmailTemplates\Service\EnglishStoreHeaderEmailTemplateService;
use Comave\MigrateEmailTemplates\Service\EnglishStoreNewAccountEmailTemplateService;
use Comave\MigrateEmailTemplates\Service\EnglishStoreForgotPasswordEmailTemplateService;
use Psr\Log\LoggerInterface;

class ImportEmailTemplates extends Command
{
    /**
     * @param State $appState
     * @param EmailTemplateImportService $emailTemplateImportService
     * @param EnglishStoreHeaderEmailTemplateService $englishStoreHeaderEmailTemplate
     * @param EnglishStoreNewAccountEmailTemplateService $newAccountEmailTemplateService
     * @param EnglishStoreForgotPasswordEmailTemplateService $englishStoreForgotPasswordEmailTemplateService
     * @param LoggerInterface $logger
     * @param string|null $name
     */
    public function __construct(
        private readonly State $appState,
        private readonly EmailTemplateImportService $emailTemplateImportService,
        private readonly EnglishStoreHeaderEmailTemplateService $englishStoreHeaderEmailTemplate,
        private readonly EnglishStoreNewAccountEmailTemplateService $newAccountEmailTemplateService,
        private readonly EnglishStoreForgotPasswordEmailTemplateService $englishStoreForgotPasswordEmailTemplateService,
        private readonly LoggerInterface $logger,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this->setName('comave:email-template:import-json')
            ->setDescription('Import email templates from json file');
        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->appState->setAreaCode(Area::AREA_ADMINHTML);

        try {
            $importedTemplates = $this->emailTemplateImportService->importEmailTemplates();
            $output->writeln('Imported ' . $importedTemplates . ' email templates ');

            $this->englishStoreHeaderEmailTemplate->updateEnglishStoreHeaderEmailTemplate();
            $output->writeln('English store: header email template has been updated.');

            $this->newAccountEmailTemplateService->updateEnglishStoreNewAccountEmailTemplate();
            $output->writeln('English store: new account email template has been updated.');

            $this->englishStoreForgotPasswordEmailTemplateService->updateEnglishStoreForgotPasswordEmailTemplate();
            $output->writeln('English store: new forgot password email template has been updated.');

            return Cli::RETURN_SUCCESS;
        } catch (\Exception $e) {
            $output->write('No email templates imported');
            $this->logger->error('Error: ' . $e->getMessage());
            return Cli::RETURN_FAILURE;
        }
    }
}
