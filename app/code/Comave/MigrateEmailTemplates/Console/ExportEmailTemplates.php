<?php
declare(strict_types=1);

namespace Comave\MigrateEmailTemplates\Console;

use Symfony\Component\Console\Command\Command;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\App\State;
use Comave\MigrateEmailTemplates\Service\EmailTemplateExportService;
use Magento\Framework\App\Area;
use Psr\Log\LoggerInterface;

class ExportEmailTemplates extends Command
{
    /**
     * @param State $appState
     * @param LoggerInterface $logger
     * @param EmailTemplateExportService $emailTemplateExportService
     * @param string|null $name
     */
    public function __construct(
        private readonly State $appState,
        private readonly LoggerInterface $logger,
        private readonly EmailTemplateExportService $emailTemplateExportService,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('comave:email-template:export-json')
            ->setDescription('Export email templates as json file');
        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->appState->setAreaCode(Area::AREA_ADMINHTML);

        try {
            $exportedEmailTemplates = $this->emailTemplateExportService->exportEmailTemplates();

            if ($exportedEmailTemplates === 0) {
                return Cli::RETURN_FAILURE;
            }

            $this->logger->info($exportedEmailTemplates . ' email templates exported');

            return Cli::RETURN_SUCCESS;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());

            return Cli::RETURN_FAILURE;
        }
    }
}
