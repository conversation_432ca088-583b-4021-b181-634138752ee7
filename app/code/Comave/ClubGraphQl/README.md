# Magento 2 Module: Club Categories GraphQL

This Magento 2 module introduces a custom GraphQL extension to retrieve category data filtered by a custom attribute `clubs`. This is useful for storefronts needing to expose category listings tied to specific clubs, like `"arsenal-fc"`.

## Features

- Adds a `categories` GraphQL query filterable by the `clubs` attribute.
- Useful for headless storefronts where content is filtered by team, club, or affiliation.
- Returns category `id` and `name` ( or any CategoryInterface object found with the given club uniqueid - see **getClubs** query)

---

## GraphQL Query Example

You can use the following query to fetch all categories associated with a specific club:

```graphql
query {
  categories(
    filters: {
      clubs: {
        in: ["arsenal-fc"]
      }
    }
  ) {
    items {
      id
      name
    }
  }
}
```

Sample Response:
```
{
  "data": {
    "categories": {
      "items": [
        {
          "id": 12,
          "name": "Arsenal Apparel"
        },
        {
          "id": 15,
          "name": "Arsenal Memorabilia"
        }
      ]
    }
  }
}
```
