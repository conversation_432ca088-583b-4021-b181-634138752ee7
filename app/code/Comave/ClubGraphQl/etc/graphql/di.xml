<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Eav\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor">
        <arguments>
            <argument xsi:type="array" name="customFilters">
                <item name="clubs" xsi:type="object">Comave\ClubGraphQl\GraphQl\Filter\Clubs</item>
            </argument>
        </arguments>
    </type>
</config>
