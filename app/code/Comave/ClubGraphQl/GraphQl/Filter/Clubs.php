<?php

declare(strict_types=1);

namespace Comave\ClubGraphQl\GraphQl\Filter;

use Magento\Catalog\Model\ResourceModel\Category\Collection;
use Magento\Framework\Api\Filter;
use Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor\CustomFilterInterface;
use Magento\Framework\Data\Collection\AbstractDb;

class Clubs implements CustomFilterInterface
{
    /**
     * @param Filter $filter
     * @param AbstractDb $collection
     * @return bool
     */
    public function apply(Filter $filter, AbstractDb $collection): bool
    {
        if (!$collection instanceof Collection) {
            return false;
        }

        $hasRowId = $collection->getConnection()->tableColumnExists(
            $collection->getConnection()->getTableName('catalog_category_entity'),
            'row_id'
        );
        $collection->getSelect()
            ->join(
                ['cpc' => $collection->getConnection()->getTableName('catalog_category_product')],
                sprintf('cpc.category_id = e.%s', $hasRowId ? 'row_id' : 'entity_id'),
                []
            )->join(
                ['ccp' => $collection->getConnection()->getTableName('comave_club_product')],
                'cpc.product_id = ccp.product_id',
                []
            )->join(
                ['cc' => $collection->getConnection()->getTableName('comave_club')],
                'cc.club_id = ccp.club_id',
                []
            )->where(
                $filter->getConditionType() === 'in' ?
                    'cc.uniqueid IN (?)' :
                    'cc.uniqueid = ?',
                $filter->getValue()
            )->group('cpc.category_id');

        return true;
    }
}
