<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model;

use Comave\MarketplaceCatalogImport\Api\ImportEntityRepositoryInterface;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Api\SearchResultsInterfaceFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterfaceFactory;
use Comave\MarketplaceCatalogImport\Model\ResourceModel\ImportEntity as ResourceModel;
use Comave\MarketplaceCatalogImport\Model\ResourceModel\ImportEntity\CollectionFactory;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class ImportEntityRepository implements ImportEntityRepositoryInterface
{
    /**
     * @param \Comave\MarketplaceCatalogImport\Model\ResourceModel\ImportEntity $resourceModel
     * @param \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterfaceFactory $modelFactory
     * @param \Magento\Framework\Api\SearchResultsInterfaceFactory $searchResultsFactory
     * @param \Comave\MarketplaceCatalogImport\Model\ResourceModel\ImportEntity\CollectionFactory $collectionFactory
     * @param \Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        private readonly ResourceModel $resourceModel,
        private readonly ImportEntityInterfaceFactory $modelFactory,
        private readonly SearchResultsInterfaceFactory $searchResultsFactory,
        private readonly CollectionFactory $collectionFactory,
        private readonly CollectionProcessorInterface $collectionProcessor,
    ) {
    }

    /**
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Magento\Framework\Api\SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface
    {
        /** @var AbstractCollection $collection */
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        $collection->walk(
            function (ImportEntityInterface $importEntity) {
                $this->resourceModel->unserializeFields($importEntity);
            }
        );

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria)
            ->setItems($collection->getItems()) //@phpstan-ignore-line
            ->setTotalCount($collection->getSize());

        return $searchResults;
    }

    /**
     * @param \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface $entity
     * @return \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface
     */
    public function save(ImportEntityInterface $entity): ImportEntityInterface
    {
        /** @var \Magento\Framework\Model\AbstractModel $entity */
        $this->resourceModel->save($entity);

        /** @var \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface $entity */
        return $entity;
    }

    /**
     * @param \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface $entity
     * @return void
     */
    public function delete(ImportEntityInterface $entity): void
    {
        /** @var \Magento\Framework\Model\AbstractModel $entity */
        $this->resourceModel->delete($entity);
    }

    /**
     * @param int $id
     * @return void
     */
    public function deleteById(int $id): void
    {
        /** @var \Magento\Framework\Model\AbstractModel $model */
        $model = $this->get($id);
        $this->resourceModel->delete($model);
    }

    /**
     * @param int $id
     * @return \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface
     */
    public function get(int $id): ImportEntityInterface
    {
        /** @var \Magento\Framework\Model\AbstractModel $model */
        $model = $this->modelFactory->create();
        $this->resourceModel->load($model, $id);

        if (!$model->getId()) {
            throw new NoSuchEntityException(__('Notification with id "%1" does not exist.', $id));
        }

        /** @var \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface $model */

        return $model;
    }
}
