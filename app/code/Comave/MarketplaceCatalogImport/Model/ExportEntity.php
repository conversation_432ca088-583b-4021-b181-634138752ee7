<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model;

use Magento\Framework\Model\AbstractModel;
use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;

class ExportEntity extends AbstractModel implements ExportEntityInterface
{
    /**
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(
            \Comave\MarketplaceCatalogImport\Model\ResourceModel\ExportEntity::class
        );
    }

    /**
     * @return bool|null
     */
    public function getEnabled(): ?bool
    {
        return (bool) $this->getData(self::ENABLED);
    }

    /**
     * @param bool $enabled
     * @return $this
     */
    public function setEnabled(bool $enabled): static
    {
        return $this->setData(self::ENABLED, $enabled);
    }

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): static
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): static
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * @return mixed[][]|null
     */
    public function getAttributeConfiguration(): ?array
    {
        return $this->getData(self::ATTRIBUTE_CONFIGURATION);
    }

    /**
     * @param array $attributeConfiguration
     * @return $this
     */
    public function setAttributeConfiguration(array $attributeConfiguration): static
    {
        return $this->setData(self::ATTRIBUTE_CONFIGURATION, $attributeConfiguration);
    }

    /**
     * @return string|null
     */
    public function getCountryId(): ?string
    {
        return $this->getData(self::COUNTRY_ID);
    }

    /**
     * @param string $countryId
     * @return $this
     */
    public function setCountryId(string $countryId): static
    {
        return $this->setData(self::COUNTRY_ID, $countryId);
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->getData(self::NAME);
    }

    /**
     * @param string $name
     * @return $this
     */
    public function setName(string $name): static
    {
        return $this->setData(self::NAME, $name);
    }
}
