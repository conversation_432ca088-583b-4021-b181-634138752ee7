<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\Command;

use Comave\MarketplaceCatalogImportUi\Exception\InvalidRowException;
use Magento\Catalog\Model\Product;
use Magento\EavGraphQl\Model\Resolver\DataProvider\AttributeOptions;

class ConfigurableProductValidator
{
    public const string KEY_PATH = 'configurable_variations';

    /**
     * @param AttributeOptions $attributeOptions
     */
    public function __construct(
        private readonly AttributeOptions $attributeOptions
    ) {
    }

    /**
     * @param array $importedRows
     * @return void
     * @throws InvalidRowException
     */
    public function execute(array $importedRows): void
    {
        $headerRows = $importedRows[0];
        $configurableIndex = array_search(self::KEY_PATH, $headerRows);
        $typeIndex = array_search('type', $headerRows);
        array_shift($importedRows);
        $configurableProducts = array_filter($importedRows, function ($row) use ($typeIndex) {
            return $row[$typeIndex] === 'configurable';
        });

        if (empty($configurableProducts)) {
            return;
        }

        foreach ($configurableProducts as $rowNr => $configurableProduct) {
            if (empty($configurableProduct[$configurableIndex])) {
                $exception = new InvalidRowException(
                    __('Missing configuration value for row %1', $rowNr)
                );
                $exception->setRowNr($rowNr);

                throw $exception;
            }

            $variationData = explode('|', $configurableProduct[$configurableIndex]);

            foreach ($variationData as $variation) {
                $variationItemData = explode(',', $variation);
                $this->validateHasVariation($rowNr, $variationItemData);
                $skuArr = $this->validateSku($rowNr, $variationItemData);
                $simpleProductSku = str_replace('sku=', '', current($skuArr));
                $simpleProductPosition = $this->validateSimpleSkuExists(
                    $rowNr,
                    $headerRows,
                    $importedRows,
                    $simpleProductSku
                );
                $configurationValues = array_diff($variationItemData, $skuArr);
                $simpleProductConfiguration = array_combine(
                    $headerRows,
                    $importedRows[$simpleProductPosition]
                );

                foreach ($configurationValues as $configurationValue) {
                    [$attributeCode, $attributeValue] = explode('=', $configurationValue);
                    $attributeOptions = $this->attributeOptions->getData(
                        Product::ENTITY,
                        $attributeCode
                    );

                    $this->validateAttributeOptionValue(
                        $rowNr,
                        $attributeValue,
                        $attributeCode,
                        $attributeOptions
                    );
                    $this->validateProductConfiguration(
                        $rowNr,
                        $attributeValue,
                        $attributeCode,
                        $simpleProductConfiguration
                    );
                }
            }
        }
    }

    /**
     * @param int $rowNr
     * @param array $variationItemData
     * @return void
     * @throws InvalidRowException
     */
    private function validateHasVariation(int $rowNr, array $variationItemData): void
    {
        if (count($variationItemData) === 1) {
            $invalidRowException = new InvalidRowException(
                __('No variation data found in row %1', $rowNr)
            );
            $invalidRowException->setRowNr($rowNr);

            throw $invalidRowException;
        }
    }

    /**
     * @param int $rowNr
     * @param array $variationItemData
     * @return array
     * @throws InvalidRowException
     */
    private function validateSku(int $rowNr, array $variationItemData): array
    {
        $hasSku = array_filter($variationItemData, function ($item) {
            return str_contains($item, 'sku');
        });

        if (!count($hasSku) || count($hasSku) > 1) {
            $invalidRowException = new InvalidRowException(
                __('SKU misconfiguration for configurable product configuration in row %1', $rowNr)
            );
            $invalidRowException->setRowNr($rowNr);

            throw $invalidRowException;
        }

        return $hasSku;
    }

    /**
     * @param int $rowNr
     * @param array $headerRows
     * @param array $importedRows
     * @param string $simpleProductSku
     * @return int
     * @throws InvalidRowException
     */
    private function validateSimpleSkuExists(
        int $rowNr,
        array $headerRows,
        array $importedRows,
        string $simpleProductSku,
    ): int {
        $skuIndex = array_search('sku', $headerRows);
        $hasSimpleProductFound = array_search(
            $simpleProductSku,
            array_column($importedRows, $skuIndex)
        );

        if ($hasSimpleProductFound === false) {
            $invalidRowException = new InvalidRowException(
                __('Unable to find simple product %1 in row %2', $simpleProductSku, $rowNr)
            );
            $invalidRowException->setRowNr($rowNr);

            throw $invalidRowException;
        }

        return (int) $hasSimpleProductFound;
    }

    /**
     * @param int $rowNr
     * @param mixed $attributeValue
     * @param string $attributeCode
     * @param array $attributeOptions
     * @return void
     * @throws InvalidRowException
     */
    private function validateAttributeOptionValue(
        int $rowNr,
        mixed $attributeValue,
        string $attributeCode,
        array $attributeOptions
    ): void {
        $hasValue = array_search($attributeValue, array_column($attributeOptions, 'label'));

        if ($hasValue === false) {
            $invalidRowException = new InvalidRowException(
                __('Attribute value "%1" for attribute "%2" not found in row %3',
                    $attributeValue,
                    $attributeCode,
                    $rowNr
                )
            );
            $invalidRowException->setRowNr($rowNr);

            throw $invalidRowException;
        }
    }

    /**
     * @param int $rowNr
     * @param mixed $attributeValue
     * @param string $attributeCode
     * @param array $simpleProductRow
     * @return void
     * @throws InvalidRowException
     */
    private function validateProductConfiguration(
        int $rowNr,
        mixed $attributeValue,
        string $attributeCode,
        array $simpleProductRow
    ): void {
        $productAttributeValue = $simpleProductRow[$attributeCode] ?? false;

        if ($productAttributeValue === false || $simpleProductRow[$attributeCode] !== $attributeValue) {
            $exception = new InvalidRowException(
                __('Product attribute value mismatch between configurable variation and simple product in row %1', $rowNr)
            );
            $exception->setRowNr($rowNr);

            throw $exception;
        }
    }
}
