<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\Command;

use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;
use Magento\Framework\App\ResourceConnection;

class ImportedRowsProvider
{
    /**
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(private readonly ResourceConnection $resourceConnection)
    {
    }

    /**
     * @param ImportEntityInterface $importEntity
     * @return array
     */
    public function getRows(ImportEntityInterface $importEntity): array
    {
        $connection = $this->resourceConnection->getConnection('read');
        $rowsSelect = $connection->select()
            ->from(
                ['main' => $connection->getTableName('comave_product_import_rows')]
            )->where(
                'import_id = ?',
                $importEntity->getId()
            );

        return $connection->fetchAll($rowsSelect);
    }
}
