<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\Command;

class ImportDataPreProcess
{
    public const string CONFIGURABLE_PRODUCTS = 'configurable_products';
    public const string SIMPLE_PRODUCTS = 'simple_products';

    /**
     * @param array $importingRows
     * @return array[]
     * @throws \JsonException
     */
    public function preprocess(array $importingRows): array
    {
        $configurableProducts = [];
        $nonConfigurableProducts = [];
        $formattedData = [];

        foreach ($importingRows as $productRow) {
            $columnData = json_decode(
                $productRow['column_data'],
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            $decodedProductData = json_decode(
                $productRow['data'],
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            $formattedData[] = array_combine($columnData, $decodedProductData);
        }

        foreach ($formattedData as $formattedRow) {
            if ($formattedRow['type'] === 'configurable') {
                $configurableProducts[$formattedRow['sku']] = [
                    'product_data' => $formattedRow,
                    'children' => []
                ];
                preg_match_all(
                    '/sku=([^,|]+)/',
                    $formattedRow[ConfigurableProductValidator::KEY_PATH],
                    $matches
                );

                foreach ($matches[1] as $childSku) {
                    $foundSku = array_search(
                        $childSku,
                        array_column($formattedData, 'sku')
                    );
                    $configurableProducts[$formattedRow['sku']]['children'][$childSku] = $formattedData[$foundSku];
                    unset($nonConfigurableProducts[$childSku]);
                }
            } else {
                $nonConfigurableProducts[$formattedRow['sku']] = $formattedRow;
            }
        }

        return [
            self::CONFIGURABLE_PRODUCTS => $configurableProducts,
            self::SIMPLE_PRODUCTS => $nonConfigurableProducts
        ];
    }
}
