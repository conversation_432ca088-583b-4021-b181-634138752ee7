<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\DataHandler;

use Comave\MarketplaceCatalogImport\Api\ProductDataHandlerInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Framework\DB\Select;

class Category implements ProductDataHandlerInterface
{
    private const string KEY = 'category_ids';

    /**
     * @param CollectionFactory $categoryCollectionFactory
     */
    public function __construct(private readonly CollectionFactory $categoryCollectionFactory)
    {
    }

    /**
     * @param ProductInterface $product
     * @param array $rowData
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function handle(ProductInterface $product, array $rowData): void
    {
        if (!isset($rowData[self::KEY]) || empty($rowData[self::KEY])) {
            return;
        }

        if (is_numeric($rowData[self::KEY])) {
            $product->setData(
                self::KEY,
                [$rowData[self::KEY]]
            );

            return;
        }

        $collection = $this->categoryCollectionFactory->create();
        $collection->getSelect()
            ->reset(Select::COLUMNS)
            ->columns(['entity_id']);
        $collection->addAttributeToSelect('category_code', 'inner');
        $collection->addFieldToFilter('category_code', $rowData[self::KEY]);

        if (!$collection->getSize()) {
            $product->unsetData(self::KEY);

            return;
        }

        $product->setData(
            self::KEY,
            [
                $collection->getFirstItem()->getId()
            ]
        );
    }
}
