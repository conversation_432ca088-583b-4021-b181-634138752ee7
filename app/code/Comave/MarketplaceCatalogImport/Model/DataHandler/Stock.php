<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\DataHandler;

use Comave\MarketplaceCatalogImport\Api\ProductDataHandlerInterface;
use Magento\Catalog\Api\Data\ProductInterface;

class Stock implements ProductDataHandlerInterface
{
    private const string KEY = 'quantity_and_stock_status';

    /**
     * @param ProductInterface $product
     * @param array $rowData
     * @return void
     */
    public function handle(ProductInterface $product, array $rowData): void
    {
        if (!isset($rowData[self::KEY]) || $product->getTypeId() === \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE) {
            return;
        }

        $quantity = $rowData[self::KEY];
        $isInStock = $quantity > 1;
        $product->setData(
            self::KEY,
            [
                'qty' => $quantity,
                'is_in_stock' => $isInStock,
            ]
        );
    }
}
