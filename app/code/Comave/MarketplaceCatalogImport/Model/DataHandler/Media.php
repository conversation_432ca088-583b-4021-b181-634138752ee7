<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\DataHandler;

use Comave\MarketplaceCatalogImport\Api\ProductDataHandlerInterface;
use Comave\SellerApi\Model\MediaGalleryRegistry;
use Comave\SellerApi\Model\Queue\Consumer\HandleProductsMedia;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Framework\Serialize\SerializerInterface;

class Media implements ProductDataHandlerInterface
{
    /**
     * @param MediaGalleryRegistry $mediaGalleryRegistry
     */
    public function __construct(
        private readonly MediaGalleryRegistry $mediaGalleryRegistry,
    ) {
    }

    private const array KEYS = [
        'thumbnail',
        'small_image',
        'image'
    ];

    /**
     * @param ProductInterface $product
     * @param array $rowData
     * @return void
     */
    public function handle(ProductInterface $product, array $rowData): void
    {
        foreach (self::KEYS as $dataKey) {
            if (!isset($rowData[$dataKey])) {
                continue;
            }

            $mediaGallery = filter_var($rowData[$dataKey], FILTER_VALIDATE_URL);

            if (!$mediaGallery) {
                continue;
            }

            $this->mediaGalleryRegistry->add([
                'sku' => $product->getSku(),
                'media_gallery_entries' => [
                    [
                        'src' => $mediaGallery,
                        'type' => $dataKey
                    ]
                ]
            ]);
        }
    }
}
