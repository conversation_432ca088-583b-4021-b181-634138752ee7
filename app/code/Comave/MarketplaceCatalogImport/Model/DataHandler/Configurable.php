<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\DataHandler;

use Comave\MarketplaceCatalogImport\Api\ProductDataHandlerInterface;
use Comave\MarketplaceCatalogImport\Model\Command\ConfigurableProductValidator;
use Magento\Catalog\Api\Data\EavAttributeInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;
use Magento\ConfigurableProduct\Helper\Product\Options\Factory;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableType;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Eav\Api\Data\AttributeOptionInterfaceFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

class Configurable implements ProductDataHandlerInterface
{
    /**
     * @param ProductRepositoryInterface $productRepository
     * @param Media $mediaDataHandler
     * @param Stock $stockDataHandler
     * @param Category $categoryHandler
     */
    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly AttributeOptionManagementInterface $attributeOptionManagement,
        private readonly AttributeOptionInterfaceFactory $attributeOptionFactory,
        private readonly Factory $extensionOptionsFactory,
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
        private readonly Media $mediaDataHandler,
        private readonly Stock $stockDataHandler,
        private readonly Category $categoryHandler,
    ) {
    }

    /**
     * @param ProductInterface $product
     * @param array $rowData
     * @return void
     * @throws NoSuchEntityException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    public function handle(ProductInterface $product, array $rowData): void
    {
        if ($product->getTypeId() !== ConfigurableType::TYPE_CODE) {
            return;
        }

        $sellerId = $product->getData('assign_seller')['seller_id'] ?? 0;
        $typeInstance = $product->getTypeInstance();
        $simpleProductIds = $typeInstance instanceof \Magento\ConfigurableProduct\Model\Product\Type\Configurable &&
            $product->getId() ?
                current($typeInstance->getChildrenIds($product->getId())) :
                [];
        $configurableAttributesData = $product->getId() ?
            $product->getTypeInstance()->getConfigurableAttributesAsArray($product) :
            $this->getDefaultConfigurableAttributes($product);

        foreach ($rowData['children'] as $childProductData) {
            $variationSku = $childProductData['sku'];

            try {
                $simpleProduct = $this->productRepository->get(
                    $variationSku,
                    true,
                    null,
                    true
                );
            } catch (NoSuchEntityException) {
                $simpleProduct = clone $product;
                $simpleProduct->setData($childProductData);
                $simpleProduct->setUrlKey($variationSku)
                    ->unsetData('entity_id')
                    ->unsetData('row_id')
                    ->setVisibility(Visibility::VISIBILITY_NOT_VISIBLE)
                    ->setStatus(Status::STATUS_DISABLED)
                    ->setTypeId('simple');
            }

            $this->manageAttributeValues(
                $simpleProduct,
                $configurableAttributesData,
                $childProductData
            );

            if ($sellerId > 0) {
                $simpleProduct->setData(
                    'assign_seller',
                    [
                        'seller_id' => $sellerId
                    ]
                );
            }

            $this->mediaDataHandler->handle($simpleProduct, $childProductData);
            $this->stockDataHandler->handle($simpleProduct, $childProductData);
            $this->categoryHandler->handle($simpleProduct, $childProductData);

            $simpleProduct->save();

            if (!in_array($simpleProduct->getId(), $simpleProductIds)) {
                $simpleProductIds[] = $simpleProduct->getId();
            }
        }

        $extensionAttributes = $product->getExtensionAttributes();
        $configurableOptions = $this->extensionOptionsFactory->create($configurableAttributesData);
        $extensionAttributes->setConfigurableProductLinks($simpleProductIds);
        $extensionAttributes->setConfigurableProductOptions($configurableOptions);
        $product->setCanSaveConfigurableAttributes(true);
        $product->setConfigurableAttributesData($configurableAttributesData);
        $product->setExtensionAttributes($extensionAttributes);
    }

    /**
     * @param ProductInterface $simpleProduct
     * @param array $configurableAttributesData
     * @param array $variation
     * @return void
     * @throws NoSuchEntityException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    private function manageAttributeValues(
        ProductInterface $simpleProduct,
        array &$configurableAttributesData,
        array $variation
    ): void {
        foreach ($configurableAttributesData as &$attributeData) {
            if (!isset($variation[$attributeData['attribute_code']])) {
                throw new LocalizedException(__('Unable to detect %1 variant value', $attributeData['attribute_code']));
            }

            $magentoAttribute = $this->productAttributeRepository->get($attributeData['attribute_code']);
            $attributeValue = trim($variation[$attributeData['attribute_code']]);

            if ($magentoAttribute->usesSource()) {
                $optionId = $magentoAttribute->getSource()->getOptionId($attributeValue);
                if (empty($optionId)) {
                    $optionId = $this->createOption(
                        $attributeData['attribute_code'],
                        $attributeValue
                    );
                }

                $attributeValue = $optionId;
            }

            $simpleProduct->setCustomAttribute(
                $attributeData['attribute_code'],
                $attributeValue
            );
            $valueIndex = $attributeValue . $simpleProduct->getId();

            if (empty($attributeData['values']) || !array_key_exists($valueIndex, $attributeData['values'])) {
                $attributeData['values'][$valueIndex] = [
                    'value_index' => $valueIndex
                ];
            }
        }
    }

    /**
     * @param string $attributeCode
     * @param string|int $attributeValue
     * @return string
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    private function createOption(
        string $attributeCode,
        string|int $attributeValue
    ): string {
        $option = $this->attributeOptionFactory->create();
        $option->setSortOrder(0);
        $option->setIsDefault(false);
        $option->setLabel($attributeValue);

        // Add the option to the attribute
        return $this->attributeOptionManagement->add(
            \Magento\Catalog\Model\Product::ENTITY,
            $attributeCode,
            $option
        );
    }

    /**
     * @param ProductInterface $product
     * @return array
     * @throws NoSuchEntityException
     */
    public function getDefaultConfigurableAttributes(ProductInterface $product): array
    {
        $attributeEntities = [];
        $configurationItems = current(
            explode('|', $product->getData(ConfigurableProductValidator::KEY_PATH))
        );
        $configurationItems = preg_replace('/sku=([^,|]+),/', '', $configurationItems);
        $attributes = explode(',', $configurationItems);

        foreach ($attributes as $attributeStr) {
            [$attributeCode,] = explode('=', $attributeStr);
            $attributeEntities[] = $this->productAttributeRepository->get(
                $attributeCode
            );
        }

        $res = [];
        /** @var EavAttributeInterface $eavAttribute */
        foreach ($attributeEntities as $position => $eavAttribute) {
            $res[$position] = [
                'attribute_id' => $eavAttribute->getAttributeId(),
                'attribute_code' => $eavAttribute->getAttributeCode(),
                'label' => $eavAttribute->getDefaultFrontendLabel(),
                'values' => []
            ];
        }

        return $res;
    }
}
