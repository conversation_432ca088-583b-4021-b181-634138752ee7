<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model;

use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;
use Magento\Framework\Model\AbstractModel;

class ImportEntity extends AbstractModel implements ImportEntityInterface
{
    /**
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(
            \Comave\MarketplaceCatalogImport\Model\ResourceModel\ImportEntity::class
        );
    }

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): static
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): static
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * @return array|null
     */
    public function getErrors(): ?array
    {
        return $this->getData(self::ERRORS);
    }

    /**
     * @param array|null $errors
     * @return $this
     */
    public function setErrors(?array $errors = null): static
    {
        return $this->setData(self::ERRORS, $errors);
    }

    /**
     * @return int|null
     */
    public function getSellerId(): ?int
    {
        return (int) $this->getData(self::SELLER_ID);
    }

    /**
     * @param int $sellerId
     * @return $this
     */
    public function setSellerId(int $sellerId): static
    {
        return $this->setData(self::SELLER_ID, $sellerId);
    }

    /**
     * @return string|null
     */
    public function getProcessedAt(): ?string
    {
        return $this->getData(self::PROCESSED_AT);
    }

    /**
     * @param string $processedAt
     * @return $this
     */
    public function setProcessedAt(string $processedAt): static
    {
        return $this->setData(self::PROCESSED_AT, $processedAt);
    }

    /**
     * @return string|null
     */
    public function getStartedAt(): ?string
    {
        return $this->getData(self::STARTED_AT);
    }

    /**
     * @param string $startedAt
     * @return $this
     */
    public function setStartedAt(string $startedAt): static
    {
        return $this->setData(self::STARTED_AT, $startedAt);
    }

    /**
     * @return int|null
     */
    public function getTemplateEntityId(): ?int
    {
        return (int) $this->getData(self::TEMPLATE_ENTITY_ID);
    }

    /**
     * @param int $templateEntityId
     * @return $this
     */
    public function setTemplateEntityId(int $templateEntityId): static
    {
        return $this->setData(self::TEMPLATE_ENTITY_ID, $templateEntityId);
    }
}
