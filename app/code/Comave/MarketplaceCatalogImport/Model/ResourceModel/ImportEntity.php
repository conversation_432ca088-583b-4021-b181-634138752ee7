<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\ResourceModel;

use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;

class ImportEntity extends \Magento\Framework\Model\ResourceModel\Db\AbstractDb
{
    protected $_serializableFields = [
        ImportEntityInterface::ERRORS => [
            null,
            []
        ]
    ];
    /**
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(
            ImportEntityInterface::TABLE_NAME,
            ImportEntityInterface::ID
        );
    }
}
