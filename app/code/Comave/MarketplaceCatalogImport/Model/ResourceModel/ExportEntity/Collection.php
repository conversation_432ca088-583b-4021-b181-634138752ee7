<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\ResourceModel\ExportEntity;

use Comave\MarketplaceCatalogImport\Model\ExportEntity as Model;
use Comave\MarketplaceCatalogImport\Model\ResourceModel\ExportEntity as ResourceModel;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    /**
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(
            Model::class,
            ResourceModel::class
        );
    }
}
