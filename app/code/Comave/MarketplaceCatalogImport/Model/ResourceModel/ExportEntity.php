<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\ResourceModel;

use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;

class ExportEntity extends \Magento\Framework\Model\ResourceModel\Db\AbstractDb
{
    protected $_serializableFields = [
        ExportEntityInterface::ATTRIBUTE_CONFIGURATION => [
            null,
            []
        ]
    ];
    /**
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(
            ExportEntityInterface::TABLE_NAME,
            ExportEntityInterface::ID
        );
    }
}
