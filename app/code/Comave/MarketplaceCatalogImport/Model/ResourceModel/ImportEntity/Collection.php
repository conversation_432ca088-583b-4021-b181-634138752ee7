<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\ResourceModel\ImportEntity;

use Comave\MarketplaceCatalogImport\Model\ImportEntity as Model;
use Comave\MarketplaceCatalogImport\Model\ResourceModel\ImportEntity as ResourceModel;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    /**
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(
            Model::class,
            ResourceModel::class
        );
    }
}
