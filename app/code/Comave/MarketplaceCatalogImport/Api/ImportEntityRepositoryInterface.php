<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Api;

use Magento\Framework\Api\SearchCriteria;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;

interface ImportEntityRepositoryInterface
{
    /**
     * @param int $id
     * @return \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface
     */
    public function get(int $id): ImportEntityInterface;

    /**
     * @param \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface $entity
     * @return \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface
     */
    public function save(ImportEntityInterface $entity): ImportEntityInterface;

    /**
     * @param \Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface $entity
     * @return void
     */
    public function delete(ImportEntityInterface $entity): void;

    /**
     * @param int $id
     * @return void
     */
    public function deleteById(int $id): void;

    /**
     * @param \Magento\Framework\Api\SearchCriteria $searchCriteria
     * @return \Magento\Framework\Api\SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface;
}
