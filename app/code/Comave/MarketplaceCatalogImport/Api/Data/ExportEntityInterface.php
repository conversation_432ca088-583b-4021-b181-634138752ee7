<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Api\Data;

interface ExportEntityInterface
{
    public const string TABLE_NAME = 'comave_export_entity';
    public const string ID = 'id';
    public const string ENABLED = 'enabled';
    public const string NAME = 'name';
    public const string ATTRIBUTE_CONFIGURATION = 'attribute_configuration';
    public const string COUNTRY_ID = 'country_id';
    public const string CREATED_AT = 'created_at';
    public const string UPDATED_AT = 'updated_at';

    /**
     * @return bool|null
     */
    public function getEnabled(): ?bool;

    /**
     * @param bool $enabled
     * @return $this
     */
    public function setEnabled(bool $enabled): static;

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string;

    /**
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): static;

    /**
     * @return string|null
     */
    public function getUpdatedAt(): ?string;

    /**
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): static;

    /**
     * @return mixed[][]|null
     */
    public function getAttributeConfiguration(): ?array;

    /**
     * @param array $attributeConfiguration
     * @return $this
     */
    public function setAttributeConfiguration(array $attributeConfiguration): static;

    /**
     * @return string|null
     */
    public function getCountryId(): ?string;

    /**
     * @param string $countryId
     * @return $this
     */
    public function setCountryId(string $countryId): static;

    /**
     * @return string|null
     */
    public function getName(): ?string;

    /**
     * @param string $name
     * @return $this
     */
    public function setName(string $name): static;
}
