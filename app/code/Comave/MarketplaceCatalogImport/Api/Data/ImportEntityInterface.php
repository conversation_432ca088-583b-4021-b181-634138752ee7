<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Api\Data;

interface ImportEntityInterface
{
    public const string TABLE_NAME = 'comave_product_import';
    public const string ID = 'id';
    public const string SELLER_ID = 'seller_id';
    public const string TEMPLATE_ENTITY_ID = 'template_entity_id';
    public const string ERRORS = 'errors';
    public const string STARTED_AT = 'started_at';
    public const string PROCESSED_AT  = 'processed_at';
    public const string CREATED_AT = 'created_at';
    public const string UPDATED_AT = 'updated_at';

    /**
     * @return array|null
     */
    public function getErrors(): ?array;

    /**
     * @param array|null $errors
     * @return $this
     */
    public function setErrors(?array $errors = null): static;

    /**
     * @return int|null
     */
    public function getTemplateEntityId(): ?int;

    /**
     * @param int $templateEntityId
     * @return $this
     */
    public function setTemplateEntityId(int $templateEntityId): static;

    /**
     * @return int|null
     */
    public function getSellerId(): ?int;

    /**
     * @param int $sellerId
     * @return $this
     */
    public function setSellerId(int $sellerId): static;

    /**
     * @return string|null
     */
    public function getProcessedAt(): ?string;

    /**
     * @param string $processedAt
     * @return $this
     */
    public function setProcessedAt(string $processedAt): static;

    /**
     * @return string|null
     */
    public function getStartedAt(): ?string;

    /**
     * @param string $startedAt
     * @return $this
     */
    public function setStartedAt(string $startedAt): static;

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string;

    /**
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): static;

    /**
     * @return string|null
     */
    public function getUpdatedAt(): ?string;

    /**
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): static;
}
