<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Api;

use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;

interface ExportEntityRepositoryInterface
{
    /**
     * @param int $id
     * @return \Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface
     */
    public function get(int $id): ExportEntityInterface;

    /**
     * @param \Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface $entity
     * @return \Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface
     */
    public function save(ExportEntityInterface $entity): ExportEntityInterface;

    /**
     * @param \Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface $entity
     * @return void
     */
    public function delete(ExportEntityInterface $entity): void;

    /**
     * @param int $id
     * @return void
     */
    public function deleteById(int $id): void;

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface;
}
