<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_export_entity" resource="default" engine="innodb" comment="Import Templates table">
        <column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="Id"/>
        <column xsi:type="boolean" name="enabled" nullable="false" default="0" comment="Is enabled"/>
        <column xsi:type="varchar" name="name" nullable="false" default="comave_import_template" comment="Is enabled"/>
        <column xsi:type="text" name="country_id" nullable="false" comment="Applies to country"/>
        <column xsi:type="json" name="attribute_configuration" nullable="false" comment="Attribute configuration"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" comment="Updated At Column"
                default="CURRENT_TIMESTAMP"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
    </table>

    <table name="comave_product_import" resource="default" engine="innodb" comment="Import Templates table">
        <column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="Id"/>
        <column xsi:type="int" name="seller_id" padding="10" unsigned="true" identity="false" nullable="false" comment="Seller ID"/>
        <column xsi:type="int" name="template_entity_id" padding="10" unsigned="true" identity="false" nullable="false" comment="Template ID"/>
        <column xsi:type="json" name="errors" nullable="true" comment="Imported Errors"/>
        <column xsi:type="timestamp" name="started_at" on_update="false" nullable="true" comment="Started At"/>
        <column xsi:type="timestamp" name="processed_at" on_update="false" nullable="true" comment="Processed At"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" comment="Updated At Column"
                default="CURRENT_TIMESTAMP"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_PRODUCT_IMPORT_SELLER_ID_SELLER_ENTITY_ID"
                    table="comave_product_import"
                    column="seller_id"
                    referenceTable="marketplace_userdata"
                    referenceColumn="seller_id"
                    onDelete="NO ACTION"/>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_PRODUCT_IMPORT_TEMPLATE_ID_TEMPLATE_ENTITY_ID"
                    table="comave_product_import"
                    column="template_entity_id"
                    referenceTable="comave_export_entity"
                    referenceColumn="id"
                    onDelete="NO ACTION"/>
    </table>

    <table name="comave_product_import_rows" resource="default" engine="innodb" comment="Import Templates table">
        <column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="Id"/>
        <column xsi:type="int" name="import_id" padding="10" unsigned="true" identity="false" nullable="false" comment="Import ID"/>
        <column xsi:type="json" name="column_data" nullable="false" comment="Formatted Product Attribute Data"/>
        <column xsi:type="json" name="data" nullable="false" comment="Formatted Product Data"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" comment="Updated At Column"
                default="CURRENT_TIMESTAMP"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_PRODUCT_IMPORT_ROW_IMPORT_ENTITY_ID"
                    table="comave_product_import_rows"
                    column="import_id"
                    referenceTable="comave_product_import"
                    referenceColumn="id"
                    onDelete="CASCADE"/>
    </table>
</schema>
