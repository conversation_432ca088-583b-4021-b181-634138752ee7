{"comave_export_entity": {"column": {"id": true, "enabled": true, "name": true, "country_id": true, "attribute_configuration": true, "created_at": true, "updated_at": true}, "constraint": {"PRIMARY": true}}, "comave_product_import": {"column": {"id": true, "seller_id": true, "entity_id": true, "template_entity_id": true, "errors": true, "created_at": true, "updated_at": true, "started_at": true, "processed_at": true}, "constraint": {"PRIMARY": true, "COMAVE_PRODUCT_IMPORT_SELLER_ID_MARKETPLACE_USERDATA_SELLER_ID": true, "COMAVE_PRODUCT_IMPORT_ENTITY_ID_COMAVE_EXPORT_ENTITY_ID": true}}, "comave_product_import_rows": {"column": {"id": true, "import_id": true, "data": true, "column_data": true, "processed_at": true, "created_at": true, "updated_at": true}, "constraint": {"PRIMARY": true, "COMAVE_PRODUCT_IMPORT_ROWS_IMPORT_ID_COMAVE_PRODUCT_IMPORT_ID": true}}}