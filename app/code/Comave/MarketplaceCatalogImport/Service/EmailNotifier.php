<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Service;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\AreaInterface;
use Magento\Framework\App\AreaList;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\Store;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\Seller;

class EmailNotifier
{
    /**
     * @param LoggerInterface $logger
     * @param StateInterface $inlineTranslation
     * @param TransportBuilder $transportBuilder
     * @param ScopeConfigInterface $scopeConfig
     * @param AreaList $areaList
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly StateInterface $inlineTranslation,
        private readonly TransportBuilder $transportBuilder,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly AreaList $areaList,
        private readonly CustomerRepositoryInterface $customerRepository,
    ) {
    }

    /**
     * @param Seller $seller
     * @param string $emailTemplate
     * @param array $templateVars
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     */
    public function sendEmail(
        Seller $seller,
        string $emailTemplate,
        array $templateVars = []
    ): void {
        $this->inlineTranslation->suspend();

        try {
            $areaObject = $this->areaList->getArea(Area::AREA_FRONTEND);
            $areaObject->load(AreaInterface::PART_TRANSLATE);

            $sender = [
                'email' => $this->scopeConfig->getValue('trans_email/ident_sales/email'),
                'name' => $this->scopeConfig->getValue('trans_email/ident_sales/name'),
            ];
            $storeId = $seller->getStoreId() ?: Store::DEFAULT_STORE_ID;

            $transport = $this->transportBuilder
                ->setTemplateIdentifier($emailTemplate)
                ->setTemplateOptions(
                    [
                        'area' => Area::AREA_FRONTEND,
                        'store' => $storeId,
                    ]
                )
                ->setTemplateVars($templateVars)
                ->setFromByScope($sender, $storeId);

            $customer = $this->customerRepository->getById($seller->getSellerId());
            $transport->addTo($customer->getEmail());

            $transport = $transport->getTransport();
            $transport->sendMessage();
            $this->inlineTranslation->resume();
        } catch (\Exception $e) {
            $this->inlineTranslation->resume();
            $this->logger->warning(
                '[ComaveSellerImport] Unable to send import results email',
                [
                    'customer' => $customer->getEmail(),
                    'errorMessage' => $e->getMessage(),
                    'trace' => $e->getTrace()
                ]
            );

            throw $e;
        }
    }
}
