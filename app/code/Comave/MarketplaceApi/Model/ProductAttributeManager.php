<?php
declare(strict_types=1);

namespace Comave\MarketplaceApi\Model;

use Comave\Marketplace\Plugin\RestrictAddAttributeOption;
use Magento\Catalog\Model\Product\Attribute\Repository;
use Comave\MarketplaceApi\Model\TokenManager;
use Comave\MarketplaceApi\Api\ProductAttributeManagerInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Comave\Marketplace\Service\SellerAttributePermission;

class ProductAttributeManager implements ProductAttributeManagerInterface
{
    private array $returnKeys = [
        'attribute_code',
        'attribute_id',
        'frontend_input',
        'frontend_label',
        'default_value',
        'options'
    ];

    /**
     * @param Repository $attributeRepository
     * @param TokenManager $tokenManager
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        private readonly Repository $attributeRepository,
        private readonly TokenManager $tokenManager,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getAttribute(string $attributeCode): array
    {
        if ($response = $this->validateSellerAccess()) {
            return $response;
        }
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(SellerAttributePermission::ATTRIBUTE_IS_SELLER_EDITABLE, 1)
            ->addFilter('attribute_code', $attributeCode)
            ->create();

        $response['status'] = __("Success: Attribute found.");
        $attributeOptions = $this->attributeRepository->getList($searchCriteria);
        if ($attributeOptions->getTotalCount() === 0) {
            $response['status'] = __("Error: Attribute not found or not editable by seller.");
            return [$response];
        } else {
            $response = $this->stripResponse($attributeOptions->getItems()[0]);
        }

        return [$response];
    }

    /**
     * @inheritDoc
     */
    public function getAllAttributes(): array
    {
        if ($response = $this->validateSellerAccess()) {
            return $response;
        }

        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(SellerAttributePermission::ATTRIBUTE_IS_SELLER_EDITABLE, 1)
            ->create();

        $attributeList = $this->attributeRepository->getList($searchCriteria);
        $attributes = $attributeList->getItems();
        $result = [];
        foreach ($attributes as $attribute) {
            $result[] = $this->stripResponse($attribute);
        }

        return $result;
    }

    /**
     * @param \Magento\Catalog\Api\Data\ProductAttributeInterface $data
     * @return array
     */
    private function stripResponse($data): array
    {
        $response = [];
        foreach ($this->returnKeys as $key) {
            if(isset($data[$key])) {
                $response[$key] = $data->getData($key);
            }
            if($key == "options"){
                foreach($data->getOptions() as $option){
                    $response[$key][] = $option->getData();
                }
            }
        }

        return $response;
    }

    private function validateSellerAccess(): ?array
    {
        $token = $this->tokenManager->extractTokenFromHeader();
        $sellerId = $this->tokenManager->validateAndRetrieveSellerId($token);
        if (!$sellerId) {
            $response['status'] = __("Error: Invalid Seller ID");
            return $response;
        }

        return null;
    }
}
