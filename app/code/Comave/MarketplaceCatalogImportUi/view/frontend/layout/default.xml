<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="layout2_seller_account_navigation">
            <block class="Webkul\Marketplace\Block\Account\Navigation"
                   name="marketplace.product.upload"
                   after="sellercoupon_account_navigation"
                   template="Comave_MarketplaceCatalogImportUi::account/layout2/navigation.phtml"/>
        </referenceContainer>
    </body>
</page>
