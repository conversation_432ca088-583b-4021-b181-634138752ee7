<?xml version="1.0" encoding="UTF-8"?>
<listing
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">seller_import_process.seller_import_process_data_source</item>
        </item>
    </argument>
    <settings>
        <deps>
            <dep>seller_import_process.seller_import_process_data_source</dep>
        </deps>
        <spinner>seller_import_process_columns</spinner>
    </settings>
    <dataSource name="seller_import_process_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
            <updateUrl path="marketplace/mui_index/render"/>
        </settings>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="seller_import_process_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="seller_import_process_columns">
        <column name="id">
            <settings>
                <sortable>false</sortable>
                <label translate="true">Import ID</label>
            </settings>
        </column>
        <column name="errors" sortOrder="30" class="Comave\MarketplaceCatalogImportUi\Ui\Import\Column\ErrorContext">
            <settings>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
                <filter>false</filter>
                <label translate="true">Error Context</label>
            </settings>
        </column>
        <column name="started_at" sortOrder="20" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Process started at</label>
                 <timezone>true</timezone>
                <dateFormat>MMM d, y HH:mm:ss</dateFormat>
                <editor>
                    <editorType>date</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
        <column name="processed_at" sortOrder="30" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Process finished at</label>
                 <timezone>true</timezone>
                <dateFormat>MMM d, y HH:mm:ss</dateFormat>
                <editor>
                    <editorType>date</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
        <column name="created_at" sortOrder="40" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created at</label>
                 <timezone>true</timezone>
                <dateFormat>MMM d, y HH:mm:ss</dateFormat>
                <editor>
                    <editorType>date</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
    </columns>
</listing>
