<?php

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Comave\MarketplaceCatalogImportUi\ViewModel\ImportedRecords $viewModel */
/** @var \Magento\Framework\Escaper $escaper */
$viewModel = $block->getData('rowsViewModel');
/** @var array[][] $rows */
$rows = $block->getData('rows') ?: $viewModel->getImportedRecords();
$rowsWithErrors = $block->getData('rowsWithErrors') ?: [];
$rowNr = 1;
?>
<div style="margin: 30px auto; font-family: 'Segoe UI', sans-serif; font-size: 14px;">
    <table style="width: 100%; border-collapse: collapse; box-shadow: 0 2px 6px rgba(0,0,0,0.1); border: 1px solid #ccc;">
        <thead>
            <?php $headerRow = $rows[0]; ?>
            <tr style="background-color: #f1f1f1; text-align: left;">
                <th style="padding: 12px 10px; border-right: 1px solid #ddd; font-weight: 600;">
                    <?= /** @noEscape */__('Row number'); ?>
                </th>
                <?php foreach ($headerRow as $data): ?>
                    <th style="padding: 12px 10px; border-right: 1px solid #ddd; font-weight: 600;">
                        <?= $escaper->escapeHtml($data); ?>
                    </th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php array_shift($rows); ?>
            <?php foreach ($rows as $recordRow): ?>
                <tr style="background-color: <?= in_array($rowNr, $rowsWithErrors) ? '#ffe6e6' : '#e6ffed' ?>;">
                    <td style="padding: 10px; border-top: 1px solid #eee;">
                        <?= $escaper->escapeHtml($rowNr); ?>
                    </td>
                    <?php foreach ($recordRow as $data): ?>
                        <td style="padding: 10px; border-top: 1px solid #eee;">
                            <?= $escaper->escapeHtml($data); ?>
                        </td>
                    <?php endforeach; ?>
                    <?php $rowNr++; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
