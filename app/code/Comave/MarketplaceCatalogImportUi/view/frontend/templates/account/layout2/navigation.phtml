<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
$magentoCurrentUrl = $block->getUrl();
$downloadUrl = $block->getUrl(
    'marketplace/catalog_import_template/download',
    ['_secure' => $block->getRequest()->isSecure()]
);
$importUrl = $block->getUrl(
    'marketplace/catalog_import/products',
    ['_secure' => $block->getRequest()->isSecure()]
);
$downloadCategoryFiles = $block->getUrl(
    'marketplace/catalog_category/download',
    ['_secure' => $block->getRequest()->isSecure()]
);
$generateCategoryFile = $block->getUrl(
    'marketplace/catalog_category/generate',
    ['_secure' => $block->getRequest()->isSecure()]
);
$isActive = str_contains($magentoCurrentUrl, 'catalog_import');
?>
<li id="wk-mp-menu-catalog-import" class="wk-mp-item-catalog-import level-0 <?= $isActive ? "current": "";?>">
    <a href="#" onclick="javascript:void(0);">
        <span><?= $escaper->escapeHtml(__('Catalog Import Settings')); ?></span>
    </a>
    <div class="wk-mp-submenu">
        <strong class="wk-mp-submenu-title"><?= /* @noEscape */ __('Settings')?></strong>
        <a href="#" class="action-close _close" data-role="wk-mp-close-submenu"></a>
        <ul>
            <li class="item-menu parent level-1">
                <strong class="wk-mp-submenu-group-title">
                    <?= /** @noEscape */__('Product Settings'); ?>
                </strong>
                <div class="wk-mp-submenu">
                    <ul style="text-align: left;">
                        <li class="level-2">
                            <a href="<?= $escaper->escapeUrl($downloadUrl); ?>"><?= __('Download import template'); ?></a>
                        </li>
                    </ul>
                    <ul style="text-align: left;">
                        <li class="level-2">
                            <a href="<?= $escaper->escapeUrl($importUrl); ?>"><?= __('Import products'); ?></a>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
        <ul>
            <li class="item-menu parent level-1">
                <strong class="wk-mp-submenu-group-title">
                    <?= /** @noEscape */__('Category Settings'); ?>
                </strong>
                <div class="wk-mp-submenu">
                    <ul>
                        <li class="level-2">
                            <a href="<?= $escaper->escapeUrl($generateCategoryFile); ?>"><?= __('Generate category list file'); ?></a>
                        </li>
                        <li class="level-2">
                            <a href="<?= $escaper->escapeUrl($downloadCategoryFiles); ?>"><?= __('Download category file'); ?></a>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
    </div>
</li>
