<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Plugin;

use Magento\ImportExport\Ui\DataProvider\ExportFileDataProvider;

class FilterCategoryFiles
{
    /**
     * @param ExportFileDataProvider $fileDataProvider
     * @param array $fileData
     * @return array
     */
    public function afterGetData(
        ExportFileDataProvider $fileDataProvider,
        array $fileData
    ): array {
        if (empty($fileData['items'])) {
            return $fileData;
        }

        $attributes = [
            'entity_id',
            'url_key',
            'name',
            'category_code'
        ];
        $hashed = hash('sha256', serialize($attributes));
        $filteredItems = array_filter(
            $fileData['items'],
            fn ($item) => str_contains($item['file_name'], $hashed)
        );

        return [
            'items' => $filteredItems,
            'totalRecords' => count($filteredItems)
        ];
    }
}
