<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Plugin;

use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\Api\Search\SearchResultInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Data\Collection;
use Magento\Framework\View\Element\UiComponent\DataProvider\Reporting;

class FilterBySeller
{
    /**
     * @param CurrentCustomer $currentCustomer
     */
    public function __construct(private readonly CurrentCustomer $currentCustomer)
    {
    }

    /**
     * @param Reporting $reporting
     * @param SearchResultInterface $searchResult
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function afterSearch(
        Reporting $reporting,
        SearchResultInterface $searchResult,
        SearchCriteriaInterface $searchCriteria
    ): SearchResultInterface {
        if ($searchCriteria->getRequestName() !== 'seller_import_process_data_source') {
            return $searchResult;
        }

        /** @var Collection $searchResult */
        $searchResult->addFieldToFilter(
            ImportEntityInterface::SELLER_ID,
            $this->currentCustomer->getCustomerId()
        );

        return $searchResult;
    }
}
