<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Ui\Import\Column;

use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;
use Magento\Ui\Component\Listing\Columns\Column;

class ErrorContext extends Column
{
    /**
     * Prepare Data Source.
     *
     * @param array $dataSource
     *
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (!isset($dataSource['data']['items'])) {
            return $dataSource;
        }

        $fieldName = $this->getData('name');

        foreach ($dataSource['data']['items'] as &$item) {
            if (!isset($item[$fieldName])) {
                continue;
            }

            $errors = json_decode(
                $item[$fieldName],
                true
            ) ?? [];

            if (empty($errors) && !empty($item[ImportEntityInterface::PROCESSED_AT])) {
                $item[$fieldName] = 'No errors encountered';
                continue;
            }

            $item[$fieldName] = '';

            foreach ($errors as $rowNr => $error) {
                $item[$fieldName] .= sprintf(
                    '<span>Error on row %s: %s</span><br/>',
                    $rowNr,
                    $error
                );
            }
        }

        return $dataSource;
    }
}
