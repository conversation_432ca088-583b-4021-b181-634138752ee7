<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Exception;

use Magento\Framework\Exception\LocalizedException;

class InvalidRowException extends LocalizedException
{
    private ?int $rowNr = null;

    /**
     * @param int $rowNr
     * @return void
     */
    public function setRowNr(int $rowNr): void
    {
        $this->rowNr = $rowNr;
    }

    /**
     * @return int|null
     */
    public function getRowNr(): ?int
    {
        return $this->rowNr;
    }
}
