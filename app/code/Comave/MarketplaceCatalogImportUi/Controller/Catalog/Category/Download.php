<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Controller\Catalog\Category;

use Magento\Customer\Controller\AccountInterface;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\View\Result\Page;
use Magento\ImportExport\Model\LocalizedFileName;

class Download extends Action implements AccountInterface, HttpGetActionInterface
{
    /**
     * @param Context $context
     * @param FileFactory $fileFactory
     * @param Filesystem $filesystem
     * @param LocalizedFileName $localizedFileName
     */
    public function __construct(
        Context $context,
        private readonly FileFactory $fileFactory,
        private readonly Filesystem $filesystem,
        private readonly LocalizedFileName $localizedFileName,
    ) {
        parent::__construct($context);
    }

    /**
     * @return Page|ResponseInterface
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function execute(): Page|ResponseInterface
    {
        if ($this->getRequest()->getParam('filename')) {
            $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setRefererUrl();
            $fileName = $this->getRequest()->getParam('filename');
            $exportDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_IMPORT_EXPORT);

            try {
                $fileName  = $exportDirectory->getDriver()->getRealPathSafety(DIRECTORY_SEPARATOR . $fileName);
                $fileExist = $exportDirectory->isExist('export' . $fileName);
            } catch (\Throwable) {
                $fileExist = false;
            }

            if (empty($fileName) || !$fileExist) {
                $this->messageManager->addErrorMessage(__('Please provide valid export file name'));

                return $resultRedirect;
            }

            try {
                $path = 'export' . $fileName;
                $directory = $this->filesystem->getDirectoryRead(DirectoryList::VAR_IMPORT_EXPORT);
                if ($directory->isFile($path)) {
                    return $this->fileFactory->create(
                        $this->localizedFileName->getFileDisplayName($path),
                        ['type' => 'filename', 'value' => $path],
                        DirectoryList::VAR_IMPORT_EXPORT
                    );
                }
                $this->messageManager->addErrorMessage(__('%1 is not a valid file', $fileName));
            } catch (\Exception $exception) {
                $this->messageManager->addErrorMessage($exception->getMessage());
            }

            return $resultRedirect;
        }

        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $resultPage->getConfig()
            ->getTitle()
            ->set((string) __('Download category file'));

        return $resultPage;
    }
}
