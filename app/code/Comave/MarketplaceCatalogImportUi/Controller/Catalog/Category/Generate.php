<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Controller\Catalog\Category;

use Comave\MarketplaceCatalogImportUi\Model\Queue\Consumer\GenerateCategoryFile;
use Magento\Customer\Controller\AccountInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\MessageQueue\PublisherInterface;

class Generate implements HttpGetActionInterface, AccountInterface
{
    /**
     * @param PublisherInterface $publisher
     * @param ResultFactory $resultFactory
     * @param CurrentCustomer $currentCustomer
     * @param ManagerInterface $messageManager
     */
    public function __construct(
        private readonly PublisherInterface $publisher,
        private readonly ResultFactory $resultFactory,
        private readonly CurrentCustomer $currentCustomer,
        private readonly ManagerInterface $messageManager,
    ) {
    }

    /**
     * @return Redirect
     */
    public function execute(): Redirect
    {
        $this->publisher->publish(
            GenerateCategoryFile::TOPIC_NAME,
            (int) $this->currentCustomer->getCustomerId()
        );
        $this->messageManager->addSuccessMessage(
            __('Your file will be available shortly')
        );

        return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setRefererUrl();
    }
}
