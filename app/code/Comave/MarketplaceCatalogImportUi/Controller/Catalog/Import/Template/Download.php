<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Controller\Catalog\Import\Template;

use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\ExportEntityRepositoryInterface;
use Comave\MarketplaceCatalogImport\Model\Command\TemplateDownloader;
use Magento\Customer\Controller\AccountInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\SellerFactory;

class Download extends Action implements HttpGetActionInterface, AccountInterface
{
    /**
     * @param Context $context
     * @param TemplateDownloader $templateDownloader
     * @param CurrentCustomer $currentCustomer
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param ExportEntityRepositoryInterface $exportEntityRepository
     * @param Seller $resourceModel
     * @param SellerFactory $sellerFactory
     */
    public function __construct(
        Context $context,
        private readonly TemplateDownloader $templateDownloader,
        private readonly CurrentCustomer $currentCustomer,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly ExportEntityRepositoryInterface $exportEntityRepository,
        private readonly Seller $resourceModel,
        private readonly SellerFactory $sellerFactory,
    ) {
        parent::__construct($context);
    }

    /**
     * @return Redirect|ResponseInterface
     * @throws \Exception
     */
    public function execute(): Redirect|ResponseInterface
    {
        $seller = $this->sellerFactory->create();
        $this->resourceModel->load(
            $seller,
            $this->currentCustomer->getCustomerId(),
            'seller_id'
        );

        if (!$seller->getId()) {
            $this->messageManager->addErrorMessage(
                __('Unable to locate seller.')
            );
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setRefererUrl();
        }

        if (empty($seller->getCountryPic())) {
            $this->messageManager->addErrorMessage(
                __('Unable to locate your country, please review your settings.')
            );
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setRefererUrl();
        }

        $this->searchCriteriaBuilder->addFilter(
            ExportEntityInterface::COUNTRY_ID,
            $seller->getCountryPic(),
            'finset'
        );
        $exportTemplateList = $this->exportEntityRepository->getList(
            $this->searchCriteriaBuilder->create()
        );

        if (!$exportTemplateList->getTotalCount()) {
            $this->messageManager->addErrorMessage(
                __('No export template found for %1.', $seller->getCountryPic())
            );
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setRefererUrl();
        }

        $exportEntity = current($exportTemplateList->getItems());

        return $this->templateDownloader->execute($exportEntity);
    }
}
