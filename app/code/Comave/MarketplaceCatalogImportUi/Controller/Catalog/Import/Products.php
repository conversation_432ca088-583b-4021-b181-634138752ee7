<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Controller\Catalog\Import;

use Magento\Customer\Controller\AccountInterface;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\Page;

class Products extends Action implements HttpGetActionInterface, AccountInterface
{
    /**
     * @return Page
     */
    public function execute(): Page
    {
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $resultPage->getConfig()->getTitle()->set(
            __('Import your products')
        );

        return $resultPage;
    }
}
