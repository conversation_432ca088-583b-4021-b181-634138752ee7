<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Controller\Catalog\Import;

use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\ExportEntityRepositoryInterface;
use Comave\MarketplaceCatalogImport\Model\Command\ImportProcess;
use Comave\MarketplaceCatalogImportUi\Model\Queue\Consumer\StartImportProcess;
use Magento\Customer\Controller\AccountInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\MessageQueue\PublisherInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\SellerFactory;

class Process implements HttpGetActionInterface, AccountInterface
{
    /**
     * @param PublisherInterface $publisher
     * @param DataPersistorInterface $dataPersistor
     * @param ManagerInterface $messageManager
     * @param CurrentCustomer $currentCustomer
     * @param ResultFactory $resultFactory
     * @param ExportEntityRepositoryInterface $exportEntityRepository
     * @param Seller $sellerResource
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param SellerFactory $sellerFactory
     * @param ImportProcess $importProcess
     */
    public function __construct(
        private readonly PublisherInterface $publisher,
        private readonly DataPersistorInterface $dataPersistor,
        private readonly ManagerInterface $messageManager,
        private readonly CurrentCustomer $currentCustomer,
        private readonly ResultFactory $resultFactory,
        private readonly ExportEntityRepositoryInterface $exportEntityRepository,
        private readonly Seller $sellerResource,
        private readonly SearchCriteriaBuilder  $searchCriteriaBuilder,
        private readonly SellerFactory $sellerFactory,
        private readonly ImportProcess $importProcess,
    ) {
    }

    /**
     * @return Redirect
     */
    public function execute(): Redirect
    {
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        if (empty($this->dataPersistor->get($this->getImportedKey()))) {
            $this->messageManager->addWarningMessage(
                __('Unable to detect products, please review the import file')
            );

            return $resultRedirect->setRefererUrl();
        }

        $seller = $this->sellerFactory->create();
        $this->sellerResource->load($seller, $this->currentCustomer->getCustomerId(), 'seller_id');

        if (empty($seller->getId())) {
            $this->messageManager->addErrorMessage(
                __('There was a problem performing the request, please contact us')
            );

            return $resultRedirect->setRefererUrl();
        }

        $this->searchCriteriaBuilder->addFilter(
            ExportEntityInterface::COUNTRY_ID,
            $seller->getCountryPic(),
            'finset'
        );

        $exportTemplateList = $this->exportEntityRepository->getList(
            $this->searchCriteriaBuilder->create()
        );

        if (!$exportTemplateList->getTotalCount()) {
            $this->messageManager->addErrorMessage(
                __('There was a problem performing the request, please contact us')
            );

            return $resultRedirect->setRefererUrl();
        }

        $importId = $this->importProcess->execute(
            $seller,
            current($exportTemplateList->getItems()),
            $this->dataPersistor->get($this->getImportedKey())
        );
        $this->publisher->publish(
            StartImportProcess::TOPIC_NAME,
            $importId
        );
        $this->dataPersistor->clear($this->getImportedKey());
        $this->messageManager->addSuccessMessage(
            __('Processing your products in a few moments, you will be notified once the process finishes')
        );

        return $resultRedirect->setRefererUrl();
    }

    /**
     * @return string
     */
    private function getImportedKey(): string
    {
        return sprintf('imported_products_%s', $this->currentCustomer->getCustomerId());
    }
}
