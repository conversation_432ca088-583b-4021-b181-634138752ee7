<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Model\Queue\Consumer;

use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;
use Opengento\CategoryImportExport\Model\Csv\Options;
use Opengento\CategoryImportExport\Model\Export\ToCsv;
use Psr\Log\LoggerInterface;

class GenerateCategoryFile
{
    public const string TOPIC_NAME = 'comave.category.export';

    /**
     * @param LoggerInterface $logger
     * @param ToCsv $toCsv
     * @param StoreManagerInterface $storeManager
     * @param RequestInterface $request
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ToCsv $toCsv,
        private readonly StoreManagerInterface $storeManager,
        private readonly RequestInterface $request,
    ) {
    }

    /**
     * @param int $generate
     * @return void
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function execute(int $generate): void
    {
        $this->logger->info(
            '[ComaveExportCategories] Beginning export',
        );

        $start = microtime(true);
        $this->request->setParams([
            'delimiter' => ',',
            'enclosure' => '"'
        ]);

        $this->toCsv->execute(
            [$this->storeManager->getDefaultStoreView()->getId()],
            [
                'entity_id',
                'url_key',
                'name',
                'category_code'
            ],
            Options::createFromRequest($this->request)
        );

        $end = microtime(true);
        $this->logger->info(
            '[ComaveExportCategories] Finished export',
            [
                'elapsedTime' => $end - $start,
            ]
        );
    }
}
