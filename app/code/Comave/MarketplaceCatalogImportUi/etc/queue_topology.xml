<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/topology.xsd">
    <exchange name="comave.category.export.exchange" type="topic" connection="amqp">
        <binding id="processCategoryExportBinding"
                 topic="comave.category.export"
                 destinationType="queue"
                 destination="comave.category.export"/>
    </exchange>
    <exchange name="comave.product.import.exchange" type="topic" connection="amqp">
        <binding id="processProductImportBinding"
                 topic="comave.product.import"
                 destinationType="queue"
                 destination="comave.product.import"/>
    </exchange>
</config>
