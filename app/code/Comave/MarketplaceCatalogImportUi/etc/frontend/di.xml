<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\ImportExport\Ui\DataProvider\ExportFileDataProvider">
        <plugin name="filterOnlyCategoryFiles" type="Comave\MarketplaceCatalogImportUi\Plugin\FilterCategoryFiles"/>
    </type>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="seller_import_process_data_source" xsi:type="string">ImportEntityDataSource</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="ImportEntityDataSource" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="const">\Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface::TABLE_NAME</argument>
            <argument name="resourceModel" xsi:type="string">Comave\MarketplaceCatalogImport\Model\ResourceModel\ImportEntity</argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\Reporting">
        <plugin name="filterBySeller" type="Comave\MarketplaceCatalogImportUi\Plugin\FilterBySeller"/>
    </type>
</config>
