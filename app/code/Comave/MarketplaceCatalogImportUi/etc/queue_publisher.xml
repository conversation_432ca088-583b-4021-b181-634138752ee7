<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <publisher topic="comave.category.export">
        <connection name="amqp" exchange="comave.category.export.exchange" />
    </publisher>
    <publisher topic="comave.product.import">
        <connection name="amqp" exchange="comave.product.import.exchange" />
    </publisher>
</config>
