<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\ImportExport\Ui\DataProvider\ExportFileDataProvider">
        <arguments>
            <argument name="file" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>

    <type name="Comave\MarketplaceCatalogImportUi\Model\Queue\Consumer\StartImportProcess">
        <arguments>
            <argument xsi:type="array" name="productDataHandlers">
                <item xsi:type="object" name="configurable">Comave\MarketplaceCatalogImport\Model\DataHandler\Configurable</item>
                <item xsi:type="object" name="stock">Comave\MarketplaceCatalogImport\Model\DataHandler\Stock</item>
                <item xsi:type="object" name="media">Comave\MarketplaceCatalogImport\Model\DataHandler\Media</item>
                <item xsi:type="object" name="category">Comave\MarketplaceCatalogImport\Model\DataHandler\Category</item>
            </argument>
        </arguments>
    </type>
</config>
