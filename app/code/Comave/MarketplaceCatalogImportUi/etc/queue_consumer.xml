<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="comave.category.export"
              queue="comave.category.export"
              connection="amqp"
              maxMessages="100"
              handler="Comave\MarketplaceCatalogImportUi\Model\Queue\Consumer\GenerateCategoryFile::execute"/>
    <consumer name="comave.product.import"
              queue="comave.product.import"
              connection="amqp"
              maxMessages="100"
              handler="Comave\MarketplaceCatalogImportUi\Model\Queue\Consumer\StartImportProcess::execute"/>
</config>
