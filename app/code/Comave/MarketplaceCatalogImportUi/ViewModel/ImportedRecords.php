<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;

class ImportedRecords implements ArgumentInterface
{
    /**
     * @return array[]
     */
    public function getImportedRecords(): array
    {
        return [
            [
                'a' => 'b',
                'c' => 'd'
            ]
        ];
    }
}
