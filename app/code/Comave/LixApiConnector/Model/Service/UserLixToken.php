<?php
declare(strict_types=1);


namespace Comave\LixApiConnector\Model\Service;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * @deprecated
 * @obsolete
 */
class UserLixToken
{
    public const string LIX_TOKEN_ATTR_CODE = 'lix_token';

    private array|null $lixTokens = null;
    private ?string $lixToken = null;

    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepositoryInterface,
    ) {
    }

    /**
     * @obsolete
     * @deprecated
     */
    final public function getUserLixToken(int $customerId): ?string
    {
        return null;
    }
}
