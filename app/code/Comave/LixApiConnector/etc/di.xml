<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <type name="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="loggerPath" xsi:type="string">LixApiConnector</argument>
        </arguments>
    </type>

    <virtualType name="Comave\Logger\Model\LixApiConnector" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">LixApiConnector</argument>
            <argument name="loggerPath" xsi:type="string">LixApiConnector</argument>
        </arguments>
    </virtualType>
</config>
