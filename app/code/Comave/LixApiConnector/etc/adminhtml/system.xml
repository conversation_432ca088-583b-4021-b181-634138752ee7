<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
   <system>
        <tab id="lix" translate="label" sortOrder="400">
            <label>Lix Api Connect</label>
        </tab>
        <section id="customsection" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>LIX Api</label>
            <tab>lix</tab>
            <resource>Lix_Customsection::configuration</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Lix Configuration</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Module</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="api_cache_enabled" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable API Cache Layer</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable the API Cache Layer</comment>
                </field>
                <field id="api_cache_lifetime" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>API Cache Layer Lifetime</label>
                    <comment>Sets the API Cache Layer Lifetime in seconds</comment>
                </field>
            </group>
        </section>
        <section id="sleekaccordian" translate="label" type="text" sortOrder="998" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Lix Api Connect</label>
            <!-- Assign section to tab -->
            <tab>lix</tab>
            <resource>Lix_Sleekaccordian::configuration</resource>
            <!-- create group for fields in section -->
            <group id="general" translate="label" type="text"  sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <!-- create text type field -->
                <field id="odata_base_url" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
                    <label>OData Base URL (Endpoint)</label>
                </field>
                <field id="site_url" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="text">
                    <label>Site Url (Endpoint)</label>
                    <comment/>
                </field>
                <field id="host" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="24" translate="label" type="text">
                    <label>Host (for mobile)</label>
                    <comment/>
                </field>
                 <field id="path" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="27" translate="label" type="text">
                    <label>Path (for mobile)</label>
                    <comment/>
                </field>
                <field id="port" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="text">
                    <label>Port</label>
                    <comment/>
                </field>
                <field id="availability" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label" type="select">
                    <label>Availability</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
                <field id="user" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70" translate="label" type="text">
                    <label>User Name / Client ID</label>
                    <comment/>
                </field>
                <field id="password" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="80" translate="label" type="obscure">
                    <label>Password / Client Secret</label>
                    <comment/>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <config_path>settings/general/password</config_path>
                </field>
                <field id="access_key" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="99" translate="label" type="text">
                    <label>web service access key</label>
                    <comment/>
                </field>
                <field id="organisation_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="101" translate="label" type="text">
                    <label>Organisation Id</label>
                    <comment/>
                </field>
                <field id="project_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="103" translate="label" type="text">
                    <label>Project Id</label>
                    <comment/>
                </field>
                <field id="qr_timer" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="103" translate="label" type="text">
                    <label>QR Timer</label>
                    <comment/>
                </field>
                <field id="cashpoint_task_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>CashPoint Task Id</label>
                    <comment/>
                </field>
                <field id="every_cash_task_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>Every CashPoint Task Id</label>
                    <comment/>
                </field>
                  <field id="lix_task_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>LIX Task Id</label>
                    <comment/>
                </field>
                <field id="every_lix_task_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>Every LIX Task Id</label>
                    <comment/>
                </field>
                <field id="news_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>Newsletter Cash Task Id</label>
                    <comment/>
                </field>
                <field id="news_lix_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>Newsletter LIX Task Id</label>
                    <comment/>
                </field>
                <field id="reg_scratch_id_cash" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>Cash Point Registration Scratch Id</label>
                    <comment/>
                </field>
                <field id="reg_scratch_title_cash" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>Cash Point Registration Scratch Task Title</label>
                    <comment/>
                </field>
                <field id="reg_scratch_id_lixx" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>LIXX Registration Scratch Id</label>
                    <comment/>
                </field>
                <field id="reg_scratch_title_lixx" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>LIXX Registration Scratch Task Title</label>
                    <comment/>
                </field>
                <field id="treasure_hunt_id_cash" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>Cash Point Treasure Hunt Task Id</label>
                    <comment/>
                </field>
                <field id="treasure_hunt_id_lixx" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="105" translate="label" type="text">
                    <label>LIXX Treasure Hunt Task Id</label>
                    <comment/>
                </field>
                <field id="activity_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="107" translate="label" type="text">
                    <label>Activity Id</label>
                    <comment/>
                </field>
                <field id="reward_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="115" translate="label" type="text">
                    <label>Reward Id</label>
                    <comment/>
                </field>
                <field id="reward_currency" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="116" translate="label" type="text">
                    <label>Reward Currency</label>
                    <!-- <source_model>Comave\LixApiConnector\Model\Config\RewardCurrency</source_model> -->
                </field>
                <field id="currency_organisation_id" showInDefault="1" showInStore="109" showInWebsite="1" sortOrder="106" translate="label" type="text">
                    <label>Currency Organisation Id</label>
                    <comment/>
                </field>
                 <field id="order_item_reward" showInDefault="1" showInStore="111" showInWebsite="1" sortOrder="106" translate="label" type="text">
                    <label>Item Reward</label>
                    <comment/>
                </field>
                <field id="order_item_reward_second" showInDefault="1" showInStore="111" showInWebsite="1" sortOrder="106" translate="label" type="text">
                    <label>Item Reward Second</label>
                    <comment/>
                </field>
                 <field id="order_reward_type" showInDefault="1" showInStore="113" showInWebsite="1" sortOrder="106" translate="label" type="text">
                    <label>Reward Type</label>
                    <comment/>
                </field>
                <field id="order_item_cost" showInDefault="1" showInStore="114" showInWebsite="1" sortOrder="106" translate="label" type="text">
                    <label>Item Cost</label>
                    <comment/>
                </field>
                <field id="service_charge" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="100" translate="label" type="text">
                    <label>Lix Service Charge</label>
                </field>
            </group>
        </section>
<!-- TODO: to be removed -->
        <section id="lix_general" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
            <label>Lix Conversion Rate</label>
            <tab>lix</tab>
            <resource>Lix_Lixgeneral::configuration</resource>
            <group id="general" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>General</label>
            </group>
            <group id="cashpoint" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>LIXPP Cash Point Conversion Rate</label>
                        <field id="usd_id" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Cash Point Custom Currency Organisation Id</label>
                            <comment/>
                        </field>

                        <field id="lixpp_wallet_name" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Wallet Name</label>
                            <comment/>
                        </field>

                        <field id="lixpp_custom_currency_organisation_id" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Cash Point Wallet Id</label>
                            <comment/>
                        </field>

                        <field id="direction" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Direction</label>
                            <comment/>
                        </field>

                        <field id="cash_point" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Points</label>
                            <comment/>
                        </field>
                          <field id="cash_currency" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Currency</label>
                            <comment/>
                        </field>
            </group>
            <!-- <group id="cashpointcurrency" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Cash Point Conversion Rate</label>
                        <field id="directioncash" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Direction</label>
                            <comment/>
                        </field>

                        <field id="currency_cash" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Currency</label>
                            <comment/>
                        </field>
                          <field id="point_cash" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Point</label>
                            <comment/>
                        </field>
            </group> -->

            <!-- <group id="lixpoint" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>LIX Point Conversion Rate</label>
                        <field id="lix_direction" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Direction</label>
                            <comment/>
                        </field>

                        <field id="lix_point" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Currency</label>
                            <comment/>
                        </field>
                          <field id="lix_currency" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Point</label>
                            <comment/>
                        </field>
            </group>
             <group id="lixcurrencypoint" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>LIX Point Conversion Rate</label>
                        <field id="lix_id" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>LIX Custom Currency Organisation Id</label>
                            <comment/>
                        </field>

                        <field id="lix_wallet_name" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Wallet Name</label>
                            <comment/>
                        </field>

                        <field id="lix_custom_currency_organisation_id" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>LIX Wallet Id</label>
                            <comment/>
                        </field>

                        <field id="direction_lix" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Direction</label>
                            <comment/>
                        </field>

                        <field id="point_lix" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Point</label>
                            <comment/>
                        </field>
                          <field id="currency_lix" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Currency</label>
                            <comment/>
                        </field>
            </group>
            <group id="lixxcurrencypoint" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>LIXX Point Conversion Rate</label>
                        <field id="lixx_id" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>LIXX Custom Currency Organisation Id</label>
                            <comment/>
                        </field>
                        <field id="lixx_wallet_name" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Wallet Name</label>
                            <comment/>
                        </field>

                        <field id="lixx_custom_currency_organisation_id" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>LIXX Wallet Id</label>
                            <comment/>
                        </field>

                        <field id="direction_lixx" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Direction</label>
                            <comment/>
                        </field>

                        <field id="point_lixx" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Point</label>
                            <comment/>
                        </field>
                          <field id="currency_lixx" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                            <label>Currency</label>
                            <comment/>
                        </field>
            </group>
             -->
        </section>


        <section id="customer_seller_general" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
            <label>Customer and Seller Profileness</label>
            <tab>lix</tab>
            <resource>Lix_Lixgeneral::configuration</resource>

            <group id="general" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>Extension Configuration</label>
                <field id="extension_enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Extension</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>

            <group id="seller_profileness" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>Seller Profileness</label>
                <field id="seller_attributes" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Attributes</label>
                    <source_model>Comave\LixApiConnector\Model\Config\Source\CustomerAttributes</source_model>
                    <depends>
                        <field id="extension_enabled">1</field>
                    </depends>
                </field>
            </group>

            <group id="customer_profileness" sortOrder="30" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>Customer Profileness</label>
                <field id="customer_attributes" translate="label" type="multiselect" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer Attributes</label>
                    <source_model>Comave\LixApiConnector\Model\Config\Source\CustomerAttributes</source_model>
                    <depends>
                        <field id="extension_enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
<!--
        <section id="monterosa" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Monterosa</label>
            <tab>lix</tab>
            <resource>Lix_Customsection::configuration</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Module</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>

            <group id="monterosaconfiguration" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Configuration</label>
                  <field id="odata_base_url" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
                    <label>OData Base URL (Endpoint)</label>
                </field>
                <field id="site_url" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="text">
                    <label>Site Url (Endpoint)</label>
                    <comment/>
                </field>
                <field id="port" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="text">
                    <label>Port</label>
                    <comment/>
                </field>
                <field id="availability" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label" type="select">
                    <label>Availability</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
                <field id="user" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70" translate="label" type="text">
                    <label>User Name / Client ID</label>
                    <comment/>
                </field>
                <field id="password" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="80" translate="label" type="obscure">
                    <label>Password / Client Secret</label>
                    <comment/>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <config_path>settings/general/password</config_path>
                </field>
                <field id="access_key" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="99" translate="label" type="text">
                    <label>web service access key</label>
                    <comment/>
                </field>
            </group>
        </section>
-->
        <section id="vatom" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Vatom</label>
            <tab>lix</tab>
            <resource>Lix_Customsection::configuration</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Module</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>

            <group id="vatomconfiguration" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Configuration</label>
                  <field id="odata_base_url" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
                    <label>OData Base URL (Endpoint)</label>
                </field>
                <field id="site_url" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="text">
                    <label>Site Url (Endpoint)</label>
                    <comment/>
                </field>
                <field id="port" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="text">
                    <label>Port</label>
                    <comment/>
                </field>
                <field id="availability" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label" type="select">
                    <label>Availability</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
                <field id="user" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70" translate="label" type="text">
                    <label>User Name / Client ID</label>
                    <comment/>
                </field>
                <field id="password" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="80" translate="label" type="obscure">
                    <label>Password / Client Secret</label>
                    <comment/>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <config_path>settings/general/password</config_path>
                </field>
                <field id="access_key" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="99" translate="label" type="text">
                    <label>web service access key</label>
                    <comment/>
                </field>
            </group>
        </section>
        <section id="comave_logger">
            <group id="lixapiconnector" translate="label" type="text" sortOrder="910" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>LixApiConnector</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Logging</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <depends>
                    <field id="comave_logger/general/enabled">1</field>
                </depends>
            </group>
        </section>
   </system>
</config>


