<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
<!--    <event name="customer_save_after">-->
<!--        <observer name="comave_lixapiconnector_observer_customer_save_after_observer" instance="Comave\LixApiConnector\Observer\CustomerSaveAfterObserver" />-->
<!--    </event>-->
<!--    <event name="review_save_after">-->
<!--        <observer name="comave_lixapiconnector_observer_review_approval_observer" instance="Comave\LixApiConnector\Observer\ReviewApprovalObserver" />-->
<!--    </event>-->
<!--    <event name="customer_register_success_popup">-->
<!--        <observer name="comave_lixapiconnector_customer_register_success_popup" instance="Comave\LixApiConnector\Observer\CustomerRegisterSuccess" />-->
<!--    </event>-->
<!--    <event name="customer_login">-->
<!--        <observer instance="Comave\LixApiConnector\Observer\CustomerLoginObserver" name="comave_lixapiconnector_observer_CustomerLoginObserver"/>-->
<!--    </event>-->
</config>
